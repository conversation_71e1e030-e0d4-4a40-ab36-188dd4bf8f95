
> @freela/admin-dashboard@1.0.0 build
> next build

   ▲ Next.js 14.0.3
   - Experiments (use at your own risk):
     · esmExternals

   Creating an optimized production build ...
<w> [webpack.cache.PackFileCacheStrategy] Caching failed for pack: Error: EPERM: operation not permitted, rename 'C:\Users\<USER>\Documents\Freela\apps\admin-dashboard\.next\cache\webpack\client-production\0.pack_' -> 'C:\Users\<USER>\Documents\Freela\apps\admin-dashboard\.next\cache\webpack\client-production\0.pack'
 ✓ Compiled successfully
   Linting and checking validity of types ...

 ⚠ The Next.js plugin was not detected in your ESLint configuration. See https://nextjs.org/docs/basic-features/eslint#migrating-existing-config

Failed to compile.

./src/app/dashboard/analytics/page.tsx
17:3  Error: 'BarChart' is defined but never used.  @typescript-eslint/no-unused-vars
18:3  Error: 'Bar' is defined but never used.  @typescript-eslint/no-unused-vars
263:42  Error: 'index' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/app/dashboard/bookings/page.tsx
10:3  Error: 'ClockIcon' is defined but never used.  @typescript-eslint/no-unused-vars
138:71  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
141:7  Warning: Unexpected console statement.  no-console

./src/app/dashboard/categories/page.tsx
109:21  Error: 'setIsLoading' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/app/dashboard/disputes/page.tsx
8:3  Error: 'XCircleIcon' is defined but never used.  @typescript-eslint/no-unused-vars
114:7  Error: 'statusLabels' is assigned a value but never used.  @typescript-eslint/no-unused-vars
157:21  Error: 'setIsLoading' is assigned a value but never used.  @typescript-eslint/no-unused-vars
160:10  Error: 'showDisputeModal' is assigned a value but never used.  @typescript-eslint/no-unused-vars
161:10  Error: 'selectedDispute' is assigned a value but never used.  @typescript-eslint/no-unused-vars
162:10  Error: 'formMode' is assigned a value but never used.  @typescript-eslint/no-unused-vars
186:9  Error: 'handleAssignDispute' is assigned a value but never used.  @typescript-eslint/no-unused-vars
323:68  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
342:70  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/dashboard/payments/page.tsx
144:9  Warning: Unexpected console statement.  no-console

./src/app/dashboard/reports/page.tsx
12:3  Error: 'EyeIcon' is defined but never used.  @typescript-eslint/no-unused-vars
123:21  Error: 'setIsLoading' is assigned a value but never used.  @typescript-eslint/no-unused-vars
150:5  Warning: Unexpected console statement.  no-console
270:66  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
288:71  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/dashboard/settings/page.tsx
109:7  Warning: Unexpected console statement.  no-console
116:80  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/dashboard/users/page.tsx
6:3  Error: 'FunnelIcon' is defined but never used.  @typescript-eslint/no-unused-vars
15:10  Error: 'EmptyState' is defined but never used.  @typescript-eslint/no-unused-vars
108:9  Warning: Unexpected console statement.  no-console
125:9  Warning: Unexpected console statement.  no-console
147:7  Warning: Unexpected console statement.  no-console

./src/app/error.tsx
13:5  Warning: Unexpected console statement.  no-console

./src/components/forms/UserForm.tsx
76:7  Warning: Unexpected console statement.  no-console
82:56  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/ui/ErrorBoundary.tsx
26:5  Warning: Unexpected console statement.  no-console

info  - Need to disable some ESLint rules? Learn more here: https://nextjs.org/docs/basic-features/eslint#disabling-rules
npm error Lifecycle script `build` failed with error:
npm error code 1
npm error path C:\Users\<USER>\Documents\Freela\apps\admin-dashboard
npm error workspace @freela/admin-dashboard@1.0.0
npm error location C:\Users\<USER>\Documents\Freela\apps\admin-dashboard
npm error command failed
npm error command C:\WINDOWS\system32\cmd.exe /d /s /c next build
