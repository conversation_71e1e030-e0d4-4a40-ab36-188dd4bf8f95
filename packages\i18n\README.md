# 🌍 @freela/i18n

> حزمة الترجمة والدعم الدولي لمنصة فريلا سوريا

## 📋 نظرة عامة

حزمة `@freela/i18n` توفر نظام ترجمة شامل ودعم دولي كامل لمنصة فريلا سوريا. تتضمن ترجمات عربية وإنجليزية مع دعم كامل للكتابة من اليمين لليسار (RTL) والتكيف الثقافي للسوق السوري.

## ✨ الميزات الرئيسية

### 🌐 دعم متعدد اللغات
- **العربية (ar)**: اللغة الأساسية مع دعم RTL كامل
- **الإنجليزية (en)**: لغة ثانوية للوصول الدولي
- **تبديل سلس**: تغيير اللغة بدون إعادة تحميل
- **حفظ التفضيلات**: تذكر اختيار المستخدم

### 📝 ترجمات شاملة
- **واجهة المستخدم**: جميع عناصر الواجهة مترجمة
- **رسائل النظام**: رسائل الخطأ والنجاح
- **المحتوى الديناميكي**: ترجمة المحتوى المتغير
- **التواريخ والأرقام**: تنسيق محلي للبيانات

### 🎨 التكيف الثقافي
- **المصطلحات السورية**: استخدام مصطلحات مألوفة
- **العملة المحلية**: دعم الليرة السورية والدولار
- **التقويم المحلي**: تواريخ بالتقويم الهجري والميلادي
- **الأرقام العربية**: عرض الأرقام بالأرقام العربية

### 🔧 أدوات التطوير
- **Type Safety**: أنواع TypeScript للترجمات
- **Hot Reload**: تحديث فوري للترجمات في التطوير
- **Missing Keys Detection**: اكتشاف المفاتيح المفقودة
- **Pluralization**: دعم صيغ الجمع العربية

## 🛠️ المكدس التقني

### Core
- **i18next**: مكتبة الترجمة الأساسية
- **react-i18next**: تكامل مع React
- **next-i18next**: تكامل مع Next.js
- **TypeScript**: دعم كامل للأنواع

### Features
- **Namespace Support**: تنظيم الترجمات في مساحات أسماء
- **Interpolation**: إدراج متغيرات في النصوص
- **Pluralization**: صيغ الجمع المعقدة
- **Context**: ترجمات حسب السياق

## 🚀 التثبيت والاستخدام

### التثبيت
```bash
# في مجلد المشروع الرئيسي
npm install

# أو تثبيت الحزمة مباشرة
npm install @freela/i18n
```

### الاستخدام في React
```typescript
import { useTranslation } from '@freela/i18n';

const MyComponent = () => {
  const { t, i18n } = useTranslation();
  
  return (
    <div>
      <h1>{t('common.welcome')}</h1>
      <p>{t('dashboard.overview')}</p>
      <button onClick={() => i18n.changeLanguage('en')}>
        {t('common.switchLanguage')}
      </button>
    </div>
  );
};
```

### الاستخدام في Next.js
```typescript
import { serverSideTranslations } from '@freela/i18n';

export const getServerSideProps = async ({ locale }) => {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common', 'dashboard'])),
    },
  };
};
```

## 📁 هيكل الحزمة

```
packages/i18n/
├── locales/               # ملفات الترجمة
│   ├── ar/               # الترجمات العربية
│   │   ├── common.json   # المصطلحات العامة
│   │   ├── auth.json     # المصادقة
│   │   ├── dashboard.json# لوحة التحكم
│   │   ├── services.json # الخدمات
│   │   ├── bookings.json # الحجوزات
│   │   ├── payments.json # المدفوعات
│   │   ├── chat.json     # المراسلة
│   │   └── errors.json   # رسائل الخطأ
│   └── en/               # الترجمات الإنجليزية
│       ├── common.json
│       ├── auth.json
│       ├── dashboard.json
│       ├── services.json
│       ├── bookings.json
│       ├── payments.json
│       ├── chat.json
│       └── errors.json
├── src/                  # الكود المصدري
│   ├── index.ts         # نقطة الدخول الرئيسية
│   ├── config.ts        # إعدادات i18n
│   ├── types.ts         # أنواع TypeScript
│   ├── hooks.ts         # React Hooks
│   └── utils.ts         # وظائف مساعدة
├── dist/                # الملفات المبنية
├── package.json         # إعدادات الحزمة
└── tsconfig.json        # إعدادات TypeScript
```

## 📝 ملفات الترجمة

### common.json (المصطلحات العامة)
```json
{
  "welcome": "مرحباً بك في فريلا سوريا",
  "save": "حفظ",
  "cancel": "إلغاء",
  "delete": "حذف",
  "edit": "تعديل",
  "loading": "جاري التحميل...",
  "error": "حدث خطأ",
  "success": "تم بنجاح",
  "confirm": "تأكيد",
  "back": "رجوع",
  "next": "التالي",
  "previous": "السابق",
  "search": "بحث",
  "filter": "تصفية",
  "sort": "ترتيب",
  "view": "عرض",
  "download": "تحميل",
  "upload": "رفع",
  "share": "مشاركة",
  "copy": "نسخ",
  "print": "طباعة",
  "export": "تصدير",
  "import": "استيراد",
  "refresh": "تحديث",
  "close": "إغلاق",
  "open": "فتح",
  "select": "اختيار",
  "selectAll": "اختيار الكل",
  "clear": "مسح",
  "reset": "إعادة تعيين",
  "apply": "تطبيق",
  "submit": "إرسال",
  "send": "إرسال",
  "receive": "استقبال",
  "accept": "قبول",
  "reject": "رفض",
  "approve": "موافقة",
  "decline": "رفض",
  "enable": "تفعيل",
  "disable": "تعطيل",
  "activate": "تنشيط",
  "deactivate": "إلغاء التنشيط",
  "show": "إظهار",
  "hide": "إخفاء",
  "expand": "توسيع",
  "collapse": "طي",
  "more": "المزيد",
  "less": "أقل",
  "all": "الكل",
  "none": "لا شيء",
  "yes": "نعم",
  "no": "لا",
  "ok": "موافق",
  "done": "تم",
  "pending": "معلق",
  "active": "نشط",
  "inactive": "غير نشط",
  "online": "متصل",
  "offline": "غير متصل",
  "available": "متاح",
  "unavailable": "غير متاح",
  "public": "عام",
  "private": "خاص",
  "draft": "مسودة",
  "published": "منشور",
  "archived": "مؤرشف",
  "deleted": "محذوف",
  "new": "جديد",
  "updated": "محدث",
  "created": "تم الإنشاء",
  "modified": "تم التعديل",
  "today": "اليوم",
  "yesterday": "أمس",
  "tomorrow": "غداً",
  "thisWeek": "هذا الأسبوع",
  "thisMonth": "هذا الشهر",
  "thisYear": "هذا العام",
  "lastWeek": "الأسبوع الماضي",
  "lastMonth": "الشهر الماضي",
  "lastYear": "العام الماضي",
  "nextWeek": "الأسبوع القادم",
  "nextMonth": "الشهر القادم",
  "nextYear": "العام القادم"
}
```

### auth.json (المصادقة)
```json
{
  "login": "تسجيل الدخول",
  "register": "إنشاء حساب",
  "logout": "تسجيل الخروج",
  "forgotPassword": "نسيت كلمة المرور؟",
  "resetPassword": "إعادة تعيين كلمة المرور",
  "changePassword": "تغيير كلمة المرور",
  "email": "البريد الإلكتروني",
  "password": "كلمة المرور",
  "confirmPassword": "تأكيد كلمة المرور",
  "firstName": "الاسم الأول",
  "lastName": "الاسم الأخير",
  "phone": "رقم الهاتف",
  "rememberMe": "تذكرني",
  "agreeToTerms": "أوافق على الشروط والأحكام",
  "alreadyHaveAccount": "لديك حساب بالفعل؟",
  "dontHaveAccount": "ليس لديك حساب؟",
  "verifyEmail": "تحقق من البريد الإلكتروني",
  "emailVerified": "تم التحقق من البريد الإلكتروني",
  "resendVerification": "إعادة إرسال رمز التحقق",
  "loginSuccess": "تم تسجيل الدخول بنجاح",
  "logoutSuccess": "تم تسجيل الخروج بنجاح",
  "registerSuccess": "تم إنشاء الحساب بنجاح",
  "passwordResetSent": "تم إرسال رابط إعادة تعيين كلمة المرور",
  "passwordChanged": "تم تغيير كلمة المرور بنجاح",
  "invalidCredentials": "بيانات الدخول غير صحيحة",
  "emailAlreadyExists": "البريد الإلكتروني مستخدم بالفعل",
  "weakPassword": "كلمة المرور ضعيفة",
  "passwordMismatch": "كلمات المرور غير متطابقة",
  "invalidEmail": "البريد الإلكتروني غير صحيح",
  "accountSuspended": "الحساب معلق",
  "accountNotVerified": "الحساب غير مفعل",
  "sessionExpired": "انتهت صلاحية الجلسة",
  "accessDenied": "تم رفض الوصول"
}
```

### dashboard.json (لوحة التحكم)
```json
{
  "overview": "نظرة عامة",
  "statistics": "الإحصائيات",
  "analytics": "التحليلات",
  "reports": "التقارير",
  "users": "المستخدمون",
  "experts": "الخبراء",
  "clients": "العملاء",
  "services": "الخدمات",
  "categories": "الفئات",
  "bookings": "الحجوزات",
  "payments": "المدفوعات",
  "earnings": "الأرباح",
  "messages": "الرسائل",
  "notifications": "الإشعارات",
  "settings": "الإعدادات",
  "profile": "الملف الشخصي",
  "account": "الحساب",
  "security": "الأمان",
  "privacy": "الخصوصية",
  "preferences": "التفضيلات",
  "help": "المساعدة",
  "support": "الدعم",
  "documentation": "الوثائق",
  "feedback": "التعليقات",
  "totalUsers": "إجمالي المستخدمين",
  "totalExperts": "إجمالي الخبراء",
  "totalClients": "إجمالي العملاء",
  "totalServices": "إجمالي الخدمات",
  "totalBookings": "إجمالي الحجوزات",
  "totalRevenue": "إجمالي الإيرادات",
  "activeUsers": "المستخدمون النشطون",
  "newUsers": "المستخدمون الجدد",
  "pendingBookings": "الحجوزات المعلقة",
  "completedBookings": "الحجوزات المكتملة",
  "monthlyRevenue": "الإيرادات الشهرية",
  "yearlyRevenue": "الإيرادات السنوية",
  "topExperts": "أفضل الخبراء",
  "topServices": "أفضل الخدمات",
  "recentActivity": "النشاط الأخير",
  "quickActions": "إجراءات سريعة",
  "viewAll": "عرض الكل",
  "viewDetails": "عرض التفاصيل",
  "manageUsers": "إدارة المستخدمين",
  "manageServices": "إدارة الخدمات",
  "manageBookings": "إدارة الحجوزات",
  "generateReport": "إنشاء تقرير",
  "exportData": "تصدير البيانات",
  "systemHealth": "صحة النظام",
  "performanceMetrics": "مؤشرات الأداء"
}
```

## 🔧 إعدادات i18n

### config.ts
```typescript
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import Backend from 'i18next-fs-backend';
import LanguageDetector from 'i18next-browser-languagedetector';

export const defaultNS = 'common';
export const resources = {
  ar: {
    common: () => import('../locales/ar/common.json'),
    auth: () => import('../locales/ar/auth.json'),
    dashboard: () => import('../locales/ar/dashboard.json'),
    services: () => import('../locales/ar/services.json'),
    bookings: () => import('../locales/ar/bookings.json'),
    payments: () => import('../locales/ar/payments.json'),
    chat: () => import('../locales/ar/chat.json'),
    errors: () => import('../locales/ar/errors.json'),
  },
  en: {
    common: () => import('../locales/en/common.json'),
    auth: () => import('../locales/en/auth.json'),
    dashboard: () => import('../locales/en/dashboard.json'),
    services: () => import('../locales/en/services.json'),
    bookings: () => import('../locales/en/bookings.json'),
    payments: () => import('../locales/en/payments.json'),
    chat: () => import('../locales/en/chat.json'),
    errors: () => import('../locales/en/errors.json'),
  },
} as const;

i18n
  .use(Backend)
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    lng: 'ar', // اللغة الافتراضية
    fallbackLng: 'ar',
    defaultNS,
    
    interpolation: {
      escapeValue: false,
    },
    
    detection: {
      order: ['localStorage', 'navigator', 'htmlTag'],
      caches: ['localStorage'],
    },
    
    backend: {
      loadPath: '/locales/{{lng}}/{{ns}}.json',
    },
  });

export default i18n;
```

## 🎯 Hooks مخصصة

### useTranslation Hook
```typescript
import { useTranslation as useI18nTranslation } from 'react-i18next';

export const useTranslation = (namespace?: string) => {
  const { t, i18n } = useI18nTranslation(namespace);
  
  const changeLanguage = (lng: string) => {
    i18n.changeLanguage(lng);
    document.dir = lng === 'ar' ? 'rtl' : 'ltr';
  };
  
  const formatNumber = (number: number) => {
    return new Intl.NumberFormat(i18n.language).format(number);
  };
  
  const formatCurrency = (amount: number, currency = 'USD') => {
    return new Intl.NumberFormat(i18n.language, {
      style: 'currency',
      currency,
    }).format(amount);
  };
  
  const formatDate = (date: Date, options?: Intl.DateTimeFormatOptions) => {
    return new Intl.DateTimeFormat(i18n.language, options).format(date);
  };
  
  return {
    t,
    i18n,
    changeLanguage,
    formatNumber,
    formatCurrency,
    formatDate,
    isRTL: i18n.language === 'ar',
    currentLanguage: i18n.language,
  };
};
```

## 🧪 الاختبار

### تشغيل الاختبارات
```bash
npm test
```

### اختبارات الترجمة
- **Translation Coverage**: تغطية جميع المفاتيح
- **Missing Keys**: اكتشاف المفاتيح المفقودة
- **Pluralization**: اختبار صيغ الجمع
- **Interpolation**: اختبار إدراج المتغيرات

## 🔧 الأوامر المتاحة

### التطوير
```bash
# بناء الحزمة
npm run build

# مراقبة التغييرات
npm run dev

# فحص الترجمات
npm run check-translations

# إضافة مفاتيح جديدة
npm run add-keys
```

## 📞 الدعم والمساعدة

### الوثائق
- [i18next Documentation](https://www.i18next.com)
- [react-i18next Documentation](https://react.i18next.com)

### المساهمة
1. Fork المشروع
2. إضافة أو تحديث الترجمات
3. Commit التغييرات
4. Push إلى الفرع
5. فتح Pull Request

---

**🎯 نظام ترجمة شامل ودعم دولي كامل لمنصة فريلا سوريا**

*آخر تحديث: ديسمبر 2024*
