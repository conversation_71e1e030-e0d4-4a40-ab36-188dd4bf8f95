# 👨‍💼 Freela Syria - Admin Dashboard

> لوحة تحكم المدير الشاملة لإدارة منصة فريلا سوريا

## 📋 نظرة عامة

لوحة تحكم المدير هي واجهة إدارية شاملة مصممة لإدارة جميع جوانب منصة فريلا سوريا. تتيح للمديرين مراقبة النشاط، إدارة المستخدمين، الإشراف على الخدمات، ومتابعة الأداء المالي للمنصة.

## ✨ الميزات الرئيسية

### 📊 لوحة التحكم الرئيسية
- **إحصائيات شاملة**: عرض مؤشرات الأداء الرئيسية (KPIs)
- **رسوم بيانية تفاعلية**: تحليلات بصرية للبيانات
- **تحديثات فورية**: معلومات محدثة في الوقت الفعلي
- **نظرة عامة سريعة**: ملخص حالة المنصة

### 👥 إدارة المستخدمين
- **قائمة المستخدمين**: عرض وتصفية جميع المستخدمين
- **ملفات تفصيلية**: معلومات شاملة عن كل مستخدم
- **إدارة الحالات**: تفعيل، تعليق، أو حذف الحسابات
- **تتبع النشاط**: مراقبة نشاط المستخدمين

### 🛠️ إدارة الخدمات
- **كتالوج الخدمات**: عرض جميع الخدمات المتاحة
- **مراجعة الخدمات**: الموافقة أو رفض الخدمات الجديدة
- **إدارة الفئات**: تنظيم وإدارة فئات الخدمات
- **مراقبة الجودة**: ضمان جودة الخدمات المقدمة

### 📅 إدارة الحجوزات
- **تتبع الحجوزات**: مراقبة جميع الحجوزات النشطة
- **حل النزاعات**: إدارة النزاعات بين العملاء والخبراء
- **إحصائيات الحجز**: تحليل أنماط الحجز والأداء
- **إدارة الحالات**: تحديث حالات الحجوزات

### 💰 إدارة المدفوعات
- **تتبع المعاملات**: مراقبة جميع المعاملات المالية
- **إدارة المبالغ المستردة**: معالجة طلبات الاسترداد
- **تقارير مالية**: تحليل الإيرادات والأرباح
- **إعدادات الدفع**: إدارة طرق الدفع المتاحة

### 📈 التحليلات والتقارير
- **تحليلات شاملة**: رؤى عميقة حول أداء المنصة
- **تقارير مخصصة**: إنشاء تقارير حسب الحاجة
- **مؤشرات الأداء**: متابعة KPIs الرئيسية
- **تصدير البيانات**: تصدير التقارير بصيغ مختلفة

## 🛠️ المكدس التقني

### Frontend Framework
- **Next.js 14**: إطار عمل React مع App Router
- **TypeScript**: لغة البرمجة المكتوبة
- **Tailwind CSS**: إطار عمل التصميم
- **React Hook Form**: إدارة النماذج
- **Zustand**: إدارة الحالة

### UI Components
- **Headless UI**: مكونات واجهة المستخدم
- **Heroicons**: مكتبة الأيقونات
- **Recharts**: رسوم بيانية تفاعلية
- **React Hot Toast**: إشعارات المستخدم
- **Framer Motion**: الرسوم المتحركة

### Internationalization
- **next-i18next**: دعم الترجمة
- **Arabic RTL**: دعم كامل للعربية من اليمين لليسار
- **Multi-language**: دعم العربية والإنجليزية

## 🚀 البدء السريع

### المتطلبات الأساسية
```bash
Node.js >= 18.0.0
npm >= 9.0.0
```

### التثبيت والتشغيل
```bash
# الانتقال إلى مجلد لوحة المدير
cd apps/admin-dashboard

# تثبيت التبعيات
npm install

# تشغيل خادم التطوير
npm run dev

# فتح المتصفح على
http://localhost:3001
```

### البناء للإنتاج
```bash
# بناء التطبيق
npm run build

# تشغيل النسخة المبنية
npm start
```

## 📁 هيكل المشروع

```
src/
├── app/                    # صفحات Next.js App Router
│   ├── dashboard/         # صفحات لوحة التحكم
│   │   ├── page.tsx      # الصفحة الرئيسية
│   │   ├── users/        # إدارة المستخدمين
│   │   ├── services/     # إدارة الخدمات
│   │   ├── bookings/     # إدارة الحجوزات
│   │   ├── payments/     # إدارة المدفوعات
│   │   ├── analytics/    # التحليلات
│   │   ├── reports/      # التقارير
│   │   ├── disputes/     # إدارة النزاعات
│   │   ├── categories/   # إدارة الفئات
│   │   └── settings/     # إعدادات النظام
│   ├── auth/             # صفحات المصادقة
│   ├── globals.css       # الأنماط العامة
│   └── layout.tsx        # التخطيط الرئيسي
├── components/            # المكونات القابلة لإعادة الاستخدام
│   ├── layout/           # مكونات التخطيط
│   ├── dashboard/        # مكونات لوحة التحكم
│   ├── forms/            # مكونات النماذج
│   ├── ui/               # مكونات واجهة المستخدم
│   └── modals/           # النوافذ المنبثقة
├── lib/                  # المكتبات والأدوات المساعدة
├── hooks/                # React Hooks مخصصة
├── store/                # إدارة الحالة (Zustand)
├── types/                # تعريفات TypeScript
└── utils/                # وظائف مساعدة
```

## 🎨 التصميم والواجهة

### نظام الألوان
- **Primary**: الأزرق الداكن للعناصر الرئيسية
- **Secondary**: الرمادي للعناصر الثانوية
- **Success**: الأخضر للحالات الناجحة
- **Warning**: الأصفر للتحذيرات
- **Error**: الأحمر للأخطاء

### Typography
- **Font Family**: Cairo (للعربية) و Inter (للإنجليزية)
- **Font Weights**: 300, 400, 500, 600, 700
- **RTL Support**: دعم كامل للكتابة من اليمين لليسار

### Dark Theme
- دعم كامل للوضع المظلم
- تبديل سلس بين الأوضاع
- حفظ تفضيلات المستخدم

## 🔐 المصادقة والأمان

### نظام المصادقة
- **JWT Tokens**: رموز الوصول الآمنة
- **Role-based Access**: تحكم في الوصول حسب الدور
- **Session Management**: إدارة الجلسات
- **Auto Logout**: تسجيل خروج تلقائي عند انتهاء الصلاحية

### الأمان
- **CSRF Protection**: حماية من هجمات CSRF
- **XSS Prevention**: منع هجمات XSS
- **Input Validation**: التحقق من صحة المدخلات
- **Secure Headers**: رؤوس أمان HTTP

## 📊 الصفحات والوظائف

### 🏠 الصفحة الرئيسية (`/dashboard`)
- نظرة عامة على إحصائيات المنصة
- رسوم بيانية للنمو والأداء
- تحديثات النشاط الأخيرة
- روابط سريعة للمهام الشائعة

### 👥 إدارة المستخدمين (`/dashboard/users`)
- قائمة شاملة بجميع المستخدمين
- فلترة وبحث متقدم
- عرض تفاصيل المستخدم
- إدارة حالات الحساب

### 🛠️ إدارة الخدمات (`/dashboard/services`)
- كتالوج جميع الخدمات
- مراجعة والموافقة على الخدمات
- إدارة فئات الخدمات
- إحصائيات الخدمات

### 📅 إدارة الحجوزات (`/dashboard/bookings`)
- تتبع جميع الحجوزات
- فلترة حسب الحالة والتاريخ
- إدارة النزاعات
- تحديث حالات الحجز

### 💰 إدارة المدفوعات (`/dashboard/payments`)
- تتبع المعاملات المالية
- إدارة المبالغ المستردة
- تقارير الإيرادات
- إعدادات طرق الدفع

### 📈 التحليلات (`/dashboard/analytics`)
- مؤشرات الأداء الرئيسية
- رسوم بيانية تفاعلية
- تحليل اتجاهات النمو
- مقارنات زمنية

### 📋 التقارير (`/dashboard/reports`)
- تقارير مالية مفصلة
- تقارير المستخدمين والنشاط
- تقارير الخدمات والحجوزات
- تصدير البيانات

### ⚖️ إدارة النزاعات (`/dashboard/disputes`)
- قائمة النزاعات النشطة
- تفاصيل كل نزاع
- أدوات حل النزاعات
- تتبع القرارات

### 🏷️ إدارة الفئات (`/dashboard/categories`)
- إدارة فئات الخدمات
- إضافة وتعديل الفئات
- ترتيب وتنظيم الفئات
- إحصائيات الفئات

### ⚙️ إعدادات النظام (`/dashboard/settings`)
- إعدادات المنصة العامة
- إدارة قوالب البريد الإلكتروني
- إعدادات الإشعارات
- مفاتيح الميزات

## 🧪 الاختبار

### تشغيل الاختبارات
```bash
# اختبارات الوحدة
npm test

# اختبارات مع تغطية الكود
npm run test:coverage

# اختبارات E2E
npm run test:e2e
```

### فحص جودة الكود
```bash
# فحص ESLint
npm run lint

# إصلاح مشاكل ESLint
npm run lint:fix

# فحص TypeScript
npm run type-check
```

## 🚀 النشر

### بناء الإنتاج
```bash
npm run build
```

### متغيرات البيئة
```env
NEXT_PUBLIC_API_URL=http://localhost:3000/api/v1
NEXT_PUBLIC_APP_NAME=Freela Syria Admin
NEXTAUTH_SECRET=your-secret-key
NEXTAUTH_URL=http://localhost:3001
```

## 📞 الدعم والمساعدة

### الوثائق
- [Next.js Documentation](https://nextjs.org/docs)
- [Tailwind CSS](https://tailwindcss.com/docs)
- [TypeScript](https://www.typescriptlang.org/docs)

### المساهمة
1. Fork المشروع
2. إنشاء فرع للميزة الجديدة
3. Commit التغييرات
4. Push إلى الفرع
5. فتح Pull Request

---

**🎯 لوحة تحكم شاملة لإدارة منصة فريلا سوريا بكفاءة واحترافية**

*آخر تحديث: ديسمبر 2024*
