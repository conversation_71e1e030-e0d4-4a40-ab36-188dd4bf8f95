"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[519],{30622:function(e,t,n){var r=n(2265),o=Symbol.for("react.element"),l=Symbol.for("react.fragment"),i=Object.prototype.hasOwnProperty,u=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,a={key:!0,ref:!0,__self:!0,__source:!0};function s(e,t,n){var r,l={},s=null,c=null;for(r in void 0!==n&&(s=""+n),void 0!==t.key&&(s=""+t.key),void 0!==t.ref&&(c=t.ref),t)i.call(t,r)&&!a.hasOwnProperty(r)&&(l[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===l[r]&&(l[r]=t[r]);return{$$typeof:o,type:e,key:s,ref:c,props:l,_owner:u.current}}t.Fragment=l,t.jsx=s,t.jsxs=s},57437:function(e,t,n){e.exports=n(30622)},14781:function(e,t,n){let r,o;n.d(t,{V:function(){return ey}});var l,i,u,a,s,c,d,f=n(2265),p=n.t(f,2),m=n(82769),v=n(12950),h=n(61858);function g(e,t,n,r){let o=(0,h.E)(n);(0,f.useEffect)(()=>{function n(e){o.current(e)}return(e=null!=e?e:window).addEventListener(t,n,r),()=>e.removeEventListener(t,n,r)},[e,t,r])}var E=n(80634),b=n(55195);function w(e){let t=(0,v.z)(e),n=(0,f.useRef)(!1);(0,f.useEffect)(()=>(n.current=!1,()=>{n.current=!0,(0,b.Y)(()=>{n.current&&t()})}),[t])}var y=n(57728),T=n(48957),O=n(46618),S=n(27976),C=((l=C||{})[l.Forwards=0]="Forwards",l[l.Backwards=1]="Backwards",l);function P(e,t){let n=(0,f.useRef)([]),r=(0,v.z)(e);(0,f.useEffect)(()=>{let e=[...n.current];for(let[o,l]of t.entries())if(n.current[o]!==l){let o=r(t,e);return n.current=t,o}},[r,...t])}var L=n(11931),F=((i=F||{})[i.None=1]="None",i[i.Focusable=2]="Focusable",i[i.Hidden=4]="Hidden",i);let M=(0,L.yV)(function(e,t){var n;let{features:r=1,...o}=e,l={ref:t,"aria-hidden":(2&r)==2||(null!=(n=o["aria-hidden"])?n:void 0),hidden:(4&r)==4||void 0,style:{position:"fixed",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0",...(4&r)==4&&(2&r)!=2&&{display:"none"}}};return(0,L.sY)({ourProps:l,theirProps:o,slot:{},defaultTag:"div",name:"Hidden"})}),R=[];!function(e){function t(){"loading"!==document.readyState&&(e(),document.removeEventListener("DOMContentLoaded",t))}"undefined"!=typeof window&&"undefined"!=typeof document&&(document.addEventListener("DOMContentLoaded",t),t())}(()=>{function e(e){e.target instanceof HTMLElement&&e.target!==document.body&&R[0]!==e.target&&(R.unshift(e.target),(R=R.filter(e=>null!=e&&e.isConnected)).splice(10))}window.addEventListener("click",e,{capture:!0}),window.addEventListener("mousedown",e,{capture:!0}),window.addEventListener("focus",e,{capture:!0}),document.body.addEventListener("click",e,{capture:!0}),document.body.addEventListener("mousedown",e,{capture:!0}),document.body.addEventListener("focus",e,{capture:!0})});var A=n(65410),N=n(60597);function k(e){if(!e)return new Set;if("function"==typeof e)return new Set(e());let t=new Set;for(let n of e.current)n.current instanceof HTMLElement&&t.add(n.current);return t}var x=((u=x||{})[u.None=1]="None",u[u.InitialFocus=2]="InitialFocus",u[u.TabLock=4]="TabLock",u[u.FocusLock=8]="FocusLock",u[u.RestoreFocus=16]="RestoreFocus",u[u.All=30]="All",u);let D=Object.assign((0,L.yV)(function(e,t){let n,r=(0,f.useRef)(null),o=(0,O.T)(r,t),{initialFocus:l,containers:i,features:u=30,...a}=e;(0,T.H)()||(u=1);let s=(0,y.i)(r);!function({ownerDocument:e},t){let n=function(e=!0){let t=(0,f.useRef)(R.slice());return P(([e],[n])=>{!0===n&&!1===e&&(0,b.Y)(()=>{t.current.splice(0)}),!1===n&&!0===e&&(t.current=R.slice())},[e,R,t]),(0,v.z)(()=>{var e;return null!=(e=t.current.find(e=>null!=e&&e.isConnected))?e:null})}(t);P(()=>{t||(null==e?void 0:e.activeElement)===(null==e?void 0:e.body)&&(0,A.C5)(n())},[t]),w(()=>{t&&(0,A.C5)(n())})}({ownerDocument:s},!!(16&u));let c=function({ownerDocument:e,container:t,initialFocus:n},r){let o=(0,f.useRef)(null),l=(0,E.t)();return P(()=>{if(!r)return;let i=t.current;i&&(0,b.Y)(()=>{if(!l.current)return;let t=null==e?void 0:e.activeElement;if(null!=n&&n.current){if((null==n?void 0:n.current)===t){o.current=t;return}}else if(i.contains(t)){o.current=t;return}null!=n&&n.current?(0,A.C5)(n.current):(0,A.jA)(i,A.TO.First)===A.fE.Error&&console.warn("There are no focusable elements inside the <FocusTrap />"),o.current=null==e?void 0:e.activeElement})},[r]),o}({ownerDocument:s,container:r,initialFocus:l},!!(2&u));!function({ownerDocument:e,container:t,containers:n,previousActiveElement:r},o){let l=(0,E.t)();g(null==e?void 0:e.defaultView,"focus",e=>{if(!o||!l.current)return;let i=k(n);t.current instanceof HTMLElement&&i.add(t.current);let u=r.current;if(!u)return;let a=e.target;a&&a instanceof HTMLElement?H(i,a)?(r.current=a,(0,A.C5)(a)):(e.preventDefault(),e.stopPropagation(),(0,A.C5)(u)):(0,A.C5)(r.current)},!0)}({ownerDocument:s,container:r,containers:i,previousActiveElement:c},!!(8&u));let d=(n=(0,f.useRef)(0),(0,S.s)("keydown",e=>{"Tab"===e.key&&(n.current=e.shiftKey?1:0)},!0),n),p=(0,v.z)(e=>{let t=r.current;t&&(0,N.E)(d.current,{[C.Forwards]:()=>{(0,A.jA)(t,A.TO.First,{skipElements:[e.relatedTarget]})},[C.Backwards]:()=>{(0,A.jA)(t,A.TO.Last,{skipElements:[e.relatedTarget]})}})}),h=(0,m.G)(),x=(0,f.useRef)(!1);return f.createElement(f.Fragment,null,!!(4&u)&&f.createElement(M,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:p,features:F.Focusable}),(0,L.sY)({ourProps:{ref:o,onKeyDown(e){"Tab"==e.key&&(x.current=!0,h.requestAnimationFrame(()=>{x.current=!1}))},onBlur(e){let t=k(i);r.current instanceof HTMLElement&&t.add(r.current);let n=e.relatedTarget;n instanceof HTMLElement&&"true"!==n.dataset.headlessuiFocusGuard&&(H(t,n)||(x.current?(0,A.jA)(r.current,(0,N.E)(d.current,{[C.Forwards]:()=>A.TO.Next,[C.Backwards]:()=>A.TO.Previous})|A.TO.WrapAround,{relativeTo:e.target}):e.target instanceof HTMLElement&&(0,A.C5)(e.target)))}},theirProps:a,defaultTag:"div",name:"FocusTrap"}),!!(4&u)&&f.createElement(M,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:p,features:F.Focusable}))}),{features:x});function H(e,t){for(let n of e)if(n.contains(t))return!0;return!1}var j=n(54887),I=n(32600);let _=(0,f.createContext)(!1);function Y(e){return f.createElement(_.Provider,{value:e.force},e.children)}var V=n(52057);let z=f.Fragment,B=f.Fragment,U=(0,f.createContext)(null),$=(0,f.createContext)(null),W=Object.assign((0,L.yV)(function(e,t){let n=(0,f.useRef)(null),r=(0,O.T)((0,O.h)(e=>{n.current=e}),t),o=(0,y.i)(n),l=function(e){let t=(0,f.useContext)(_),n=(0,f.useContext)(U),r=(0,y.i)(e),[o,l]=(0,f.useState)(()=>{if(!t&&null!==n||V.O.isServer)return null;let e=null==r?void 0:r.getElementById("headlessui-portal-root");if(e)return e;if(null===r)return null;let o=r.createElement("div");return o.setAttribute("id","headlessui-portal-root"),r.body.appendChild(o)});return(0,f.useEffect)(()=>{null!==o&&(null!=r&&r.body.contains(o)||null==r||r.body.appendChild(o))},[o,r]),(0,f.useEffect)(()=>{t||null!==n&&l(n.current)},[n,l,t]),o}(n),[i]=(0,f.useState)(()=>{var e;return V.O.isServer?null:null!=(e=null==o?void 0:o.createElement("div"))?e:null}),u=(0,f.useContext)($),a=(0,T.H)();return(0,I.e)(()=>{!l||!i||l.contains(i)||(i.setAttribute("data-headlessui-portal",""),l.appendChild(i))},[l,i]),(0,I.e)(()=>{if(i&&u)return u.register(i)},[u,i]),w(()=>{var e;l&&i&&(i instanceof Node&&l.contains(i)&&l.removeChild(i),l.childNodes.length<=0&&(null==(e=l.parentElement)||e.removeChild(l)))}),a&&l&&i?(0,j.createPortal)((0,L.sY)({ourProps:{ref:r},theirProps:e,defaultTag:z,name:"Portal"}),i):null}),{Group:(0,L.yV)(function(e,t){let{target:n,...r}=e,o={ref:(0,O.T)(t)};return f.createElement(U.Provider,{value:n},(0,L.sY)({ourProps:o,theirProps:r,defaultTag:B,name:"Popover.Group"}))})}),{useState:q,useEffect:Z,useLayoutEffect:G,useDebugValue:K}=p;"undefined"!=typeof window&&void 0!==window.document&&window.document.createElement;let J=p.useSyncExternalStore;var X=n(85390),Q=n(34644);let ee=(a={PUSH(e,t){var n;let r=null!=(n=this.get(e))?n:{doc:e,count:0,d:(0,X.k)(),meta:new Set};return r.count++,r.meta.add(t),this.set(e,r),this},POP(e,t){let n=this.get(e);return n&&(n.count--,n.meta.delete(t)),this},SCROLL_PREVENT({doc:e,d:t,meta:n}){let r;let o={doc:e,d:t,meta:function(e){let t={};for(let n of e)Object.assign(t,n(t));return t}(n)},l=[(0,Q.gn)()?{before({doc:e,d:t,meta:n}){function r(e){return n.containers.flatMap(e=>e()).some(t=>t.contains(e))}t.microTask(()=>{var n;if("auto"!==window.getComputedStyle(e.documentElement).scrollBehavior){let n=(0,X.k)();n.style(e.documentElement,"scrollBehavior","auto"),t.add(()=>t.microTask(()=>n.dispose()))}let o=null!=(n=window.scrollY)?n:window.pageYOffset,l=null;t.addEventListener(e,"click",t=>{if(t.target instanceof HTMLElement)try{let n=t.target.closest("a");if(!n)return;let{hash:o}=new URL(n.href),i=e.querySelector(o);i&&!r(i)&&(l=i)}catch{}},!0),t.addEventListener(e,"touchstart",e=>{if(e.target instanceof HTMLElement){if(r(e.target)){let n=e.target;for(;n.parentElement&&r(n.parentElement);)n=n.parentElement;t.style(n,"overscrollBehavior","contain")}else t.style(e.target,"touchAction","none")}}),t.addEventListener(e,"touchmove",e=>{if(e.target instanceof HTMLElement){if(r(e.target)){let t=e.target;for(;t.parentElement&&""!==t.dataset.headlessuiPortal&&!(t.scrollHeight>t.clientHeight||t.scrollWidth>t.clientWidth);)t=t.parentElement;""===t.dataset.headlessuiPortal&&e.preventDefault()}else e.preventDefault()}},{passive:!1}),t.add(()=>{var e;o!==(null!=(e=window.scrollY)?e:window.pageYOffset)&&window.scrollTo(0,o),l&&l.isConnected&&(l.scrollIntoView({block:"nearest"}),l=null)})})}}:{},{before({doc:e}){var t;let n=e.documentElement;r=(null!=(t=e.defaultView)?t:window).innerWidth-n.clientWidth},after({doc:e,d:t}){let n=e.documentElement,o=n.clientWidth-n.offsetWidth,l=r-o;t.style(n,"paddingRight",`${l}px`)}},{before({doc:e,d:t}){t.style(e.documentElement,"overflow","hidden")}}];l.forEach(({before:e})=>null==e?void 0:e(o)),l.forEach(({after:e})=>null==e?void 0:e(o))},SCROLL_ALLOW({d:e}){e.dispose()},TEARDOWN({doc:e}){this.delete(e)}},r=new Map,o=new Set,{getSnapshot:()=>r,subscribe:e=>(o.add(e),()=>o.delete(e)),dispatch(e,...t){let n=a[e].call(r,...t);n&&(r=n,o.forEach(e=>e()))}});ee.subscribe(()=>{let e=ee.getSnapshot(),t=new Map;for(let[n]of e)t.set(n,n.documentElement.style.overflow);for(let n of e.values()){let e="hidden"===t.get(n.doc),r=0!==n.count;(r&&!e||!r&&e)&&ee.dispatch(n.count>0?"SCROLL_PREVENT":"SCROLL_ALLOW",n),0===n.count&&ee.dispatch("TEARDOWN",n)}});var et=n(75606);let en=new Map,er=new Map;function eo(e,t=!0){(0,I.e)(()=>{var n;if(!t)return;let r="function"==typeof e?e():e.current;if(!r)return;let o=null!=(n=er.get(r))?n:0;return er.set(r,o+1),0!==o||(en.set(r,{"aria-hidden":r.getAttribute("aria-hidden"),inert:r.inert}),r.setAttribute("aria-hidden","true"),r.inert=!0),function(){var e;if(!r)return;let t=null!=(e=er.get(r))?e:1;if(1===t?er.delete(r):er.set(r,t-1),1!==t)return;let n=en.get(r);n&&(null===n["aria-hidden"]?r.removeAttribute("aria-hidden"):r.setAttribute("aria-hidden",n["aria-hidden"]),r.inert=n.inert,en.delete(r))}},[e,t])}var el=n(90583),ei=n(25306);let eu=(0,f.createContext)(()=>{});eu.displayName="StackContext";var ea=((s=ea||{})[s.Add=0]="Add",s[s.Remove=1]="Remove",s);function es({children:e,onUpdate:t,type:n,element:r,enabled:o}){let l=(0,f.useContext)(eu),i=(0,v.z)((...e)=>{null==t||t(...e),l(...e)});return(0,I.e)(()=>{let e=void 0===o||!0===o;return e&&i(0,n,r),()=>{e&&i(1,n,r)}},[i,n,r,o]),f.createElement(eu.Provider,{value:i},e)}var ec=n(35863);let ed=(0,f.createContext)(null),ef=Object.assign((0,L.yV)(function(e,t){let n=(0,et.M)(),{id:r=`headlessui-description-${n}`,...o}=e,l=function e(){let t=(0,f.useContext)(ed);if(null===t){let t=Error("You used a <Description /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(t,e),t}return t}(),i=(0,O.T)(t);(0,I.e)(()=>l.register(r),[r,l.register]);let u={ref:i,...l.props,id:r};return(0,L.sY)({ourProps:u,theirProps:o,slot:l.slot||{},defaultTag:"p",name:l.name||"Description"})}),{});var ep=n(93850),em=((c=em||{})[c.Open=0]="Open",c[c.Closed=1]="Closed",c),ev=((d=ev||{})[d.SetTitleId=0]="SetTitleId",d);let eh={0:(e,t)=>e.titleId===t.id?e:{...e,titleId:t.id}},eg=(0,f.createContext)(null);function eE(e){let t=(0,f.useContext)(eg);if(null===t){let t=Error(`<${e} /> is missing a parent <Dialog /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,eE),t}return t}function eb(e,t){return(0,N.E)(t.type,eh,e,t)}eg.displayName="DialogContext";let ew=L.AN.RenderStrategy|L.AN.Static,ey=Object.assign((0,L.yV)(function(e,t){let n,r,o,l,i,u=(0,et.M)(),{id:a=`headlessui-dialog-${u}`,open:s,onClose:c,initialFocus:d,role:p="dialog",__demoMode:m=!1,...h}=e,[E,b]=(0,f.useState)(0),w=(0,f.useRef)(!1);p="dialog"===p||"alertdialog"===p?p:(w.current||(w.current=!0,console.warn(`Invalid role [${p}] passed to <Dialog />. Only \`dialog\` and and \`alertdialog\` are supported. Using \`dialog\` instead.`)),"dialog");let S=(0,ei.oJ)();void 0===s&&null!==S&&(s=(S&ei.ZM.Open)===ei.ZM.Open);let C=(0,f.useRef)(null),P=(0,O.T)(C,t),R=(0,y.i)(C),A=e.hasOwnProperty("open")||null!==S,k=e.hasOwnProperty("onClose");if(!A&&!k)throw Error("You have to provide an `open` and an `onClose` prop to the `Dialog` component.");if(!A)throw Error("You provided an `onClose` prop to the `Dialog`, but forgot an `open` prop.");if(!k)throw Error("You provided an `open` prop to the `Dialog`, but forgot an `onClose` prop.");if("boolean"!=typeof s)throw Error(`You provided an \`open\` prop to the \`Dialog\`, but the value is not a boolean. Received: ${s}`);if("function"!=typeof c)throw Error(`You provided an \`onClose\` prop to the \`Dialog\`, but the value is not a function. Received: ${c}`);let x=s?0:1,[H,j]=(0,f.useReducer)(eb,{titleId:null,descriptionId:null,panelRef:(0,f.createRef)()}),_=(0,v.z)(()=>c(!1)),V=(0,v.z)(e=>j({type:0,id:e})),z=!!(0,T.H)()&&!m&&0===x,B=E>1,U=null!==(0,f.useContext)(eg),[q,Z]=(n=(0,f.useContext)($),r=(0,f.useRef)([]),o=(0,v.z)(e=>(r.current.push(e),n&&n.register(e),()=>l(e))),l=(0,v.z)(e=>{let t=r.current.indexOf(e);-1!==t&&r.current.splice(t,1),n&&n.unregister(e)}),i=(0,f.useMemo)(()=>({register:o,unregister:l,portals:r}),[o,l,r]),[r,(0,f.useMemo)(()=>function({children:e}){return f.createElement($.Provider,{value:i},e)},[i])]),{resolveContainers:G,mainTreeNodeRef:K,MainTreeNode:X}=function({defaultContainers:e=[],portals:t,mainTreeNodeRef:n}={}){var r;let o=(0,f.useRef)(null!=(r=null==n?void 0:n.current)?r:null),l=(0,y.i)(o),i=(0,v.z)(()=>{var n,r,i;let u=[];for(let t of e)null!==t&&(t instanceof HTMLElement?u.push(t):"current"in t&&t.current instanceof HTMLElement&&u.push(t.current));if(null!=t&&t.current)for(let e of t.current)u.push(e);for(let e of null!=(n=null==l?void 0:l.querySelectorAll("html > *, body > *"))?n:[])e!==document.body&&e!==document.head&&e instanceof HTMLElement&&"headlessui-portal-root"!==e.id&&(e.contains(o.current)||e.contains(null==(i=null==(r=o.current)?void 0:r.getRootNode())?void 0:i.host)||u.some(t=>e.contains(t))||u.push(e));return u});return{resolveContainers:i,contains:(0,v.z)(e=>i().some(t=>t.contains(e))),mainTreeNodeRef:o,MainTreeNode:(0,f.useMemo)(()=>function(){return null!=n?null:f.createElement(M,{features:F.Hidden,ref:o})},[o,n])}}({portals:q,defaultContainers:[{get current(){var Q;return null!=(Q=H.panelRef.current)?Q:C.current}}]}),en=null!==S&&(S&ei.ZM.Closing)===ei.ZM.Closing,er=!U&&!en&&z;eo((0,f.useCallback)(()=>{var e,t;return null!=(t=Array.from(null!=(e=null==R?void 0:R.querySelectorAll("body > *"))?e:[]).find(e=>"headlessui-portal-root"!==e.id&&e.contains(K.current)&&e instanceof HTMLElement))?t:null},[K]),er);let eu=!!B||z;eo((0,f.useCallback)(()=>{var e,t;return null!=(t=Array.from(null!=(e=null==R?void 0:R.querySelectorAll("[data-headlessui-portal]"))?e:[]).find(e=>e.contains(K.current)&&e instanceof HTMLElement))?t:null},[K]),eu);let ec=!(!z||B);(0,el.O)(G,e=>{e.preventDefault(),_()},ec);let ef=!(B||0!==x);g(null==R?void 0:R.defaultView,"keydown",e=>{ef&&(e.defaultPrevented||e.key===ep.R.Escape&&(e.preventDefault(),e.stopPropagation(),_()))}),function(e,t,n=()=>[document.body]){var r;let o,l;r=e=>{var t;return{containers:[...null!=(t=e.containers)?t:[],n]}},o=J(ee.subscribe,ee.getSnapshot,ee.getSnapshot),(l=e?o.get(e):void 0)&&l.count,(0,I.e)(()=>{if(!(!e||!t))return ee.dispatch("PUSH",e,r),()=>ee.dispatch("POP",e,r)},[t,e])}(R,!(en||0!==x||U),G),(0,f.useEffect)(()=>{if(0!==x||!C.current)return;let e=new ResizeObserver(e=>{for(let t of e){let e=t.target.getBoundingClientRect();0===e.x&&0===e.y&&0===e.width&&0===e.height&&_()}});return e.observe(C.current),()=>e.disconnect()},[x,C,_]);let[em,ev]=function(){let[e,t]=(0,f.useState)([]);return[e.length>0?e.join(" "):void 0,(0,f.useMemo)(()=>function(e){let n=(0,v.z)(e=>(t(t=>[...t,e]),()=>t(t=>{let n=t.slice(),r=n.indexOf(e);return -1!==r&&n.splice(r,1),n}))),r=(0,f.useMemo)(()=>({register:n,slot:e.slot,name:e.name,props:e.props}),[n,e.slot,e.name,e.props]);return f.createElement(ed.Provider,{value:r},e.children)},[t])]}(),eh=(0,f.useMemo)(()=>[{dialogState:x,close:_,setTitleId:V},H],[x,H,_,V]),eE=(0,f.useMemo)(()=>({open:0===x}),[x]),ey={ref:P,id:a,role:p,"aria-modal":0===x||void 0,"aria-labelledby":H.titleId,"aria-describedby":em};return f.createElement(es,{type:"Dialog",enabled:0===x,element:C,onUpdate:(0,v.z)((e,t)=>{"Dialog"===t&&(0,N.E)(e,{[ea.Add]:()=>b(e=>e+1),[ea.Remove]:()=>b(e=>e-1)})})},f.createElement(Y,{force:!0},f.createElement(W,null,f.createElement(eg.Provider,{value:eh},f.createElement(W.Group,{target:C},f.createElement(Y,{force:!1},f.createElement(ev,{slot:eE,name:"Dialog.Description"},f.createElement(D,{initialFocus:d,containers:G,features:z?(0,N.E)(B?"parent":"leaf",{parent:D.features.RestoreFocus,leaf:D.features.All&~D.features.FocusLock}):D.features.None},f.createElement(Z,null,(0,L.sY)({ourProps:ey,theirProps:h,slot:eE,defaultTag:"div",features:ew,visible:0===x,name:"Dialog"}))))))))),f.createElement(X,null))}),{Backdrop:(0,L.yV)(function(e,t){let n=(0,et.M)(),{id:r=`headlessui-dialog-backdrop-${n}`,...o}=e,[{dialogState:l},i]=eE("Dialog.Backdrop"),u=(0,O.T)(t);(0,f.useEffect)(()=>{if(null===i.panelRef.current)throw Error("A <Dialog.Backdrop /> component is being used, but a <Dialog.Panel /> component is missing.")},[i.panelRef]);let a=(0,f.useMemo)(()=>({open:0===l}),[l]);return f.createElement(Y,{force:!0},f.createElement(W,null,(0,L.sY)({ourProps:{ref:u,id:r,"aria-hidden":!0},theirProps:o,slot:a,defaultTag:"div",name:"Dialog.Backdrop"})))}),Panel:(0,L.yV)(function(e,t){let n=(0,et.M)(),{id:r=`headlessui-dialog-panel-${n}`,...o}=e,[{dialogState:l},i]=eE("Dialog.Panel"),u=(0,O.T)(t,i.panelRef),a=(0,f.useMemo)(()=>({open:0===l}),[l]),s=(0,v.z)(e=>{e.stopPropagation()});return(0,L.sY)({ourProps:{ref:u,id:r,onClick:s},theirProps:o,slot:a,defaultTag:"div",name:"Dialog.Panel"})}),Overlay:(0,L.yV)(function(e,t){let n=(0,et.M)(),{id:r=`headlessui-dialog-overlay-${n}`,...o}=e,[{dialogState:l,close:i}]=eE("Dialog.Overlay"),u=(0,O.T)(t),a=(0,v.z)(e=>{if(e.target===e.currentTarget){if((0,ec.P)(e.currentTarget))return e.preventDefault();e.preventDefault(),e.stopPropagation(),i()}}),s=(0,f.useMemo)(()=>({open:0===l}),[l]);return(0,L.sY)({ourProps:{ref:u,id:r,"aria-hidden":!0,onClick:a},theirProps:o,slot:s,defaultTag:"div",name:"Dialog.Overlay"})}),Title:(0,L.yV)(function(e,t){let n=(0,et.M)(),{id:r=`headlessui-dialog-title-${n}`,...o}=e,[{dialogState:l,setTitleId:i}]=eE("Dialog.Title"),u=(0,O.T)(t);(0,f.useEffect)(()=>(i(r),()=>i(null)),[r,i]);let a=(0,f.useMemo)(()=>({open:0===l}),[l]);return(0,L.sY)({ourProps:{ref:u,id:r},theirProps:o,slot:a,defaultTag:"h2",name:"Dialog.Title"})}),Description:ef})},93850:function(e,t,n){n.d(t,{R:function(){return o}});var r,o=((r=o||{}).Space=" ",r.Enter="Enter",r.Escape="Escape",r.Backspace="Backspace",r.Delete="Delete",r.ArrowLeft="ArrowLeft",r.ArrowUp="ArrowUp",r.ArrowRight="ArrowRight",r.ArrowDown="ArrowDown",r.Home="Home",r.End="End",r.PageUp="PageUp",r.PageDown="PageDown",r.Tab="Tab",r)},9805:function(e,t,n){n.d(t,{u:function(){return N}});var r,o=n(2265),l=n(82769),i=n(12950),u=n(80634),a=n(32600),s=n(61858),c=n(48957),d=n(46618),f=n(85390),p=n(60597);function m(e,...t){e&&t.length>0&&e.classList.add(...t)}function v(e,...t){e&&t.length>0&&e.classList.remove(...t)}var h=n(25306),g=n(63960),E=n(11931);function b(e=""){return e.split(/\s+/).filter(e=>e.length>1)}let w=(0,o.createContext)(null);w.displayName="TransitionContext";var y=((r=y||{}).Visible="visible",r.Hidden="hidden",r);let T=(0,o.createContext)(null);function O(e){return"children"in e?O(e.children):e.current.filter(({el:e})=>null!==e.current).filter(({state:e})=>"visible"===e).length>0}function S(e,t){let n=(0,s.E)(e),r=(0,o.useRef)([]),a=(0,u.t)(),c=(0,l.G)(),d=(0,i.z)((e,t=E.l4.Hidden)=>{let o=r.current.findIndex(({el:t})=>t===e);-1!==o&&((0,p.E)(t,{[E.l4.Unmount](){r.current.splice(o,1)},[E.l4.Hidden](){r.current[o].state="hidden"}}),c.microTask(()=>{var e;!O(r)&&a.current&&(null==(e=n.current)||e.call(n))}))}),f=(0,i.z)(e=>{let t=r.current.find(({el:t})=>t===e);return t?"visible"!==t.state&&(t.state="visible"):r.current.push({el:e,state:"visible"}),()=>d(e,E.l4.Unmount)}),m=(0,o.useRef)([]),v=(0,o.useRef)(Promise.resolve()),h=(0,o.useRef)({enter:[],leave:[],idle:[]}),g=(0,i.z)((e,n,r)=>{m.current.splice(0),t&&(t.chains.current[n]=t.chains.current[n].filter(([t])=>t!==e)),null==t||t.chains.current[n].push([e,new Promise(e=>{m.current.push(e)})]),null==t||t.chains.current[n].push([e,new Promise(e=>{Promise.all(h.current[n].map(([e,t])=>t)).then(()=>e())})]),"enter"===n?v.current=v.current.then(()=>null==t?void 0:t.wait.current).then(()=>r(n)):r(n)}),b=(0,i.z)((e,t,n)=>{Promise.all(h.current[t].splice(0).map(([e,t])=>t)).then(()=>{var e;null==(e=m.current.shift())||e()}).then(()=>n(t))});return(0,o.useMemo)(()=>({children:r,register:f,unregister:d,onStart:g,onStop:b,wait:v,chains:h}),[f,d,r,g,b,h,v])}function C(){}T.displayName="NestingContext";let P=["beforeEnter","afterEnter","beforeLeave","afterLeave"];function L(e){var t;let n={};for(let r of P)n[r]=null!=(t=e[r])?t:C;return n}let F=E.AN.RenderStrategy,M=(0,E.yV)(function(e,t){let{show:n,appear:r=!1,unmount:l=!0,...u}=e,s=(0,o.useRef)(null),f=(0,d.T)(s,t);(0,c.H)();let p=(0,h.oJ)();if(void 0===n&&null!==p&&(n=(p&h.ZM.Open)===h.ZM.Open),![!0,!1].includes(n))throw Error("A <Transition /> is used but it is missing a `show={true | false}` prop.");let[m,v]=(0,o.useState)(n?"visible":"hidden"),g=S(()=>{v("hidden")}),[b,y]=(0,o.useState)(!0),C=(0,o.useRef)([n]);(0,a.e)(()=>{!1!==b&&C.current[C.current.length-1]!==n&&(C.current.push(n),y(!1))},[C,n]);let P=(0,o.useMemo)(()=>({show:n,appear:r,initial:b}),[n,r,b]);(0,o.useEffect)(()=>{if(n)v("visible");else if(O(g)){let e=s.current;if(!e)return;let t=e.getBoundingClientRect();0===t.x&&0===t.y&&0===t.width&&0===t.height&&v("hidden")}else v("hidden")},[n,g]);let L={unmount:l},M=(0,i.z)(()=>{var t;b&&y(!1),null==(t=e.beforeEnter)||t.call(e)}),A=(0,i.z)(()=>{var t;b&&y(!1),null==(t=e.beforeLeave)||t.call(e)});return o.createElement(T.Provider,{value:g},o.createElement(w.Provider,{value:P},(0,E.sY)({ourProps:{...L,as:o.Fragment,children:o.createElement(R,{ref:f,...L,...u,beforeEnter:M,beforeLeave:A})},theirProps:{},defaultTag:o.Fragment,features:F,visible:"visible"===m,name:"Transition"})))}),R=(0,E.yV)(function(e,t){var n,r,y;let C;let{beforeEnter:P,afterEnter:M,beforeLeave:R,afterLeave:A,enter:N,enterFrom:k,enterTo:x,entered:D,leave:H,leaveFrom:j,leaveTo:I,..._}=e,Y=(0,o.useRef)(null),V=(0,d.T)(Y,t),z=null==(n=_.unmount)||n?E.l4.Unmount:E.l4.Hidden,{show:B,appear:U,initial:$}=function(){let e=(0,o.useContext)(w);if(null===e)throw Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),[W,q]=(0,o.useState)(B?"visible":"hidden"),Z=function(){let e=(0,o.useContext)(T);if(null===e)throw Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),{register:G,unregister:K}=Z;(0,o.useEffect)(()=>G(Y),[G,Y]),(0,o.useEffect)(()=>{if(z===E.l4.Hidden&&Y.current){if(B&&"visible"!==W){q("visible");return}return(0,p.E)(W,{hidden:()=>K(Y),visible:()=>G(Y)})}},[W,Y,G,K,B,z]);let J=(0,s.E)({base:b(_.className),enter:b(N),enterFrom:b(k),enterTo:b(x),entered:b(D),leave:b(H),leaveFrom:b(j),leaveTo:b(I)}),X=(y={beforeEnter:P,afterEnter:M,beforeLeave:R,afterLeave:A},C=(0,o.useRef)(L(y)),(0,o.useEffect)(()=>{C.current=L(y)},[y]),C),Q=(0,c.H)();(0,o.useEffect)(()=>{if(Q&&"visible"===W&&null===Y.current)throw Error("Did you forget to passthrough the `ref` to the actual DOM node?")},[Y,W,Q]);let ee=U&&B&&$,et=Q&&(!$||U)?B?"enter":"leave":"idle",en=function(e=0){let[t,n]=(0,o.useState)(e),r=(0,u.t)(),l=(0,o.useCallback)(e=>{r.current&&n(t=>t|e)},[t,r]),i=(0,o.useCallback)(e=>!!(t&e),[t]);return{flags:t,addFlag:l,hasFlag:i,removeFlag:(0,o.useCallback)(e=>{r.current&&n(t=>t&~e)},[n,r]),toggleFlag:(0,o.useCallback)(e=>{r.current&&n(t=>t^e)},[n])}}(0),er=(0,i.z)(e=>(0,p.E)(e,{enter:()=>{en.addFlag(h.ZM.Opening),X.current.beforeEnter()},leave:()=>{en.addFlag(h.ZM.Closing),X.current.beforeLeave()},idle:()=>{}})),eo=(0,i.z)(e=>(0,p.E)(e,{enter:()=>{en.removeFlag(h.ZM.Opening),X.current.afterEnter()},leave:()=>{en.removeFlag(h.ZM.Closing),X.current.afterLeave()},idle:()=>{}})),el=S(()=>{q("hidden"),K(Y)},Z),ei=(0,o.useRef)(!1);!function({immediate:e,container:t,direction:n,classes:r,onStart:o,onStop:i}){let c=(0,u.t)(),d=(0,l.G)(),h=(0,s.E)(n);(0,a.e)(()=>{e&&(h.current="enter")},[e]),(0,a.e)(()=>{let e=(0,f.k)();d.add(e.dispose);let n=t.current;if(n&&"idle"!==h.current&&c.current){var l,u,a;let t,s,c,d,g,E,b;return e.dispose(),o.current(h.current),e.add((l=r.current,u="enter"===h.current,a=()=>{e.dispose(),i.current(h.current)},s=u?"enter":"leave",c=(0,f.k)(),d=void 0!==a?(t={called:!1},(...e)=>{if(!t.called)return t.called=!0,a(...e)}):()=>{},"enter"===s&&(n.removeAttribute("hidden"),n.style.display=""),g=(0,p.E)(s,{enter:()=>l.enter,leave:()=>l.leave}),E=(0,p.E)(s,{enter:()=>l.enterTo,leave:()=>l.leaveTo}),b=(0,p.E)(s,{enter:()=>l.enterFrom,leave:()=>l.leaveFrom}),v(n,...l.base,...l.enter,...l.enterTo,...l.enterFrom,...l.leave,...l.leaveFrom,...l.leaveTo,...l.entered),m(n,...l.base,...g,...b),c.nextFrame(()=>{v(n,...l.base,...g,...b),m(n,...l.base,...g,...E),function(e,t){let n=(0,f.k)();if(!e)return n.dispose;let{transitionDuration:r,transitionDelay:o}=getComputedStyle(e),[l,i]=[r,o].map(e=>{let[t=0]=e.split(",").filter(Boolean).map(e=>e.includes("ms")?parseFloat(e):1e3*parseFloat(e)).sort((e,t)=>t-e);return t}),u=l+i;if(0!==u){n.group(n=>{n.setTimeout(()=>{t(),n.dispose()},u),n.addEventListener(e,"transitionrun",e=>{e.target===e.currentTarget&&n.dispose()})});let r=n.addEventListener(e,"transitionend",e=>{e.target===e.currentTarget&&(t(),r())})}else t();n.add(()=>t()),n.dispose}(n,()=>(v(n,...l.base,...g),m(n,...l.base,...l.entered),d()))}),c.dispose)),e.dispose}},[n])}({immediate:ee,container:Y,classes:J,direction:et,onStart:(0,s.E)(e=>{ei.current=!0,el.onStart(Y,e,er)}),onStop:(0,s.E)(e=>{ei.current=!1,el.onStop(Y,e,eo),"leave"!==e||O(el)||(q("hidden"),K(Y))})});let eu=_;return ee?eu={...eu,className:(0,g.A)(_.className,...J.current.enter,...J.current.enterFrom)}:ei.current&&(eu.className=(0,g.A)(_.className,null==(r=Y.current)?void 0:r.className),""===eu.className&&delete eu.className),o.createElement(T.Provider,{value:el},o.createElement(h.up,{value:(0,p.E)(W,{visible:h.ZM.Open,hidden:h.ZM.Closed})|en.flags},(0,E.sY)({ourProps:{ref:V},theirProps:eu,defaultTag:"div",features:F,visible:"visible"===W,name:"Transition.Child"})))}),A=(0,E.yV)(function(e,t){let n=null!==(0,o.useContext)(w),r=null!==(0,h.oJ)();return o.createElement(o.Fragment,null,!n&&r?o.createElement(M,{ref:t,...e}):o.createElement(R,{ref:t,...e}))}),N=Object.assign(M,{Child:A,Root:M})},82769:function(e,t,n){n.d(t,{G:function(){return l}});var r=n(2265),o=n(85390);function l(){let[e]=(0,r.useState)(o.k);return(0,r.useEffect)(()=>()=>e.dispose(),[e]),e}},12950:function(e,t,n){n.d(t,{z:function(){return l}});var r=n(2265),o=n(61858);let l=function(e){let t=(0,o.E)(e);return r.useCallback((...e)=>t.current(...e),[t])}},75606:function(e,t,n){n.d(t,{M:function(){return a}});var r,o=n(2265),l=n(52057),i=n(32600),u=n(48957);let a=null!=(r=o.useId)?r:function(){let e=(0,u.H)(),[t,n]=o.useState(e?()=>l.O.nextId():null);return(0,i.e)(()=>{null===t&&n(l.O.nextId())},[t]),null!=t?""+t:void 0}},80634:function(e,t,n){n.d(t,{t:function(){return l}});var r=n(2265),o=n(32600);function l(){let e=(0,r.useRef)(!1);return(0,o.e)(()=>(e.current=!0,()=>{e.current=!1}),[]),e}},32600:function(e,t,n){n.d(t,{e:function(){return l}});var r=n(2265),o=n(52057);let l=(e,t)=>{o.O.isServer?(0,r.useEffect)(e,t):(0,r.useLayoutEffect)(e,t)}},61858:function(e,t,n){n.d(t,{E:function(){return l}});var r=n(2265),o=n(32600);function l(e){let t=(0,r.useRef)(e);return(0,o.e)(()=>{t.current=e},[e]),t}},90583:function(e,t,n){n.d(t,{O:function(){return s}});var r=n(2265),o=n(65410),l=n(34644),i=n(61858);function u(e,t,n){let o=(0,i.E)(t);(0,r.useEffect)(()=>{function t(e){o.current(e)}return document.addEventListener(e,t,n),()=>document.removeEventListener(e,t,n)},[e,n])}var a=n(27976);function s(e,t,n=!0){let i=(0,r.useRef)(!1);function s(n,r){if(!i.current||n.defaultPrevented)return;let l=r(n);if(null!==l&&l.getRootNode().contains(l)&&l.isConnected){for(let t of function e(t){return"function"==typeof t?e(t()):Array.isArray(t)||t instanceof Set?t:[t]}(e)){if(null===t)continue;let e=t instanceof HTMLElement?t:t.current;if(null!=e&&e.contains(l)||n.composed&&n.composedPath().includes(e))return}return(0,o.sP)(l,o.tJ.Loose)||-1===l.tabIndex||n.preventDefault(),t(n,l)}}(0,r.useEffect)(()=>{requestAnimationFrame(()=>{i.current=n})},[n]);let c=(0,r.useRef)(null);u("pointerdown",e=>{var t,n;i.current&&(c.current=(null==(n=null==(t=e.composedPath)?void 0:t.call(e))?void 0:n[0])||e.target)},!0),u("mousedown",e=>{var t,n;i.current&&(c.current=(null==(n=null==(t=e.composedPath)?void 0:t.call(e))?void 0:n[0])||e.target)},!0),u("click",e=>{(0,l.tq)()||c.current&&(s(e,()=>c.current),c.current=null)},!0),u("touchend",e=>s(e,()=>e.target instanceof HTMLElement?e.target:null),!0),(0,a.s)("blur",e=>s(e,()=>window.document.activeElement instanceof HTMLIFrameElement?window.document.activeElement:null),!0)}},57728:function(e,t,n){n.d(t,{i:function(){return l}});var r=n(2265),o=n(54851);function l(...e){return(0,r.useMemo)(()=>(0,o.r)(...e),[...e])}},48957:function(e,t,n){n.d(t,{H:function(){return i}});var r,o=n(2265),l=n(52057);function i(){let e;let t=(e="undefined"==typeof document,(0,(r||(r=n.t(o,2))).useSyncExternalStore)(()=>()=>{},()=>!1,()=>!e)),[i,u]=o.useState(l.O.isHandoffComplete);return i&&!1===l.O.isHandoffComplete&&u(!1),o.useEffect(()=>{!0!==i&&u(!0)},[i]),o.useEffect(()=>l.O.handoff(),[]),!t&&i}},46618:function(e,t,n){n.d(t,{T:function(){return u},h:function(){return i}});var r=n(2265),o=n(12950);let l=Symbol();function i(e,t=!0){return Object.assign(e,{[l]:t})}function u(...e){let t=(0,r.useRef)(e);(0,r.useEffect)(()=>{t.current=e},[e]);let n=(0,o.z)(e=>{for(let n of t.current)null!=n&&("function"==typeof n?n(e):n.current=e)});return e.every(e=>null==e||(null==e?void 0:e[l]))?void 0:n}},27976:function(e,t,n){n.d(t,{s:function(){return l}});var r=n(2265),o=n(61858);function l(e,t,n){let l=(0,o.E)(t);(0,r.useEffect)(()=>{function t(e){l.current(e)}return window.addEventListener(e,t,n),()=>window.removeEventListener(e,t,n)},[e,n])}},25306:function(e,t,n){n.d(t,{ZM:function(){return i},oJ:function(){return u},up:function(){return a}});var r,o=n(2265);let l=(0,o.createContext)(null);l.displayName="OpenClosedContext";var i=((r=i||{})[r.Open=1]="Open",r[r.Closed=2]="Closed",r[r.Closing=4]="Closing",r[r.Opening=8]="Opening",r);function u(){return(0,o.useContext)(l)}function a({value:e,children:t}){return o.createElement(l.Provider,{value:e},t)}},35863:function(e,t,n){function r(e){let t=e.parentElement,n=null;for(;t&&!(t instanceof HTMLFieldSetElement);)t instanceof HTMLLegendElement&&(n=t),t=t.parentElement;let r=(null==t?void 0:t.getAttribute("disabled"))==="";return!(r&&function(e){if(!e)return!1;let t=e.previousElementSibling;for(;null!==t;){if(t instanceof HTMLLegendElement)return!1;t=t.previousElementSibling}return!0}(n))&&r}n.d(t,{P:function(){return r}})},63960:function(e,t,n){n.d(t,{A:function(){return r}});function r(...e){return Array.from(new Set(e.flatMap(e=>"string"==typeof e?e.split(" "):[]))).filter(Boolean).join(" ")}},85390:function(e,t,n){n.d(t,{k:function(){return function e(){let t=[],n={addEventListener:(e,t,r,o)=>(e.addEventListener(t,r,o),n.add(()=>e.removeEventListener(t,r,o))),requestAnimationFrame(...e){let t=requestAnimationFrame(...e);return n.add(()=>cancelAnimationFrame(t))},nextFrame:(...e)=>n.requestAnimationFrame(()=>n.requestAnimationFrame(...e)),setTimeout(...e){let t=setTimeout(...e);return n.add(()=>clearTimeout(t))},microTask(...e){let t={current:!0};return(0,r.Y)(()=>{t.current&&e[0]()}),n.add(()=>{t.current=!1})},style(e,t,n){let r=e.style.getPropertyValue(t);return Object.assign(e.style,{[t]:n}),this.add(()=>{Object.assign(e.style,{[t]:r})})},group(t){let n=e();return t(n),this.add(()=>n.dispose())},add:e=>(t.push(e),()=>{let n=t.indexOf(e);if(n>=0)for(let e of t.splice(n,1))e()}),dispose(){for(let e of t.splice(0))e()}};return n}}});var r=n(55195)},52057:function(e,t,n){n.d(t,{O:function(){return u}});var r=Object.defineProperty,o=(e,t,n)=>t in e?r(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,l=(e,t,n)=>(o(e,"symbol"!=typeof t?t+"":t,n),n);class i{constructor(){l(this,"current",this.detect()),l(this,"handoffState","pending"),l(this,"currentId",0)}set(e){this.current!==e&&(this.handoffState="pending",this.currentId=0,this.current=e)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return"server"===this.current}get isClient(){return"client"===this.current}detect(){return"undefined"==typeof window||"undefined"==typeof document?"server":"client"}handoff(){"pending"===this.handoffState&&(this.handoffState="complete")}get isHandoffComplete(){return"complete"===this.handoffState}}let u=new i},65410:function(e,t,n){n.d(t,{C5:function(){return w},EO:function(){return T},TO:function(){return f},fE:function(){return p},jA:function(){return O},sP:function(){return g},tJ:function(){return h},wI:function(){return E},z2:function(){return y}});var r,o,l,i,u,a=n(85390),s=n(60597),c=n(54851);let d=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map(e=>`${e}:not([tabindex='-1'])`).join(",");var f=((r=f||{})[r.First=1]="First",r[r.Previous=2]="Previous",r[r.Next=4]="Next",r[r.Last=8]="Last",r[r.WrapAround=16]="WrapAround",r[r.NoScroll=32]="NoScroll",r),p=((o=p||{})[o.Error=0]="Error",o[o.Overflow=1]="Overflow",o[o.Success=2]="Success",o[o.Underflow=3]="Underflow",o),m=((l=m||{})[l.Previous=-1]="Previous",l[l.Next=1]="Next",l);function v(e=document.body){return null==e?[]:Array.from(e.querySelectorAll(d)).sort((e,t)=>Math.sign((e.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}var h=((i=h||{})[i.Strict=0]="Strict",i[i.Loose=1]="Loose",i);function g(e,t=0){var n;return e!==(null==(n=(0,c.r)(e))?void 0:n.body)&&(0,s.E)(t,{0:()=>e.matches(d),1(){let t=e;for(;null!==t;){if(t.matches(d))return!0;t=t.parentElement}return!1}})}function E(e){let t=(0,c.r)(e);(0,a.k)().nextFrame(()=>{t&&!g(t.activeElement,0)&&w(e)})}var b=((u=b||{})[u.Keyboard=0]="Keyboard",u[u.Mouse=1]="Mouse",u);function w(e){null==e||e.focus({preventScroll:!0})}function y(e,t=e=>e){return e.slice().sort((e,n)=>{let r=t(e),o=t(n);if(null===r||null===o)return 0;let l=r.compareDocumentPosition(o);return l&Node.DOCUMENT_POSITION_FOLLOWING?-1:l&Node.DOCUMENT_POSITION_PRECEDING?1:0})}function T(e,t){return O(v(),t,{relativeTo:e})}function O(e,t,{sorted:n=!0,relativeTo:r=null,skipElements:o=[]}={}){var l,i,u;let a=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e.ownerDocument,s=Array.isArray(e)?n?y(e):e:v(e);o.length>0&&s.length>1&&(s=s.filter(e=>!o.includes(e))),r=null!=r?r:a.activeElement;let c=(()=>{if(5&t)return 1;if(10&t)return -1;throw Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),d=(()=>{if(1&t)return 0;if(2&t)return Math.max(0,s.indexOf(r))-1;if(4&t)return Math.max(0,s.indexOf(r))+1;if(8&t)return s.length-1;throw Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),f=32&t?{preventScroll:!0}:{},p=0,m=s.length,h;do{if(p>=m||p+m<=0)return 0;let e=d+p;if(16&t)e=(e+m)%m;else{if(e<0)return 3;if(e>=m)return 1}null==(h=s[e])||h.focus(f),p+=c}while(h!==a.activeElement);return 6&t&&null!=(u=null==(i=null==(l=h)?void 0:l.matches)?void 0:i.call(l,"textarea,input"))&&u&&h.select(),2}"undefined"!=typeof window&&"undefined"!=typeof document&&(document.addEventListener("keydown",e=>{e.metaKey||e.altKey||e.ctrlKey||(document.documentElement.dataset.headlessuiFocusVisible="")},!0),document.addEventListener("click",e=>{1===e.detail?delete document.documentElement.dataset.headlessuiFocusVisible:0===e.detail&&(document.documentElement.dataset.headlessuiFocusVisible="")},!0))},60597:function(e,t,n){n.d(t,{E:function(){return r}});function r(e,t,...n){if(e in t){let r=t[e];return"function"==typeof r?r(...n):r}let o=Error(`Tried to handle "${e}" but there is no handler defined. Only defined handlers are: ${Object.keys(t).map(e=>`"${e}"`).join(", ")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(o,r),o}},55195:function(e,t,n){n.d(t,{Y:function(){return r}});function r(e){"function"==typeof queueMicrotask?queueMicrotask(e):Promise.resolve().then(e).catch(e=>setTimeout(()=>{throw e}))}},54851:function(e,t,n){n.d(t,{r:function(){return o}});var r=n(52057);function o(e){return r.O.isServer?null:e instanceof Node?e.ownerDocument:null!=e&&e.hasOwnProperty("current")&&e.current instanceof Node?e.current.ownerDocument:document}},34644:function(e,t,n){function r(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function o(){return r()||/Android/gi.test(window.navigator.userAgent)}n.d(t,{gn:function(){return r},tq:function(){return o}})},11931:function(e,t,n){n.d(t,{AN:function(){return a},l4:function(){return s},sY:function(){return c},yV:function(){return m}});var r,o,l=n(2265),i=n(63960),u=n(60597),a=((r=a||{})[r.None=0]="None",r[r.RenderStrategy=1]="RenderStrategy",r[r.Static=2]="Static",r),s=((o=s||{})[o.Unmount=0]="Unmount",o[o.Hidden=1]="Hidden",o);function c({ourProps:e,theirProps:t,slot:n,defaultTag:r,features:o,visible:l=!0,name:i,mergeRefs:a}){a=null!=a?a:f;let s=p(t,e);if(l)return d(s,n,r,i,a);let c=null!=o?o:0;if(2&c){let{static:e=!1,...t}=s;if(e)return d(t,n,r,i,a)}if(1&c){let{unmount:e=!0,...t}=s;return(0,u.E)(e?0:1,{0:()=>null,1:()=>d({...t,hidden:!0,style:{display:"none"}},n,r,i,a)})}return d(s,n,r,i,a)}function d(e,t={},n,r,o){let{as:u=n,children:a,refName:s="ref",...c}=h(e,["unmount","static"]),d=void 0!==e.ref?{[s]:e.ref}:{},f="function"==typeof a?a(t):a;"className"in c&&c.className&&"function"==typeof c.className&&(c.className=c.className(t));let m={};if(t){let e=!1,n=[];for(let[r,o]of Object.entries(t))"boolean"==typeof o&&(e=!0),!0===o&&n.push(r);e&&(m["data-headlessui-state"]=n.join(" "))}if(u===l.Fragment&&Object.keys(v(c)).length>0){if(!(0,l.isValidElement)(f)||Array.isArray(f)&&f.length>1)throw Error(['Passing props on "Fragment"!',"",`The current component <${r} /> is rendering a "Fragment".`,"However we need to passthrough the following props:",Object.keys(c).map(e=>`  - ${e}`).join(`
`),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".',"Render a single element as the child so that we can forward the props onto that element."].map(e=>`  - ${e}`).join(`
`)].join(`
`));let e=f.props,t="function"==typeof(null==e?void 0:e.className)?(...t)=>(0,i.A)(null==e?void 0:e.className(...t),c.className):(0,i.A)(null==e?void 0:e.className,c.className);return(0,l.cloneElement)(f,Object.assign({},p(f.props,v(h(c,["ref"]))),m,d,{ref:o(f.ref,d.ref)},t?{className:t}:{}))}return(0,l.createElement)(u,Object.assign({},h(c,["ref"]),u!==l.Fragment&&d,u!==l.Fragment&&m),f)}function f(...e){return e.every(e=>null==e)?void 0:t=>{for(let n of e)null!=n&&("function"==typeof n?n(t):n.current=t)}}function p(...e){if(0===e.length)return{};if(1===e.length)return e[0];let t={},n={};for(let r of e)for(let e in r)e.startsWith("on")&&"function"==typeof r[e]?(null!=n[e]||(n[e]=[]),n[e].push(r[e])):t[e]=r[e];if(t.disabled||t["aria-disabled"])return Object.assign(t,Object.fromEntries(Object.keys(n).map(e=>[e,void 0])));for(let e in n)Object.assign(t,{[e](t,...r){for(let o of n[e]){if((t instanceof Event||(null==t?void 0:t.nativeEvent)instanceof Event)&&t.defaultPrevented)return;o(t,...r)}}});return t}function m(e){var t;return Object.assign((0,l.forwardRef)(e),{displayName:null!=(t=e.displayName)?t:e.name})}function v(e){let t=Object.assign({},e);for(let e in t)void 0===t[e]&&delete t[e];return t}function h(e,t=[]){let n=Object.assign({},e);for(let e of t)e in n&&delete n[e];return n}},43274:function(e,t,n){var r=n(2265);let o=r.forwardRef(function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18 18 6M6 6l12 12"}))});t.Z=o}}]);