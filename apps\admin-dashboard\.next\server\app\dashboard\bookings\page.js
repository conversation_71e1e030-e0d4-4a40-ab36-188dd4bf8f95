(()=>{var e={};e.id=907,e.ids=[907],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},6213:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>d.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>c,routeModule:()=>p,tree:()=>o});var a=r(67096),s=r(16132),i=r(37284),d=r.n(i),n=r(32564),l={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let o=["",{children:["dashboard",{children:["bookings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,77400)),"C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\dashboard\\bookings\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,68182)),"C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,62594)),"C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,16097)),"C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,25666)),"C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\not-found.tsx"]}],c=["C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\dashboard\\bookings\\page.tsx"],x="/dashboard/bookings/page",m={require:r,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/dashboard/bookings/page",pathname:"/dashboard/bookings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},16500:(e,t,r)=>{Promise.resolve().then(r.bind(r,87713))},87713:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var a=r(53854),s=r(34218),i=r(44135),d=r(49174),n=r(41350),l=r(8385),o=r(25675),c=r(38397),x=r(10817);let m=[{id:"1",clientName:"سارة أحمد",expertName:"أحمد محمد",serviceName:"تصميم موقع إلكتروني",date:"2024-01-20",time:"14:00",duration:120,amount:150,status:"confirmed",paymentStatus:"paid",createdAt:"2024-01-15"},{id:"2",clientName:"محمد علي",expertName:"فاطمة خالد",serviceName:"استشارة تقنية",date:"2024-01-22",time:"10:00",duration:60,amount:75,status:"pending",paymentStatus:"pending",createdAt:"2024-01-18"},{id:"3",clientName:"نور حسن",expertName:"خالد أحمد",serviceName:"تطوير تطبيق موبايل",date:"2024-01-25",time:"16:00",duration:180,amount:300,status:"disputed",paymentStatus:"paid",createdAt:"2024-01-10"}];function p(){let[e,t]=(0,s.useState)(m),[r,p]=(0,s.useState)(""),[u,g]=(0,s.useState)("all"),[h,y]=(0,s.useState)("all"),[b,k]=(0,s.useState)(!1),w=e.filter(e=>{let t=e.clientName.toLowerCase().includes(r.toLowerCase())||e.expertName.toLowerCase().includes(r.toLowerCase())||e.serviceName.toLowerCase().includes(r.toLowerCase()),a="all"===u||e.status===u,s="all"===h||e.paymentStatus===h;return t&&a&&s}),v=e=>a.jsx("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${{pending:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",confirmed:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",completed:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",cancelled:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",disputed:"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300"}[e]}`,children:{pending:"في الانتظار",confirmed:"مؤكد",completed:"مكتمل",cancelled:"ملغي",disputed:"متنازع عليه"}[e]}),j=e=>a.jsx("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${{pending:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",paid:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",refunded:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"}[e]}`,children:{pending:"في الانتظار",paid:"مدفوع",refunded:"مسترد"}[e]}),f=async(e,r)=>{k(!0);try{await new Promise(e=>setTimeout(e,1e3)),t(t=>t.map(t=>t.id===e?{...t,status:r}:t))}catch(e){console.error("Error updating booking status:",e)}finally{k(!1)}};return b?a.jsx(c.G,{message:"جاري تحميل الحجوزات..."}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"إدارة الحجوزات"}),a.jsx("p",{className:"mt-2 text-sm text-gray-700 dark:text-gray-300",children:"قائمة بجميع الحجوزات في منصة فريلا سوريا مع إمكانية المتابعة والإدارة"})]}),a.jsx("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg p-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-3",children:[(0,a.jsxs)("div",{className:"relative",children:[a.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:a.jsx(i.Z,{className:"h-5 w-5 text-gray-400"})}),a.jsx("input",{type:"text",placeholder:"البحث في الحجوزات...",value:r,onChange:e=>p(e.target.value),className:"block w-full pr-10 border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"})]}),a.jsx("div",{children:(0,a.jsxs)("select",{value:u,onChange:e=>g(e.target.value),className:"block w-full border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",children:[a.jsx("option",{value:"all",children:"جميع الحالات"}),a.jsx("option",{value:"pending",children:"في الانتظار"}),a.jsx("option",{value:"confirmed",children:"مؤكد"}),a.jsx("option",{value:"completed",children:"مكتمل"}),a.jsx("option",{value:"cancelled",children:"ملغي"}),a.jsx("option",{value:"disputed",children:"متنازع عليه"})]})}),a.jsx("div",{children:(0,a.jsxs)("select",{value:h,onChange:e=>y(e.target.value),className:"block w-full border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",children:[a.jsx("option",{value:"all",children:"جميع حالات الدفع"}),a.jsx("option",{value:"pending",children:"في الانتظار"}),a.jsx("option",{value:"paid",children:"مدفوع"}),a.jsx("option",{value:"refunded",children:"مسترد"})]})})]})}),0===w.length?a.jsx(x.u,{icon:a.jsx(d.Z,{className:"h-12 w-12"}),title:"لا توجد حجوزات",description:"لم يتم العثور على حجوزات تطابق معايير البحث المحددة"}):a.jsx("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden",children:a.jsx("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-700",children:[a.jsx("thead",{className:"bg-gray-50 dark:bg-gray-900",children:(0,a.jsxs)("tr",{children:[a.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"الحجز"}),a.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"الموعد"}),a.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"المبلغ"}),a.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"حالة الحجز"}),a.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"حالة الدفع"}),a.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"الإجراءات"})]})}),a.jsx("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700",children:w.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50 dark:hover:bg-gray-700",children:[a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{children:[a.jsx("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:e.serviceName}),(0,a.jsxs)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:["العميل: ",e.clientName]}),(0,a.jsxs)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:["الخبير: ",e.expertName]})]})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[a.jsx("div",{className:"text-sm text-gray-900 dark:text-white",children:e.date}),(0,a.jsxs)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:[e.time," (",e.duration," دقيقة)"]})]}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white",children:["$",e.amount]}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:v(e.status)}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:j(e.paymentStatus)}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex space-x-2 rtl:space-x-reverse",children:[a.jsx("button",{type:"button",className:"text-primary-600 hover:text-primary-900 dark:text-primary-400",title:"عرض التفاصيل",children:a.jsx(n.Z,{className:"h-4 w-4"})}),"pending"===e.status&&(0,a.jsxs)(a.Fragment,{children:[a.jsx("button",{type:"button",onClick:()=>f(e.id,"confirmed"),className:"text-green-600 hover:text-green-900 dark:text-green-400",title:"تأكيد الحجز",children:a.jsx(l.Z,{className:"h-4 w-4"})}),a.jsx("button",{type:"button",onClick:()=>f(e.id,"cancelled"),className:"text-red-600 hover:text-red-900 dark:text-red-400",title:"إلغاء الحجز",children:a.jsx(o.Z,{className:"h-4 w-4"})})]})]})})]},e.id))})]})})})]})}},10817:(e,t,r)=>{"use strict";r.d(t,{u:()=>s});var a=r(53854);function s({icon:e,title:t,description:r,action:s,className:i=""}){return(0,a.jsxs)("div",{className:`text-center py-12 ${i}`,children:[e&&a.jsx("div",{className:"mx-auto h-12 w-12 text-gray-400 dark:text-gray-500 mb-4",children:e}),a.jsx("h3",{className:"mt-2 text-sm font-medium text-gray-900 dark:text-white",children:t}),r&&a.jsx("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:r}),s&&a.jsx("div",{className:"mt-6",children:(0,a.jsxs)("button",{type:"button",onClick:s.onClick,className:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500",children:[s.icon&&a.jsx("span",{className:"ml-2 h-4 w-4",children:s.icon}),s.label]})})]})}},9687:(e,t,r)=>{"use strict";r.d(t,{T:()=>s});var a=r(53854);function s({size:e="md",color:t="primary",className:r=""}){return a.jsx("div",{className:`animate-spin ${{sm:"h-4 w-4",md:"h-6 w-6",lg:"h-8 w-8"}[e]} ${{primary:"text-primary-600",white:"text-white",gray:"text-gray-400"}[t]} ${r}`,children:(0,a.jsxs)("svg",{className:"w-full h-full",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[a.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),a.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]})})}},38397:(e,t,r)=>{"use strict";r.d(t,{G:()=>i});var a=r(53854),s=r(9687);function i({message:e="جاري التحميل...",size:t="md",className:r=""}){return(0,a.jsxs)("div",{className:`flex flex-col items-center justify-center py-12 ${r}`,children:[a.jsx(s.T,{size:t}),a.jsx("p",{className:"mt-4 text-sm text-gray-500 dark:text-gray-400",children:e})]})}},77400:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>d,__esModule:()=>i,default:()=>l});var a=r(95153);let s=(0,a.createProxy)(String.raw`C:\Users\<USER>\Documents\Freela\apps\admin-dashboard\src\app\dashboard\bookings\page.tsx`),{__esModule:i,$$typeof:d}=s,n=s.default,l=n},8385:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i});var a=r(34218);let s=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m4.5 12.75 6 6 9-13.5"}))}),i=s},41350:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i});var a=r(34218);let s=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))}),i=s},44135:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i});var a=r(34218);let s=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"}))}),i=s}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[917,301,952],()=>r(6213));module.exports=a})();