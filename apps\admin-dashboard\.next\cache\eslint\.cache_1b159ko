[{"C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\dashboard\\layout.tsx": "1", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\dashboard\\page.tsx": "2", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\layout.tsx": "3", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\page.tsx": "4", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\providers.tsx": "5", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\components\\dashboard\\RecentActivity.tsx": "6", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\components\\dashboard\\RevenueChart.tsx": "7", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\components\\dashboard\\StatsCards.tsx": "8", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\components\\dashboard\\UserGrowthChart.tsx": "9", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\components\\layout\\Header.tsx": "10", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\components\\layout\\Sidebar.tsx": "11", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\dashboard\\analytics\\page.tsx": "12", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\dashboard\\bookings\\page.tsx": "13", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\dashboard\\categories\\page.tsx": "14", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\dashboard\\disputes\\page.tsx": "15", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\dashboard\\payments\\page.tsx": "16", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\dashboard\\reports\\page.tsx": "17", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\dashboard\\services\\page.tsx": "18", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\dashboard\\settings\\page.tsx": "19", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\dashboard\\users\\page.tsx": "20", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\error.tsx": "21", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\not-found.tsx": "22", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\components\\forms\\UserForm.tsx": "23", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\components\\ui\\EmptyState.tsx": "24", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\components\\ui\\ErrorBoundary.tsx": "25", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\components\\ui\\LoadingSpinner.tsx": "26", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\components\\ui\\LoadingState.tsx": "27", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\components\\ui\\Modal.tsx": "28"}, {"size": 814, "mtime": 1749555746251, "results": "29", "hashOfConfig": "30"}, {"size": 993, "mtime": 1749555755319, "results": "31", "hashOfConfig": "30"}, {"size": 1066, "mtime": 1749559121902, "results": "32", "hashOfConfig": "30"}, {"size": 137, "mtime": 1749555737078, "results": "33", "hashOfConfig": "30"}, {"size": 1372, "mtime": 1749555709670, "results": "34", "hashOfConfig": "30"}, {"size": 3314, "mtime": 1749559018534, "results": "35", "hashOfConfig": "30"}, {"size": 2325, "mtime": 1749555851984, "results": "36", "hashOfConfig": "30"}, {"size": 2365, "mtime": 1749555824545, "results": "37", "hashOfConfig": "30"}, {"size": 2224, "mtime": 1749555838500, "results": "38", "hashOfConfig": "30"}, {"size": 4782, "mtime": 1749555807776, "results": "39", "hashOfConfig": "30"}, {"size": 5503, "mtime": 1749577263630, "results": "40", "hashOfConfig": "30"}, {"size": 11858, "mtime": 1749565040293, "results": "41", "hashOfConfig": "30"}, {"size": 12979, "mtime": 1749588405918, "results": "42", "hashOfConfig": "30"}, {"size": 22808, "mtime": 1749576991388, "results": "43", "hashOfConfig": "30"}, {"size": 18812, "mtime": 1749577215833, "results": "44", "hashOfConfig": "30"}, {"size": 16953, "mtime": 1749588468624, "results": "45", "hashOfConfig": "30"}, {"size": 17760, "mtime": 1749577099899, "results": "46", "hashOfConfig": "30"}, {"size": 11839, "mtime": 1749564990296, "results": "47", "hashOfConfig": "30"}, {"size": 26645, "mtime": 1749574893151, "results": "48", "hashOfConfig": "30"}, {"size": 16674, "mtime": 1749588482949, "results": "49", "hashOfConfig": "30"}, {"size": 1741, "mtime": 1749564696103, "results": "50", "hashOfConfig": "30"}, {"size": 1105, "mtime": 1749564684241, "results": "51", "hashOfConfig": "30"}, {"size": 9000, "mtime": 1749574624394, "results": "52", "hashOfConfig": "30"}, {"size": 1456, "mtime": 1749574572803, "results": "53", "hashOfConfig": "30"}, {"size": 3307, "mtime": 1749574589234, "results": "54", "hashOfConfig": "30"}, {"size": 1121, "mtime": 1749588325164, "results": "55", "hashOfConfig": "30"}, {"size": 534, "mtime": 1749588344439, "results": "56", "hashOfConfig": "30"}, {"size": 3091, "mtime": 1749574551050, "results": "57", "hashOfConfig": "30"}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "enzm8s", {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\dashboard\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\providers.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\components\\dashboard\\RecentActivity.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\components\\dashboard\\RevenueChart.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\components\\dashboard\\StatsCards.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\components\\dashboard\\UserGrowthChart.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\components\\layout\\Header.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\components\\layout\\Sidebar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\dashboard\\analytics\\page.tsx", ["142", "143", "144"], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\dashboard\\bookings\\page.tsx", ["145", "146", "147"], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\dashboard\\categories\\page.tsx", ["148"], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\dashboard\\disputes\\page.tsx", ["149", "150", "151", "152", "153", "154", "155", "156", "157"], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\dashboard\\payments\\page.tsx", ["158"], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\dashboard\\reports\\page.tsx", ["159", "160", "161", "162", "163"], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\dashboard\\services\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\dashboard\\settings\\page.tsx", ["164", "165"], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\dashboard\\users\\page.tsx", ["166", "167", "168", "169", "170"], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\error.tsx", ["171"], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\not-found.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\components\\forms\\UserForm.tsx", ["172", "173"], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\components\\ui\\EmptyState.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\components\\ui\\ErrorBoundary.tsx", ["174"], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\components\\ui\\LoadingSpinner.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\components\\ui\\LoadingState.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\components\\ui\\Modal.tsx", [], [], {"ruleId": "175", "severity": 2, "message": "176", "line": 17, "column": 3, "nodeType": "177", "messageId": "178", "endLine": 17, "endColumn": 11}, {"ruleId": "175", "severity": 2, "message": "179", "line": 18, "column": 3, "nodeType": "177", "messageId": "178", "endLine": 18, "endColumn": 6}, {"ruleId": "175", "severity": 2, "message": "180", "line": 263, "column": 42, "nodeType": "177", "messageId": "178", "endLine": 263, "endColumn": 47}, {"ruleId": "175", "severity": 2, "message": "181", "line": 10, "column": 3, "nodeType": "177", "messageId": "178", "endLine": 10, "endColumn": 12}, {"ruleId": "182", "severity": 1, "message": "183", "line": 138, "column": 71, "nodeType": "184", "messageId": "185", "endLine": 138, "endColumn": 74, "suggestions": "186"}, {"ruleId": "187", "severity": 1, "message": "188", "line": 141, "column": 7, "nodeType": "189", "messageId": "190", "endLine": 141, "endColumn": 20, "suggestions": "191"}, {"ruleId": "175", "severity": 2, "message": "192", "line": 109, "column": 21, "nodeType": "177", "messageId": "178", "endLine": 109, "endColumn": 33}, {"ruleId": "175", "severity": 2, "message": "193", "line": 8, "column": 3, "nodeType": "177", "messageId": "178", "endLine": 8, "endColumn": 14}, {"ruleId": "175", "severity": 2, "message": "194", "line": 114, "column": 7, "nodeType": "177", "messageId": "178", "endLine": 114, "endColumn": 19}, {"ruleId": "175", "severity": 2, "message": "192", "line": 157, "column": 21, "nodeType": "177", "messageId": "178", "endLine": 157, "endColumn": 33}, {"ruleId": "175", "severity": 2, "message": "195", "line": 160, "column": 10, "nodeType": "177", "messageId": "178", "endLine": 160, "endColumn": 26}, {"ruleId": "175", "severity": 2, "message": "196", "line": 161, "column": 10, "nodeType": "177", "messageId": "178", "endLine": 161, "endColumn": 25}, {"ruleId": "175", "severity": 2, "message": "197", "line": 162, "column": 10, "nodeType": "177", "messageId": "178", "endLine": 162, "endColumn": 18}, {"ruleId": "175", "severity": 2, "message": "198", "line": 186, "column": 9, "nodeType": "177", "messageId": "178", "endLine": 186, "endColumn": 28}, {"ruleId": "182", "severity": 1, "message": "183", "line": 323, "column": 68, "nodeType": "184", "messageId": "185", "endLine": 323, "endColumn": 71, "suggestions": "199"}, {"ruleId": "182", "severity": 1, "message": "183", "line": 342, "column": 70, "nodeType": "184", "messageId": "185", "endLine": 342, "endColumn": 73, "suggestions": "200"}, {"ruleId": "187", "severity": 1, "message": "188", "line": 144, "column": 9, "nodeType": "189", "messageId": "190", "endLine": 144, "endColumn": 22, "suggestions": "201"}, {"ruleId": "175", "severity": 2, "message": "202", "line": 12, "column": 3, "nodeType": "177", "messageId": "178", "endLine": 12, "endColumn": 10}, {"ruleId": "175", "severity": 2, "message": "192", "line": 123, "column": 21, "nodeType": "177", "messageId": "178", "endLine": 123, "endColumn": 33}, {"ruleId": "187", "severity": 1, "message": "188", "line": 150, "column": 5, "nodeType": "189", "messageId": "190", "endLine": 150, "endColumn": 16, "suggestions": "203"}, {"ruleId": "182", "severity": 1, "message": "183", "line": 270, "column": 66, "nodeType": "184", "messageId": "185", "endLine": 270, "endColumn": 69, "suggestions": "204"}, {"ruleId": "182", "severity": 1, "message": "183", "line": 288, "column": 71, "nodeType": "184", "messageId": "185", "endLine": 288, "endColumn": 74, "suggestions": "205"}, {"ruleId": "187", "severity": 1, "message": "188", "line": 109, "column": 7, "nodeType": "189", "messageId": "190", "endLine": 109, "endColumn": 20, "suggestions": "206"}, {"ruleId": "182", "severity": 1, "message": "183", "line": 116, "column": 80, "nodeType": "184", "messageId": "185", "endLine": 116, "endColumn": 83, "suggestions": "207"}, {"ruleId": "175", "severity": 2, "message": "208", "line": 6, "column": 3, "nodeType": "177", "messageId": "178", "endLine": 6, "endColumn": 13}, {"ruleId": "175", "severity": 2, "message": "209", "line": 15, "column": 10, "nodeType": "177", "messageId": "178", "endLine": 15, "endColumn": 20}, {"ruleId": "187", "severity": 1, "message": "188", "line": 108, "column": 9, "nodeType": "189", "messageId": "190", "endLine": 108, "endColumn": 22, "suggestions": "210"}, {"ruleId": "187", "severity": 1, "message": "188", "line": 125, "column": 9, "nodeType": "189", "messageId": "190", "endLine": 125, "endColumn": 22, "suggestions": "211"}, {"ruleId": "187", "severity": 1, "message": "188", "line": 147, "column": 7, "nodeType": "189", "messageId": "190", "endLine": 147, "endColumn": 20, "suggestions": "212"}, {"ruleId": "187", "severity": 1, "message": "188", "line": 13, "column": 5, "nodeType": "189", "messageId": "190", "endLine": 13, "endColumn": 18, "suggestions": "213"}, {"ruleId": "187", "severity": 1, "message": "188", "line": 76, "column": 7, "nodeType": "189", "messageId": "190", "endLine": 76, "endColumn": 20, "suggestions": "214"}, {"ruleId": "182", "severity": 1, "message": "183", "line": 82, "column": 56, "nodeType": "184", "messageId": "185", "endLine": 82, "endColumn": 59, "suggestions": "215"}, {"ruleId": "187", "severity": 1, "message": "188", "line": 26, "column": 5, "nodeType": "189", "messageId": "190", "endLine": 26, "endColumn": 18, "suggestions": "216"}, "@typescript-eslint/no-unused-vars", "'BarChart' is defined but never used.", "Identifier", "unusedVar", "'Bar' is defined but never used.", "'index' is defined but never used. Allowed unused args must match /^_/u.", "'ClockIcon' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["217", "218"], "no-console", "Unexpected console statement.", "MemberExpression", "unexpected", ["219"], "'setIsLoading' is assigned a value but never used.", "'XCircleIcon' is defined but never used.", "'statusLabels' is assigned a value but never used.", "'showDisputeModal' is assigned a value but never used.", "'selectedDispute' is assigned a value but never used.", "'formMode' is assigned a value but never used.", "'handleAssignDispute' is assigned a value but never used.", ["220", "221"], ["222", "223"], ["224"], "'EyeIcon' is defined but never used.", ["225"], ["226", "227"], ["228", "229"], ["230"], ["231", "232"], "'FunnelIcon' is defined but never used.", "'EmptyState' is defined but never used.", ["233"], ["234"], ["235"], ["236"], ["237"], ["238", "239"], ["240"], {"messageId": "241", "fix": "242", "desc": "243"}, {"messageId": "244", "fix": "245", "desc": "246"}, {"messageId": "247", "data": "248", "fix": "249", "desc": "250"}, {"messageId": "241", "fix": "251", "desc": "243"}, {"messageId": "244", "fix": "252", "desc": "246"}, {"messageId": "241", "fix": "253", "desc": "243"}, {"messageId": "244", "fix": "254", "desc": "246"}, {"messageId": "247", "data": "255", "fix": "256", "desc": "250"}, {"messageId": "247", "data": "257", "fix": "258", "desc": "259"}, {"messageId": "241", "fix": "260", "desc": "243"}, {"messageId": "244", "fix": "261", "desc": "246"}, {"messageId": "241", "fix": "262", "desc": "243"}, {"messageId": "244", "fix": "263", "desc": "246"}, {"messageId": "247", "data": "264", "fix": "265", "desc": "250"}, {"messageId": "241", "fix": "266", "desc": "243"}, {"messageId": "244", "fix": "267", "desc": "246"}, {"messageId": "247", "data": "268", "fix": "269", "desc": "250"}, {"messageId": "247", "data": "270", "fix": "271", "desc": "250"}, {"messageId": "247", "data": "272", "fix": "273", "desc": "250"}, {"messageId": "247", "data": "274", "fix": "275", "desc": "250"}, {"messageId": "247", "data": "276", "fix": "277", "desc": "250"}, {"messageId": "241", "fix": "278", "desc": "243"}, {"messageId": "244", "fix": "279", "desc": "246"}, {"messageId": "247", "data": "280", "fix": "281", "desc": "250"}, "suggestUnknown", {"range": "282", "text": "283"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "284", "text": "285"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "removeConsole", {"propertyName": "286"}, {"range": "287", "text": "288"}, "Remove the console.error().", {"range": "289", "text": "283"}, {"range": "290", "text": "285"}, {"range": "291", "text": "283"}, {"range": "292", "text": "285"}, {"propertyName": "286"}, {"range": "293", "text": "288"}, {"propertyName": "294"}, {"range": "295", "text": "288"}, "Remove the console.log().", {"range": "296", "text": "283"}, {"range": "297", "text": "285"}, {"range": "298", "text": "283"}, {"range": "299", "text": "285"}, {"propertyName": "286"}, {"range": "300", "text": "288"}, {"range": "301", "text": "283"}, {"range": "302", "text": "285"}, {"propertyName": "286"}, {"range": "303", "text": "288"}, {"propertyName": "286"}, {"range": "304", "text": "288"}, {"propertyName": "286"}, {"range": "305", "text": "288"}, {"propertyName": "286"}, {"range": "306", "text": "288"}, {"propertyName": "286"}, {"range": "307", "text": "288"}, {"range": "308", "text": "283"}, {"range": "309", "text": "285"}, {"propertyName": "286"}, {"range": "310", "text": "288"}, [4384, 4387], "unknown", [4384, 4387], "never", "error", [4438, 4493], "", [10810, 10813], [10810, 10813], [11686, 11689], [11686, 11689], [4587, 4636], "log", [4532, 4589], [9073, 9076], [9073, 9076], [9900, 9903], [9900, 9903], [2688, 2735], [2914, 2917], [2914, 2917], [2986, 3031], [3518, 3565], [4144, 4191], [194, 215], [1941, 1988], [2099, 2102], [2099, 2102], [561, 627]]