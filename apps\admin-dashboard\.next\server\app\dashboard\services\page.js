(()=>{var e={};e.id=382,e.ids=[382],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},40804:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>o});var a=t(67096),s=t(16132),n=t(37284),i=t.n(n),d=t(32564),l={};for(let e in d)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>d[e]);t.d(r,l);let o=["",{children:["dashboard",{children:["services",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,22313)),"C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\dashboard\\services\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,68182)),"C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,62594)),"C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,16097)),"C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,25666)),"C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\not-found.tsx"]}],c=["C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\dashboard\\services\\page.tsx"],m="/dashboard/services/page",x={require:t,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/dashboard/services/page",pathname:"/dashboard/services",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},20521:(e,r,t)=>{Promise.resolve().then(t.bind(t,32659))},32659:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>x});var a=t(53854),s=t(34218),n=t(44135);let i=s.forwardRef(function({title:e,titleId:r,...t},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},t),e?s.createElement("title",{id:r},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M11.48 3.499a.562.562 0 0 1 1.04 0l2.125 5.111a.563.563 0 0 0 .475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 0 0-.182.557l1.285 5.385a.562.562 0 0 1-.84.61l-4.725-2.885a.562.562 0 0 0-.586 0L6.982 20.54a.562.562 0 0 1-.84-.61l1.285-5.386a.562.562 0 0 0-.182-.557l-4.204-3.602a.562.562 0 0 1 .321-.988l5.518-.442a.563.563 0 0 0 .475-.345L11.48 3.5Z"}))});var d=t(41350),l=t(8385),o=t(25675),c=t(654);let m=[{id:"1",title:"تصميم مواقع الويب الاحترافية",description:"تصميم وتطوير مواقع ويب حديثة ومتجاوبة باستخدام أحدث التقنيات",category:"تطوير الويب",expert:{name:"أحمد محمد"},price:500,status:"active",featured:!0,rating:4.8,reviewCount:24,createdAt:"2024-01-15"},{id:"2",title:"تطوير تطبيقات الموبايل",description:"تطوير تطبيقات iOS و Android باستخدام React Native",category:"تطوير التطبيقات",expert:{name:"فاطمة أحمد"},price:800,status:"pending",featured:!1,rating:4.9,reviewCount:18,createdAt:"2024-01-20"},{id:"3",title:"استشارات تقنية متخصصة",description:"استشارات في مجال التكنولوجيا والحلول الرقمية",category:"استشارات",expert:{name:"محمد علي"},price:100,status:"rejected",featured:!1,rating:4.5,reviewCount:12,createdAt:"2024-01-18"}];function x(){let[e,r]=(0,s.useState)(m),[t,x]=(0,s.useState)(""),[u,p]=(0,s.useState)("all"),[g,h]=(0,s.useState)("all"),v=e.filter(e=>{let r=e.title.toLowerCase().includes(t.toLowerCase())||e.expert.name.toLowerCase().includes(t.toLowerCase()),a="all"===u||e.status===u,s="all"===g||e.category===g;return r&&a&&s}),b=e=>a.jsx("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${{active:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",pending:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",rejected:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",suspended:"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"}[e]}`,children:{active:"نشط",pending:"في الانتظار",rejected:"مرفوض",suspended:"معلق"}[e]}),y=t=>{r(e.map(e=>e.id===t?{...e,status:"active"}:e))},f=t=>{r(e.map(e=>e.id===t?{...e,status:"rejected"}:e))};return(0,a.jsxs)("div",{className:"space-y-6",children:[a.jsx("div",{className:"sm:flex sm:items-center",children:(0,a.jsxs)("div",{className:"sm:flex-auto",children:[a.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"إدارة الخدمات"}),a.jsx("p",{className:"mt-2 text-sm text-gray-700 dark:text-gray-300",children:"إدارة جميع الخدمات المنشورة على المنصة مع إمكانية الموافقة والرفض والتعديل"})]})}),a.jsx("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg p-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-3",children:[(0,a.jsxs)("div",{className:"relative",children:[a.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:a.jsx(n.Z,{className:"h-5 w-5 text-gray-400"})}),a.jsx("input",{type:"text",placeholder:"البحث عن خدمة...",value:t,onChange:e=>x(e.target.value),className:"block w-full pr-10 border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"})]}),a.jsx("div",{children:(0,a.jsxs)("select",{value:u,onChange:e=>p(e.target.value),className:"block w-full border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",children:[a.jsx("option",{value:"all",children:"جميع الحالات"}),a.jsx("option",{value:"active",children:"نشط"}),a.jsx("option",{value:"pending",children:"في الانتظار"}),a.jsx("option",{value:"rejected",children:"مرفوض"}),a.jsx("option",{value:"suspended",children:"معلق"})]})}),a.jsx("div",{children:(0,a.jsxs)("select",{value:g,onChange:e=>h(e.target.value),className:"block w-full border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",children:[a.jsx("option",{value:"all",children:"جميع الفئات"}),a.jsx("option",{value:"تطوير الويب",children:"تطوير الويب"}),a.jsx("option",{value:"تطوير التطبيقات",children:"تطوير التطبيقات"}),a.jsx("option",{value:"استشارات",children:"استشارات"}),a.jsx("option",{value:"تصميم",children:"تصميم"})]})})]})}),a.jsx("div",{className:"grid grid-cols-1 gap-6 lg:grid-cols-2 xl:grid-cols-3",children:v.map(e=>a.jsx("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white line-clamp-2",children:[e.title,e.featured&&a.jsx(i,{className:"inline h-4 w-4 text-yellow-400 mr-1"})]}),a.jsx("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:e.category})]}),a.jsx("div",{className:"mr-4",children:b(e.status)})]}),a.jsx("p",{className:"mt-3 text-sm text-gray-600 dark:text-gray-300 line-clamp-3",children:e.description}),(0,a.jsxs)("div",{className:"mt-4 flex items-center",children:[a.jsx("div",{className:"h-8 w-8 rounded-full bg-primary-500 flex items-center justify-center",children:a.jsx("span",{className:"text-xs font-medium text-white",children:e.expert.name.charAt(0)})}),a.jsx("div",{className:"mr-3",children:a.jsx("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:e.expert.name})})]}),(0,a.jsxs)("div",{className:"mt-4 flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx(i,{className:"h-4 w-4 text-yellow-400"}),(0,a.jsxs)("span",{className:"mr-1 text-sm text-gray-600 dark:text-gray-300",children:[e.rating," (",e.reviewCount,")"]})]}),(0,a.jsxs)("div",{className:"text-lg font-bold text-primary-600 dark:text-primary-400",children:["$",e.price]})]}),(0,a.jsxs)("div",{className:"mt-6 flex space-x-3 rtl:space-x-reverse",children:[(0,a.jsxs)("button",{className:"flex-1 inline-flex justify-center items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600",children:[a.jsx(d.Z,{className:"h-4 w-4 ml-2"}),"عرض"]}),"pending"===e.status&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("button",{onClick:()=>y(e.id),className:"flex-1 inline-flex justify-center items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700",children:[a.jsx(l.Z,{className:"h-4 w-4 ml-2"}),"موافقة"]}),(0,a.jsxs)("button",{onClick:()=>f(e.id),className:"flex-1 inline-flex justify-center items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700",children:[a.jsx(o.Z,{className:"h-4 w-4 ml-2"}),"رفض"]})]}),"pending"!==e.status&&(0,a.jsxs)("button",{className:"flex-1 inline-flex justify-center items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600",children:[a.jsx(c.Z,{className:"h-4 w-4 ml-2"}),"تعديل"]})]})]})},e.id))}),0===v.length&&(0,a.jsxs)("div",{className:"text-center py-12",children:[a.jsx("div",{className:"text-gray-400 dark:text-gray-600",children:a.jsx("svg",{className:"mx-auto h-12 w-12",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),a.jsx("h3",{className:"mt-2 text-sm font-medium text-gray-900 dark:text-white",children:"لا توجد خدمات"}),a.jsx("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"لم يتم العثور على خدمات تطابق معايير البحث."})]})]})}},22313:(e,r,t)=>{"use strict";t.r(r),t.d(r,{$$typeof:()=>i,__esModule:()=>n,default:()=>l});var a=t(95153);let s=(0,a.createProxy)(String.raw`C:\Users\<USER>\Documents\Freela\apps\admin-dashboard\src\app\dashboard\services\page.tsx`),{__esModule:n,$$typeof:i}=s,d=s.default,l=d},8385:(e,r,t)=>{"use strict";t.d(r,{Z:()=>n});var a=t(34218);let s=a.forwardRef(function({title:e,titleId:r,...t},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":r},t),e?a.createElement("title",{id:r},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m4.5 12.75 6 6 9-13.5"}))}),n=s},41350:(e,r,t)=>{"use strict";t.d(r,{Z:()=>n});var a=t(34218);let s=a.forwardRef(function({title:e,titleId:r,...t},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":r},t),e?a.createElement("title",{id:r},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))}),n=s},44135:(e,r,t)=>{"use strict";t.d(r,{Z:()=>n});var a=t(34218);let s=a.forwardRef(function({title:e,titleId:r,...t},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":r},t),e?a.createElement("title",{id:r},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"}))}),n=s},654:(e,r,t)=>{"use strict";t.d(r,{Z:()=>n});var a=t(34218);let s=a.forwardRef(function({title:e,titleId:r,...t},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":r},t),e?a.createElement("title",{id:r},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125"}))}),n=s}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[917,301,952],()=>t(40804));module.exports=a})();