# 🚀 Freela Syria - Backend API

> خادم API الخلفي الشامل لمنصة فريلا سوريا

## 📋 نظرة عامة

خادم API الخلفي لمنصة فريلا سوريا مبني باستخدام Node.js و Express.js مع TypeScript. يوفر جميع الخدمات المطلوبة للمنصة بما في ذلك المصادقة، إدارة المستخدمين، الخدمات، الحجوزات، والمدفوعات.

## ✨ الميزات الرئيسية

### 🔐 نظام المصادقة
- **JWT Authentication**: مصادقة آمنة باستخدام JSON Web Tokens
- **Refresh Tokens**: رموز تحديث للجلسات طويلة المدى
- **Role-based Access**: تحكم في الوصول حسب الأدوار
- **Password Security**: تشفير كلمات المرور باستخدام bcrypt

### 👥 إدارة المستخدمين
- **User Registration**: تسجيل المستخدمين الجدد
- **Profile Management**: إدارة الملفات الشخصية
- **Expert Profiles**: ملفات متخصصة للخبراء
- **Client Profiles**: ملفات العملاء

### 🛠️ إدارة الخدمات
- **Service Catalog**: كتالوج شامل للخدمات
- **Category Management**: إدارة فئات الخدمات
- **Service Approval**: نظام الموافقة على الخدمات
- **Search & Filtering**: بحث وتصفية متقدم

### 📅 نظام الحجوزات
- **Booking Management**: إدارة الحجوزات
- **Status Tracking**: تتبع حالة الحجوزات
- **Scheduling**: جدولة المواعيد
- **Notifications**: إشعارات الحجوزات

### 💰 نظام المدفوعات
- **Payment Processing**: معالجة المدفوعات
- **Transaction History**: تاريخ المعاملات
- **Refund Management**: إدارة المبالغ المستردة
- **Financial Reports**: التقارير المالية

### 💬 نظام المراسلة
- **Real-time Chat**: محادثات فورية
- **Message History**: تاريخ الرسائل
- **File Sharing**: مشاركة الملفات
- **Notifications**: إشعارات الرسائل

## 🛠️ المكدس التقني

### Backend Framework
- **Node.js 18+**: بيئة تشغيل JavaScript
- **Express.js**: إطار عمل الخادم
- **TypeScript**: لغة البرمجة المكتوبة
- **Prisma ORM**: أداة إدارة قاعدة البيانات

### Database & Cache
- **PostgreSQL**: قاعدة البيانات الرئيسية
- **Redis**: ذاكرة التخزين المؤقت
- **Prisma Client**: عميل قاعدة البيانات

### Security & Validation
- **JWT**: JSON Web Tokens للمصادقة
- **bcryptjs**: تشفير كلمات المرور
- **Helmet**: رؤوس الأمان
- **CORS**: حماية CORS
- **Express Validator**: التحقق من صحة البيانات

### Utilities
- **Winston**: نظام السجلات
- **Multer**: رفع الملفات
- **Nodemailer**: إرسال البريد الإلكتروني
- **Swagger**: توثيق API

## 🚀 البدء السريع

### المتطلبات الأساسية
```bash
Node.js >= 18.0.0
npm >= 9.0.0
PostgreSQL >= 13
Redis >= 6.0
```

### التثبيت والتشغيل
```bash
# الانتقال إلى مجلد API
cd apps/api

# تثبيت التبعيات
npm install

# إعداد متغيرات البيئة
cp .env.example .env

# تشغيل الترحيلات
npm run db:migrate

# ملء قاعدة البيانات بالبيانات التجريبية
npm run db:seed

# تشغيل خادم التطوير
npm run dev

# الخادم يعمل على
http://localhost:3000
```

### البناء للإنتاج
```bash
# بناء التطبيق
npm run build

# تشغيل النسخة المبنية
npm start
```

## 📁 هيكل المشروع

```
src/
├── app.ts                 # إعداد التطبيق الرئيسي
├── index.ts              # نقطة دخول الخادم
├── config/               # إعدادات التطبيق
│   ├── database.ts       # إعداد قاعدة البيانات
│   ├── redis.ts          # إعداد Redis
│   └── swagger.ts        # إعداد Swagger
├── controllers/          # متحكمات API
│   ├── auth.ts          # متحكم المصادقة
│   ├── users.ts         # متحكم المستخدمين
│   ├── services.ts      # متحكم الخدمات
│   ├── bookings.ts      # متحكم الحجوزات
│   └── payments.ts      # متحكم المدفوعات
├── middleware/           # Middleware functions
│   ├── auth.ts          # مصادقة المستخدم
│   ├── validation.ts    # التحقق من البيانات
│   ├── security.ts      # الأمان
│   └── logging.ts       # السجلات
├── routes/              # مسارات API
│   ├── auth.ts          # مسارات المصادقة
│   ├── users.ts         # مسارات المستخدمين
│   ├── services.ts      # مسارات الخدمات
│   ├── bookings.ts      # مسارات الحجوزات
│   └── payments.ts      # مسارات المدفوعات
├── services/            # خدمات الأعمال
│   ├── auth.service.ts  # خدمة المصادقة
│   ├── user.service.ts  # خدمة المستخدمين
│   ├── email.service.ts # خدمة البريد الإلكتروني
│   └── upload.service.ts# خدمة رفع الملفات
├── types/               # تعريفات TypeScript
│   ├── auth.ts          # أنواع المصادقة
│   ├── user.ts          # أنواع المستخدمين
│   └── api.ts           # أنواع API
└── utils/               # وظائف مساعدة
    ├── jwt.ts           # أدوات JWT
    ├── validation.ts    # أدوات التحقق
    └── helpers.ts       # وظائف مساعدة عامة
```

## 📡 نقاط النهاية (API Endpoints)

### 🔐 المصادقة (`/api/v1/auth`)
```
POST   /register         # تسجيل مستخدم جديد
POST   /login           # تسجيل الدخول
POST   /logout          # تسجيل الخروج
POST   /refresh         # تحديث الرمز المميز
POST   /forgot-password # نسيان كلمة المرور
POST   /reset-password  # إعادة تعيين كلمة المرور
GET    /verify-email    # التحقق من البريد الإلكتروني
```

### 👥 المستخدمون (`/api/v1/users`)
```
GET    /               # قائمة المستخدمين
GET    /:id            # تفاصيل المستخدم
PUT    /:id            # تحديث المستخدم
DELETE /:id            # حذف المستخدم
POST   /:id/suspend    # تعليق المستخدم
POST   /:id/activate   # تفعيل المستخدم
```

### 🛠️ الخدمات (`/api/v1/services`)
```
GET    /               # قائمة الخدمات
POST   /               # إنشاء خدمة جديدة
GET    /:id            # تفاصيل الخدمة
PUT    /:id            # تحديث الخدمة
DELETE /:id            # حذف الخدمة
POST   /:id/approve    # الموافقة على الخدمة
POST   /:id/reject     # رفض الخدمة
```

### 📅 الحجوزات (`/api/v1/bookings`)
```
GET    /               # قائمة الحجوزات
POST   /               # إنشاء حجز جديد
GET    /:id            # تفاصيل الحجز
PUT    /:id/status     # تحديث حالة الحجز
POST   /:id/cancel     # إلغاء الحجز
POST   /:id/complete   # إكمال الحجز
```

### 💰 المدفوعات (`/api/v1/payments`)
```
GET    /               # قائمة المدفوعات
POST   /               # معالجة الدفع
GET    /:id            # تفاصيل الدفع
POST   /:id/refund     # استرداد المبلغ
GET    /methods        # طرق الدفع المتاحة
```

## 🔧 إعداد متغيرات البيئة

### ملف `.env`
```env
# قاعدة البيانات
DATABASE_URL="postgresql://freela:password@localhost:5432/freela_syria"

# Redis
REDIS_URL="redis://localhost:6379"

# JWT
JWT_SECRET="your-super-secret-jwt-key-here"
JWT_REFRESH_SECRET="your-super-secret-refresh-key-here"
JWT_EXPIRES_IN="15m"
JWT_REFRESH_EXPIRES_IN="7d"

# API
API_VERSION="v1"
PORT=3000
NODE_ENV="development"

# البريد الإلكتروني
SMTP_HOST="localhost"
SMTP_PORT=1025
SMTP_USER=""
SMTP_PASS=""
FROM_EMAIL="<EMAIL>"

# رفع الملفات
UPLOAD_DIR="./uploads"
MAX_FILE_SIZE="10485760"  # 10MB

# الأمان
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000  # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100

# التطوير
LOG_LEVEL="debug"
ENABLE_SWAGGER=true
```

## 🧪 الاختبار

### تشغيل الاختبارات
```bash
# اختبارات الوحدة
npm test

# اختبارات مع تغطية الكود
npm run test:coverage

# اختبارات التكامل
npm run test:integration

# اختبارات E2E
npm run test:e2e
```

### فحص جودة الكود
```bash
# فحص ESLint
npm run lint

# إصلاح مشاكل ESLint
npm run lint:fix

# فحص TypeScript
npm run type-check
```

## 📊 المراقبة والسجلات

### نظام السجلات
- **Winston Logger**: سجلات منظمة ومفصلة
- **Log Levels**: Debug, Info, Warn, Error
- **File Rotation**: تدوير ملفات السجلات
- **Error Tracking**: تتبع الأخطاء

### المراقبة
- **Health Checks**: فحص صحة النظام
- **Performance Metrics**: مؤشرات الأداء
- **Database Monitoring**: مراقبة قاعدة البيانات
- **Redis Monitoring**: مراقبة Redis

## 🔒 الأمان

### إجراءات الأمان
- **Input Validation**: التحقق من صحة المدخلات
- **SQL Injection Prevention**: منع حقن SQL
- **XSS Protection**: حماية من XSS
- **CSRF Protection**: حماية من CSRF
- **Rate Limiting**: تحديد معدل الطلبات
- **Secure Headers**: رؤوس أمان HTTP

### التشفير
- **Password Hashing**: تشفير كلمات المرور
- **JWT Signing**: توقيع JWT
- **Data Encryption**: تشفير البيانات الحساسة
- **HTTPS**: اتصال آمن

## 📚 التوثيق

### Swagger Documentation
- **API Documentation**: توثيق شامل لجميع نقاط النهاية
- **Interactive Testing**: اختبار تفاعلي للAPI
- **Schema Definitions**: تعريفات المخططات
- **Examples**: أمثلة على الطلبات والاستجابات

### الوصول للتوثيق
```
http://localhost:3000/api/v1/docs
```

## 🚀 النشر

### بناء الإنتاج
```bash
npm run build
```

### Docker
```bash
# بناء الصورة
docker build -t freela-api .

# تشغيل الحاوية
docker run -p 3000:3000 freela-api
```

## 📞 الدعم والمساعدة

### الوثائق
- [Node.js Documentation](https://nodejs.org/docs)
- [Express.js](https://expressjs.com)
- [Prisma](https://www.prisma.io/docs)
- [TypeScript](https://www.typescriptlang.org/docs)

### المساهمة
1. Fork المشروع
2. إنشاء فرع للميزة الجديدة
3. Commit التغييرات
4. Push إلى الفرع
5. فتح Pull Request

---

**🎯 خادم API قوي وآمن لدعم منصة فريلا سوريا**

*آخر تحديث: ديسمبر 2024*
