# 📝 @freela/types

> حزمة أنواع TypeScript المشتركة لمنصة فريلا سوريا

## 📋 نظرة عامة

حزمة `@freela/types` تحتوي على جميع تعريفات أنواع TypeScript المشتركة عبر تطبيقات منصة فريلا سوريا. تضمن الاتساق في أنواع البيانات وتوفر type safety عبر جميع المكونات.

## ✨ الميزات الرئيسية

### 🔧 أنواع شاملة
- **User Types**: أنواع المستخدمين والأدوار
- **Service Types**: أنواع الخدمات والفئات
- **Booking Types**: أنواع الحجوزات والحالات
- **Payment Types**: أنواع المدفوعات والمعاملات
- **API Types**: أنواع طلبات واستجابات API
- **UI Types**: أنواع مكونات واجهة المستخدم

### 🛡️ Type Safety
- **Strict Typing**: أنواع صارمة لجميع البيانات
- **Interface Definitions**: تعريفات واجهات شاملة
- **Enum Types**: تعدادات للقيم الثابتة
- **Generic Types**: أنواع عامة قابلة لإعادة الاستخدام

### 🔄 التوافق
- **Cross-Platform**: متوافق مع جميع التطبيقات
- **Version Consistency**: ضمان توافق الإصدارات
- **Export Organization**: تنظيم الصادرات بشكل منطقي

## 🛠️ المكدس التقني

### Core
- **TypeScript 5+**: لغة البرمجة المكتوبة
- **Type Definitions**: تعريفات أنواع شاملة
- **Interface Design**: تصميم واجهات محكم

## 🚀 التثبيت والاستخدام

### التثبيت
```bash
# في مجلد المشروع الرئيسي
npm install

# أو تثبيت الحزمة مباشرة
npm install @freela/types
```

### الاستخدام
```typescript
import { 
  User, 
  Service, 
  Booking, 
  ApiResponse 
} from '@freela/types';

// مثال على استخدام الأنواع
const user: User = {
  id: '1',
  email: '<EMAIL>',
  firstName: 'أحمد',
  lastName: 'محمد',
  role: 'EXPERT',
  isActive: true,
};

const apiResponse: ApiResponse<User> = {
  success: true,
  data: user,
  message: 'تم جلب البيانات بنجاح',
};
```

## 📁 هيكل الحزمة

```
packages/types/
├── src/                   # الكود المصدري
│   ├── index.ts          # نقطة الدخول الرئيسية
│   ├── user.ts           # أنواع المستخدمين
│   ├── expert.ts         # أنواع الخبراء
│   ├── client.ts         # أنواع العملاء
│   ├── service.ts        # أنواع الخدمات
│   ├── booking.ts        # أنواع الحجوزات
│   ├── payment.ts        # أنواع المدفوعات
│   ├── chat.ts           # أنواع المراسلة
│   ├── admin.ts          # أنواع الإدارة
│   ├── api.ts            # أنواع API
│   ├── ui.ts             # أنواع واجهة المستخدم
│   └── i18n.ts           # أنواع الترجمة
├── dist/                 # الملفات المبنية
├── package.json          # إعدادات الحزمة
└── tsconfig.json         # إعدادات TypeScript
```

## 📊 تعريفات الأنواع

### 👤 User Types
```typescript
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  phone?: string;
  avatar?: string;
  role: UserRole;
  isEmailVerified: boolean;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export enum UserRole {
  CLIENT = 'CLIENT',
  EXPERT = 'EXPERT',
  ADMIN = 'ADMIN',
}

export interface UserProfile {
  user: User;
  expertProfile?: ExpertProfile;
  clientProfile?: ClientProfile;
}
```

### 👨‍💻 Expert Types
```typescript
export interface ExpertProfile {
  id: string;
  userId: string;
  title: string;
  bio?: string;
  skills: string[];
  experience: number;
  hourlyRate?: number;
  availability?: string;
  languages: string[];
  portfolio?: Portfolio[];
  certifications?: Certification[];
  rating: number;
  totalReviews: number;
  totalEarnings: number;
  isVerified: boolean;
}

export interface Portfolio {
  id: string;
  title: string;
  description: string;
  images: string[];
  technologies: string[];
  url?: string;
}

export interface Certification {
  id: string;
  name: string;
  issuer: string;
  date: Date;
  url?: string;
}
```

### 🛠️ Service Types
```typescript
export interface Service {
  id: string;
  expertId: string;
  categoryId: string;
  title: string;
  description: string;
  price: number;
  duration: number;
  deliveryTime: number;
  requirements?: string;
  features: string[];
  images: string[];
  status: ServiceStatus;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export enum ServiceStatus {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  INACTIVE = 'INACTIVE',
}

export interface ServiceCategory {
  id: string;
  name: string;
  description?: string;
  icon?: string;
  parentId?: string;
  isActive: boolean;
}
```

### 📅 Booking Types
```typescript
export interface Booking {
  id: string;
  clientId: string;
  expertId: string;
  serviceId: string;
  title: string;
  description?: string;
  budget: number;
  deadline?: Date;
  status: BookingStatus;
  requirements?: any;
  deliverables?: any;
  createdAt: Date;
  updatedAt: Date;
}

export enum BookingStatus {
  PENDING = 'PENDING',
  ACCEPTED = 'ACCEPTED',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  DISPUTED = 'DISPUTED',
}

export interface BookingWithDetails extends Booking {
  client: User;
  expert: ExpertProfile;
  service: Service;
  payments: Payment[];
}
```

### 💰 Payment Types
```typescript
export interface Payment {
  id: string;
  bookingId: string;
  amount: number;
  currency: string;
  status: PaymentStatus;
  method?: string;
  transactionId?: string;
  processedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export enum PaymentStatus {
  PENDING = 'PENDING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  REFUNDED = 'REFUNDED',
}

export interface PaymentMethod {
  id: string;
  name: string;
  type: 'CARD' | 'BANK' | 'WALLET';
  isActive: boolean;
}
```

### 💬 Chat Types
```typescript
export interface Message {
  id: string;
  conversationId: string;
  senderId: string;
  content: string;
  type: MessageType;
  attachments?: Attachment[];
  isRead: boolean;
  createdAt: Date;
}

export enum MessageType {
  TEXT = 'TEXT',
  FILE = 'FILE',
  IMAGE = 'IMAGE',
  SYSTEM = 'SYSTEM',
}

export interface Conversation {
  id: string;
  participants: string[];
  lastMessage?: Message;
  unreadCount: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface Attachment {
  id: string;
  name: string;
  url: string;
  size: number;
  type: string;
}
```

### 📡 API Types
```typescript
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  errors?: ValidationError[];
}

export interface PaginatedResponse<T = any> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface ValidationError {
  field: string;
  message: string;
}

export interface ApiRequest {
  headers?: Record<string, string>;
  params?: Record<string, any>;
  query?: Record<string, any>;
  body?: any;
}
```

### 🎨 UI Types
```typescript
export interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  disabled?: boolean;
  children: React.ReactNode;
  onClick?: () => void;
}

export interface InputProps {
  label?: string;
  placeholder?: string;
  error?: string;
  required?: boolean;
  disabled?: boolean;
  value?: string;
  onChange?: (value: string) => void;
}

export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl';
}

export interface TableColumn<T = any> {
  key: keyof T;
  title: string;
  render?: (value: any, record: T) => React.ReactNode;
  sortable?: boolean;
  width?: string;
}
```

### 🌍 i18n Types
```typescript
export interface TranslationKeys {
  common: {
    save: string;
    cancel: string;
    delete: string;
    edit: string;
    loading: string;
    error: string;
    success: string;
  };
  auth: {
    login: string;
    register: string;
    logout: string;
    forgotPassword: string;
  };
  dashboard: {
    overview: string;
    services: string;
    bookings: string;
    earnings: string;
  };
}

export type Language = 'ar' | 'en';

export interface LocaleConfig {
  code: Language;
  name: string;
  dir: 'ltr' | 'rtl';
  flag: string;
}
```

## 🔧 الأوامر المتاحة

### التطوير
```bash
# بناء الحزمة
npm run build

# مراقبة التغييرات
npm run dev

# فحص الأنواع
npm run type-check

# فحص ESLint
npm run lint
```

## 🧪 الاختبار

### تشغيل الاختبارات
```bash
npm test
```

### اختبارات الأنواع
- **Type Validation**: التحقق من صحة الأنواع
- **Interface Compatibility**: توافق الواجهات
- **Export Tests**: اختبار الصادرات

## 📚 أمثلة الاستخدام

### في تطبيق React
```typescript
import { User, Service, ApiResponse } from '@freela/types';

interface UserCardProps {
  user: User;
}

const UserCard: React.FC<UserCardProps> = ({ user }) => {
  return (
    <div>
      <h3>{user.firstName} {user.lastName}</h3>
      <p>{user.email}</p>
    </div>
  );
};
```

### في API Controller
```typescript
import { ApiResponse, User, CreateUserRequest } from '@freela/types';

export const createUser = async (
  req: CreateUserRequest
): Promise<ApiResponse<User>> => {
  try {
    const user = await userService.create(req.body);
    return {
      success: true,
      data: user,
      message: 'تم إنشاء المستخدم بنجاح',
    };
  } catch (error) {
    return {
      success: false,
      error: 'فشل في إنشاء المستخدم',
    };
  }
};
```

## 📞 الدعم والمساعدة

### الوثائق
- [TypeScript Documentation](https://www.typescriptlang.org/docs)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/handbook/intro.html)

### المساهمة
1. Fork المشروع
2. إنشاء فرع للميزة الجديدة
3. Commit التغييرات
4. Push إلى الفرع
5. فتح Pull Request

---

**🎯 أنواع TypeScript شاملة ومنظمة لمنصة فريلا سوريا**

*آخر تحديث: ديسمبر 2024*
