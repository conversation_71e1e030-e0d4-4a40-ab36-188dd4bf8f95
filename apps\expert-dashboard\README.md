# 👨‍💻 Freela Syria - Expert Dashboard

> لوحة تحكم الخبير المتخصصة لإدارة الخدمات والأعمال على منصة فريلا سوريا

## 📋 نظرة عامة

لوحة تحكم الخبير هي منصة شاملة مصممة خصيصاً للخبراء السوريين لإدارة خدماتهم، تتبع أرباحهم، والتواصل مع العملاء. تتيح للخبراء بناء حضور مهني قوي وإدارة أعمالهم بكفاءة عالية.

## ✨ الميزات الرئيسية

### 📊 لوحة التحكم الرئيسية
- **نظرة عامة على الأداء**: إحصائيات شاملة عن الخدمات والأرباح
- **مؤشرات النجاح**: معدل إكمال المشاريع ورضا العملاء
- **تحديثات فورية**: إشعارات الحجوزات والرسائل الجديدة
- **ملخص الأنشطة**: نشاط الحساب والمهام المعلقة

### 🛠️ إدارة الخدمات
- **كتالوج الخدمات**: عرض وإدارة جميع الخدمات المقدمة
- **إنشاء خدمات جديدة**: معالج سهل لإضافة خدمات جديدة
- **تحديث الخدمات**: تعديل الأسعار والأوصاف والمتطلبات
- **إدارة المعرض**: رفع وتنظيم أعمال سابقة ونماذج

### 📅 إدارة الحجوزات
- **تتبع الحجوزات**: مراقبة جميع الحجوزات النشطة والمكتملة
- **إدارة الجدولة**: تنظيم المواعيد والمهل الزمنية
- **تحديث الحالات**: تحديث تقدم العمل وحالة المشاريع
- **التواصل مع العملاء**: نظام مراسلة مدمج

### 💰 إدارة الأرباح
- **تتبع الإيرادات**: مراقبة الأرباح اليومية والشهرية والسنوية
- **طلبات السحب**: إدارة طلبات سحب الأموال
- **تاريخ المدفوعات**: سجل شامل لجميع المعاملات المالية
- **تقارير ضريبية**: تقارير مالية للأغراض الضريبية

### 📈 تحليلات الأداء
- **إحصائيات مفصلة**: تحليل أداء الخدمات والعملاء
- **اتجاهات النمو**: رسوم بيانية لتطور الأعمال
- **تقييمات العملاء**: متابعة التقييمات والمراجعات
- **مؤشرات الجودة**: معايير الأداء والتحسين

### 💬 نظام المراسلة
- **محادثات العملاء**: تواصل مباشر مع العملاء
- **إدارة الاستفسارات**: الرد على الأسئلة والاستفسارات
- **مشاركة الملفات**: إرسال واستقبال الملفات والمستندات
- **إشعارات فورية**: تنبيهات للرسائل الجديدة

### 👤 إدارة الملف الشخصي
- **الملف المهني**: عرض وتحديث المعلومات المهنية
- **المهارات والخبرات**: إدارة قائمة المهارات والتخصصات
- **الشهادات والمؤهلات**: رفع وعرض الشهادات
- **معرض الأعمال**: عرض أفضل الأعمال والمشاريع

## 🛠️ المكدس التقني

### Frontend Framework
- **Next.js 14**: إطار عمل React مع App Router
- **TypeScript**: لغة البرمجة المكتوبة
- **Tailwind CSS**: إطار عمل التصميم
- **React Hook Form**: إدارة النماذج
- **Zustand**: إدارة الحالة

### UI Components
- **Headless UI**: مكونات واجهة المستخدم
- **Heroicons**: مكتبة الأيقونات
- **Recharts**: رسوم بيانية تفاعلية
- **React Hot Toast**: إشعارات المستخدم
- **React Calendar**: تقويم تفاعلي

### Internationalization
- **next-i18next**: دعم الترجمة
- **Arabic RTL**: دعم كامل للعربية من اليمين لليسار
- **Multi-language**: دعم العربية والإنجليزية

## 🚀 البدء السريع

### المتطلبات الأساسية
```bash
Node.js >= 18.0.0
npm >= 9.0.0
```

### التثبيت والتشغيل
```bash
# الانتقال إلى مجلد لوحة الخبير
cd apps/expert-dashboard

# تثبيت التبعيات
npm install

# تشغيل خادم التطوير
npm run dev

# فتح المتصفح على
http://localhost:3002
```

### البناء للإنتاج
```bash
# بناء التطبيق
npm run build

# تشغيل النسخة المبنية
npm start
```

## 📁 هيكل المشروع

```
src/
├── app/                    # صفحات Next.js App Router
│   ├── dashboard/         # صفحات لوحة التحكم
│   │   ├── page.tsx      # الصفحة الرئيسية
│   │   ├── services/     # إدارة الخدمات
│   │   ├── bookings/     # إدارة الحجوزات
│   │   ├── earnings/     # إدارة الأرباح
│   │   ├── messages/     # نظام المراسلة
│   │   ├── analytics/    # تحليلات الأداء
│   │   └── profile/      # إدارة الملف الشخصي
│   ├── auth/             # صفحات المصادقة
│   ├── globals.css       # الأنماط العامة
│   └── layout.tsx        # التخطيط الرئيسي
├── components/            # المكونات القابلة لإعادة الاستخدام
│   ├── layout/           # مكونات التخطيط
│   ├── dashboard/        # مكونات لوحة التحكم
│   ├── forms/            # مكونات النماذج
│   ├── ui/               # مكونات واجهة المستخدم
│   └── charts/           # مكونات الرسوم البيانية
├── lib/                  # المكتبات والأدوات المساعدة
├── hooks/                # React Hooks مخصصة
├── store/                # إدارة الحالة (Zustand)
├── types/                # تعريفات TypeScript
└── utils/                # وظائف مساعدة
```

## 📊 الصفحات والوظائف

### 🏠 الصفحة الرئيسية (`/dashboard`)
- نظرة عامة على الأداء والإحصائيات
- ملخص الأرباح والحجوزات النشطة
- تحديثات النشاط الأخيرة
- روابط سريعة للمهام الشائعة

### 🛠️ إدارة الخدمات (`/dashboard/services`)
- قائمة جميع الخدمات المقدمة
- إضافة وتعديل الخدمات
- إدارة الأسعار والأوصاف
- تتبع أداء كل خدمة

### 📅 إدارة الحجوزات (`/dashboard/bookings`)
- عرض جميع الحجوزات النشطة
- تحديث حالة المشاريع
- جدولة المواعيد والمهام
- التواصل مع العملاء

### 💰 إدارة الأرباح (`/dashboard/earnings`)
- تتبع الإيرادات والأرباح
- طلبات سحب الأموال
- تاريخ المدفوعات
- تقارير مالية مفصلة

### 💬 نظام المراسلة (`/dashboard/messages`)
- محادثات مع العملاء
- إدارة الاستفسارات
- مشاركة الملفات
- إشعارات الرسائل

### 📈 تحليلات الأداء (`/dashboard/analytics`)
- إحصائيات الأداء
- رسوم بيانية للنمو
- تحليل التقييمات
- مؤشرات النجاح

### 👤 إدارة الملف الشخصي (`/dashboard/profile`)
- تحديث المعلومات الشخصية
- إدارة المهارات والخبرات
- رفع الشهادات والمؤهلات
- تحديث معرض الأعمال

## 🎨 التصميم والواجهة

### نظام الألوان
- **Primary**: الأزرق الداكن للعناصر الرئيسية
- **Secondary**: الرمادي للعناصر الثانوية
- **Success**: الأخضر للحالات الناجحة
- **Warning**: الأصفر للتحذيرات
- **Error**: الأحمر للأخطاء

### Typography
- **Font Family**: Cairo (للعربية) و Inter (للإنجليزية)
- **Font Weights**: 300, 400, 500, 600, 700
- **RTL Support**: دعم كامل للكتابة من اليمين لليسار

### Dark Theme
- دعم كامل للوضع المظلم
- تبديل سلس بين الأوضاع
- حفظ تفضيلات المستخدم

## 🔐 المصادقة والأمان

### نظام المصادقة
- **JWT Tokens**: رموز الوصول الآمنة
- **Expert Role**: تحكم في الوصول للخبراء فقط
- **Session Management**: إدارة الجلسات
- **Auto Logout**: تسجيل خروج تلقائي عند انتهاء الصلاحية

### الأمان
- **Data Protection**: حماية بيانات الخبراء
- **Secure Communication**: تشفير المراسلات
- **Input Validation**: التحقق من صحة المدخلات
- **Privacy Controls**: إعدادات الخصوصية

## 🧪 الاختبار

### تشغيل الاختبارات
```bash
# اختبارات الوحدة
npm test

# اختبارات مع تغطية الكود
npm run test:coverage

# اختبارات E2E
npm run test:e2e
```

### فحص جودة الكود
```bash
# فحص ESLint
npm run lint

# إصلاح مشاكل ESLint
npm run lint:fix

# فحص TypeScript
npm run type-check
```

## 🚀 النشر

### بناء الإنتاج
```bash
npm run build
```

### متغيرات البيئة
```env
NEXT_PUBLIC_API_URL=http://localhost:3000/api/v1
NEXT_PUBLIC_APP_NAME=Freela Syria Expert
NEXTAUTH_SECRET=your-secret-key
NEXTAUTH_URL=http://localhost:3002
```

## 📞 الدعم والمساعدة

### الوثائق
- [Next.js Documentation](https://nextjs.org/docs)
- [Tailwind CSS](https://tailwindcss.com/docs)
- [TypeScript](https://www.typescriptlang.org/docs)

### المساهمة
1. Fork المشروع
2. إنشاء فرع للميزة الجديدة
3. Commit التغييرات
4. Push إلى الفرع
5. فتح Pull Request

---

**🎯 منصة شاملة للخبراء السوريين لإدارة أعمالهم وتطوير مسيرتهم المهنية**

*آخر تحديث: ديسمبر 2024*
