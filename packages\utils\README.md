# 🛠️ @freela/utils

> حزمة الوظائف المساعدة المشتركة لمنصة فريلا سوريا

## 📋 نظرة عامة

حزمة `@freela/utils` تحتوي على مجموعة شاملة من الوظائف المساعدة والأدوات المشتركة عبر تطبيقات منصة فريلا سوريا. تتضمن وظائف للمصادقة، التحقق من البيانات، التنسيق، والثوابت المشتركة.

## ✨ الميزات الرئيسية

### 🔐 أدوات المصادقة
- **JWT Utilities**: إنشاء والتحقق من رموز JWT
- **Password Hashing**: تشفير والتحقق من كلمات المرور
- **Token Management**: إدارة رموز الوصول والتحديث
- **Session Utilities**: أدوات إدارة الجلسات

### ✅ التحقق من البيانات
- **Input Validation**: التحقق من صحة المدخلات
- **Schema Validation**: التحقق من المخططات
- **Email Validation**: التحقق من صحة البريد الإلكتروني
- **Phone Validation**: التحقق من أرقام الهاتف

### 📊 تنسيق البيانات
- **Date Formatting**: تنسيق التواريخ
- **Currency Formatting**: تنسيق العملات
- **Text Utilities**: أدوات النصوص
- **Number Formatting**: تنسيق الأرقام

### 🌍 الدعم الدولي
- **RTL Support**: دعم الكتابة من اليمين لليسار
- **Arabic Utilities**: أدوات خاصة بالعربية
- **Locale Helpers**: مساعدات اللغة والمنطقة
- **Translation Utilities**: أدوات الترجمة

## 🛠️ المكدس التقني

### Core
- **TypeScript 5+**: لغة البرمجة المكتوبة
- **Utility Functions**: وظائف مساعدة محسنة
- **Cross-Platform**: متوافق مع جميع البيئات

### Dependencies
- **bcryptjs**: تشفير كلمات المرور
- **jsonwebtoken**: إدارة JWT
- **validator**: التحقق من البيانات
- **date-fns**: معالجة التواريخ

## 🚀 التثبيت والاستخدام

### التثبيت
```bash
# في مجلد المشروع الرئيسي
npm install

# أو تثبيت الحزمة مباشرة
npm install @freela/utils
```

### الاستخدام
```typescript
import { 
  hashPassword, 
  verifyPassword, 
  generateJWT, 
  validateEmail,
  formatCurrency,
  formatDate 
} from '@freela/utils';

// مثال على استخدام الوظائف
const hashedPassword = await hashPassword('myPassword123');
const isValid = await verifyPassword('myPassword123', hashedPassword);
const token = generateJWT({ userId: '123', role: 'EXPERT' });
const isEmailValid = validateEmail('<EMAIL>');
const formattedPrice = formatCurrency(100, 'USD');
const formattedDate = formatDate(new Date(), 'ar');
```

## 📁 هيكل الحزمة

```
packages/utils/
├── src/                   # الكود المصدري
│   ├── index.ts          # نقطة الدخول الرئيسية
│   ├── auth.ts           # أدوات المصادقة
│   ├── validation.ts     # أدوات التحقق
│   ├── formatting.ts     # أدوات التنسيق
│   ├── constants.ts      # الثوابت المشتركة
│   ├── helpers.ts        # وظائف مساعدة عامة
│   ├── crypto.ts         # أدوات التشفير
│   ├── date.ts           # أدوات التاريخ والوقت
│   ├── string.ts         # أدوات النصوص
│   ├── number.ts         # أدوات الأرقام
│   └── locale.ts         # أدوات اللغة والمنطقة
├── dist/                 # الملفات المبنية
├── package.json          # إعدادات الحزمة
└── tsconfig.json         # إعدادات TypeScript
```

## 🔧 الوظائف المتاحة

### 🔐 أدوات المصادقة (auth.ts)
```typescript
// تشفير كلمة المرور
export const hashPassword = async (password: string): Promise<string>;

// التحقق من كلمة المرور
export const verifyPassword = async (
  password: string, 
  hash: string
): Promise<boolean>;

// إنشاء JWT
export const generateJWT = (
  payload: any, 
  expiresIn?: string
): string;

// التحقق من JWT
export const verifyJWT = (token: string): any;

// إنشاء رمز تحديث
export const generateRefreshToken = (): string;

// إنشاء رمز عشوائي
export const generateRandomToken = (length?: number): string;
```

### ✅ أدوات التحقق (validation.ts)
```typescript
// التحقق من البريد الإلكتروني
export const validateEmail = (email: string): boolean;

// التحقق من رقم الهاتف
export const validatePhone = (phone: string): boolean;

// التحقق من كلمة المرور
export const validatePassword = (password: string): {
  isValid: boolean;
  errors: string[];
};

// التحقق من الاسم
export const validateName = (name: string): boolean;

// التحقق من URL
export const validateURL = (url: string): boolean;

// التحقق من الرقم
export const validateNumber = (
  value: any, 
  min?: number, 
  max?: number
): boolean;
```

### 📊 أدوات التنسيق (formatting.ts)
```typescript
// تنسيق العملة
export const formatCurrency = (
  amount: number, 
  currency: string = 'USD',
  locale: string = 'ar-SY'
): string;

// تنسيق التاريخ
export const formatDate = (
  date: Date, 
  locale: string = 'ar',
  format?: string
): string;

// تنسيق الوقت النسبي
export const formatRelativeTime = (
  date: Date, 
  locale: string = 'ar'
): string;

// تنسيق الرقم
export const formatNumber = (
  number: number, 
  locale: string = 'ar'
): string;

// تنسيق النسبة المئوية
export const formatPercentage = (
  value: number, 
  decimals: number = 1
): string;

// تنسيق حجم الملف
export const formatFileSize = (bytes: number): string;
```

### 🌍 أدوات اللغة (locale.ts)
```typescript
// تحديد اتجاه النص
export const getTextDirection = (locale: string): 'ltr' | 'rtl';

// تحويل الأرقام للعربية
export const toArabicNumbers = (text: string): string;

// تحويل الأرقام للإنجليزية
export const toEnglishNumbers = (text: string): string;

// تنسيق النص للعربية
export const formatArabicText = (text: string): string;

// الحصول على اللغة المفضلة
export const getPreferredLanguage = (): string;

// تحديد المنطقة الزمنية
export const getTimezone = (): string;
```

### 🔤 أدوات النصوص (string.ts)
```typescript
// تحويل لـ camelCase
export const toCamelCase = (str: string): string;

// تحويل لـ kebab-case
export const toKebabCase = (str: string): string;

// تحويل لـ snake_case
export const toSnakeCase = (str: string): string;

// اقتطاع النص
export const truncateText = (
  text: string, 
  length: number, 
  suffix: string = '...'
): string;

// إزالة HTML tags
export const stripHtml = (html: string): string;

// تنظيف النص
export const sanitizeText = (text: string): string;

// إنشاء slug
export const createSlug = (text: string): string;
```

### 🔢 أدوات الأرقام (number.ts)
```typescript
// تقريب الرقم
export const roundNumber = (
  number: number, 
  decimals: number = 2
): number;

// تحويل لنسبة مئوية
export const toPercentage = (
  value: number, 
  total: number
): number;

// حساب المتوسط
export const calculateAverage = (numbers: number[]): number;

// إنشاء رقم عشوائي
export const randomNumber = (min: number, max: number): number;

// التحقق من كون الرقم في نطاق
export const isInRange = (
  number: number, 
  min: number, 
  max: number
): boolean;
```

### 📅 أدوات التاريخ (date.ts)
```typescript
// إضافة أيام للتاريخ
export const addDays = (date: Date, days: number): Date;

// حساب الفرق بين تاريخين
export const dateDifference = (
  date1: Date, 
  date2: Date, 
  unit: 'days' | 'hours' | 'minutes'
): number;

// التحقق من كون التاريخ في الماضي
export const isPastDate = (date: Date): boolean;

// التحقق من كون التاريخ في المستقبل
export const isFutureDate = (date: Date): boolean;

// الحصول على بداية اليوم
export const startOfDay = (date: Date): Date;

// الحصول على نهاية اليوم
export const endOfDay = (date: Date): Date;
```

### 🔒 أدوات التشفير (crypto.ts)
```typescript
// إنشاء hash
export const createHash = (data: string, algorithm?: string): string;

// إنشاء UUID
export const generateUUID = (): string;

// تشفير البيانات
export const encryptData = (data: string, key: string): string;

// فك تشفير البيانات
export const decryptData = (
  encryptedData: string, 
  key: string
): string;

// إنشاء مفتاح عشوائي
export const generateSecretKey = (length?: number): string;
```

## 📊 الثوابت المشتركة (constants.ts)

```typescript
// أدوار المستخدمين
export const USER_ROLES = {
  CLIENT: 'CLIENT',
  EXPERT: 'EXPERT',
  ADMIN: 'ADMIN',
} as const;

// حالات الخدمات
export const SERVICE_STATUS = {
  PENDING: 'PENDING',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED',
  INACTIVE: 'INACTIVE',
} as const;

// حالات الحجوزات
export const BOOKING_STATUS = {
  PENDING: 'PENDING',
  ACCEPTED: 'ACCEPTED',
  IN_PROGRESS: 'IN_PROGRESS',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED',
  DISPUTED: 'DISPUTED',
} as const;

// أنواع الملفات المدعومة
export const SUPPORTED_FILE_TYPES = {
  IMAGES: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
  DOCUMENTS: ['pdf', 'doc', 'docx', 'txt'],
  ARCHIVES: ['zip', 'rar', '7z'],
} as const;

// حدود الملفات
export const FILE_LIMITS = {
  MAX_SIZE: 10 * 1024 * 1024, // 10MB
  MAX_FILES: 5,
} as const;

// إعدادات JWT
export const JWT_CONFIG = {
  ACCESS_TOKEN_EXPIRES: '15m',
  REFRESH_TOKEN_EXPIRES: '7d',
  ALGORITHM: 'HS256',
} as const;
```

## 🧪 الاختبار

### تشغيل الاختبارات
```bash
npm test
```

### اختبارات الوظائف
- **Unit Tests**: اختبار كل وظيفة منفردة
- **Integration Tests**: اختبار التكامل بين الوظائف
- **Performance Tests**: اختبار الأداء
- **Security Tests**: اختبار الأمان

## 📚 أمثلة الاستخدام

### في تطبيق React
```typescript
import { formatCurrency, formatDate, validateEmail } from '@freela/utils';

const ServiceCard = ({ service }) => {
  const formattedPrice = formatCurrency(service.price, 'USD');
  const formattedDate = formatDate(service.createdAt, 'ar');
  
  return (
    <div>
      <h3>{service.title}</h3>
      <p>السعر: {formattedPrice}</p>
      <p>تاريخ الإنشاء: {formattedDate}</p>
    </div>
  );
};
```

### في API Controller
```typescript
import { hashPassword, validateEmail, generateJWT } from '@freela/utils';

export const registerUser = async (req, res) => {
  const { email, password } = req.body;
  
  if (!validateEmail(email)) {
    return res.status(400).json({ error: 'بريد إلكتروني غير صحيح' });
  }
  
  const hashedPassword = await hashPassword(password);
  const user = await createUser({ email, password: hashedPassword });
  const token = generateJWT({ userId: user.id, role: user.role });
  
  res.json({ user, token });
};
```

## 🔧 الأوامر المتاحة

### التطوير
```bash
# بناء الحزمة
npm run build

# مراقبة التغييرات
npm run dev

# فحص الأنواع
npm run type-check

# فحص ESLint
npm run lint
```

## 📞 الدعم والمساعدة

### الوثائق
- [TypeScript Documentation](https://www.typescriptlang.org/docs)
- [Node.js Documentation](https://nodejs.org/docs)

### المساهمة
1. Fork المشروع
2. إنشاء فرع للميزة الجديدة
3. Commit التغييرات
4. Push إلى الفرع
5. فتح Pull Request

---

**🎯 مجموعة شاملة من الوظائف المساعدة لمنصة فريلا سوريا**

*آخر تحديث: ديسمبر 2024*
