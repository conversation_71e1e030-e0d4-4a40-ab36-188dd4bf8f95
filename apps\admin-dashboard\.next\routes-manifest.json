{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/dashboard/analytics", "regex": "^/dashboard/analytics(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/analytics(?:/)?$"}, {"page": "/dashboard/bookings", "regex": "^/dashboard/bookings(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/bookings(?:/)?$"}, {"page": "/dashboard/categories", "regex": "^/dashboard/categories(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/categories(?:/)?$"}, {"page": "/dashboard/disputes", "regex": "^/dashboard/disputes(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/disputes(?:/)?$"}, {"page": "/dashboard/payments", "regex": "^/dashboard/payments(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/payments(?:/)?$"}, {"page": "/dashboard/reports", "regex": "^/dashboard/reports(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/reports(?:/)?$"}, {"page": "/dashboard/services", "regex": "^/dashboard/services(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/services(?:/)?$"}, {"page": "/dashboard/settings", "regex": "^/dashboard/settings(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/settings(?:/)?$"}, {"page": "/dashboard/users", "regex": "^/dashboard/users(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/users(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Url", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "rewrites": [{"source": "/api/:path*", "destination": "http://localhost:3000/api/:path*", "regex": "^/api(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}]}