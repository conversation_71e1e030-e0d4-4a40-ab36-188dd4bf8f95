(()=>{var e={};e.id=618,e.ids=[618],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},55393:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>l.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>o});var a=t(67096),s=t(16132),i=t(37284),l=t.n(i),n=t(32564),d={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(r,d);let o=["",{children:["dashboard",{children:["users",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,20294)),"C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\dashboard\\users\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,68182)),"C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,62594)),"C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,16097)),"C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,25666)),"C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\not-found.tsx"]}],c=["C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\dashboard\\users\\page.tsx"],m="/dashboard/users/page",x={require:t,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/dashboard/users/page",pathname:"/dashboard/users",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},21583:(e,r,t)=>{Promise.resolve().then(t.bind(t,16492))},16492:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>f});var a=t(53854),s=t(34218);let i=s.forwardRef(function({title:e,titleId:r,...t},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},t),e?s.createElement("title",{id:r},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M18 7.5v3m0 0v3m0-3h3m-3 0h-3m-2.25-4.125a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0ZM3 19.235v-.11a6.375 6.375 0 0 1 12.75 0v.109A12.318 12.318 0 0 1 9.374 21c-2.331 0-4.512-.645-6.374-1.766Z"}))});var l=t(44135),n=t(41350),d=t(654);let o=s.forwardRef(function({title:e,titleId:r,...t},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},t),e?s.createElement("title",{id:r},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M18.364 18.364A9 9 0 0 0 5.636 5.636m12.728 12.728A9 9 0 0 1 5.636 5.636m12.728 12.728L5.636 5.636"}))});var c=t(6570),m=t(19013),x=t(44919),u=t(25675);function g({isOpen:e,onClose:r,title:t,children:i,size:l="md",showCloseButton:n=!0,closeOnOverlayClick:d=!0}){return a.jsx(m.u,{appear:!0,show:e,as:s.Fragment,children:(0,a.jsxs)(x.V,{as:"div",className:"relative z-50",onClose:d?r:()=>{},children:[a.jsx(m.u.Child,{as:s.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:a.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-25"})}),a.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:a.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:a.jsx(m.u.Child,{as:s.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:(0,a.jsxs)(x.V.Panel,{className:`w-full ${{sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl",full:"max-w-7xl"}[l]} transform overflow-hidden rounded-2xl bg-white dark:bg-gray-800 p-6 text-right align-middle shadow-xl transition-all`,children:[(t||n)&&(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[t&&a.jsx(x.V.Title,{as:"h3",className:"text-lg font-medium leading-6 text-gray-900 dark:text-white",children:t}),n&&(0,a.jsxs)("button",{type:"button",className:"rounded-md text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500",onClick:r,children:[a.jsx("span",{className:"sr-only",children:"إغلاق"}),a.jsx(u.Z,{className:"h-6 w-6","aria-hidden":"true"})]})]}),i]})})})})]})})}var h=t(9687);function p({isOpen:e,onClose:r,onSubmit:t,user:i,mode:l}){let[n,d]=(0,s.useState)({name:i?.name||"",email:i?.email||"",role:i?.role||"client",status:i?.status||"active",verified:i?.verified||!1,phone:i?.phone||"",bio:i?.bio||""}),[o,c]=(0,s.useState)(!1),[m,x]=(0,s.useState)({}),u="view"===l,p=()=>{let e={};return n.name.trim()||(e.name="الاسم مطلوب"),n.email.trim()?/\S+@\S+\.\S+/.test(n.email)||(e.email="البريد الإلكتروني غير صحيح"):e.email="البريد الإلكتروني مطلوب",x(e),0===Object.keys(e).length},y=async e=>{if(e.preventDefault(),u){r();return}if(p()){c(!0);try{await t({...n,id:i?.id}),r()}catch(e){console.error("Error submitting form:",e)}finally{c(!1)}}},b=(e,r)=>{d(t=>({...t,[e]:r})),m[e]&&x(r=>({...r,[e]:""}))};return a.jsx(g,{isOpen:e,onClose:r,title:"create"===l?"إضافة مستخدم جديد":"edit"===l?"تعديل المستخدم":"عرض المستخدم",size:"lg",children:(0,a.jsxs)("form",{onSubmit:y,className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-6 sm:grid-cols-2",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"الاسم الكامل *"}),a.jsx("input",{type:"text",value:n.name,onChange:e=>b("name",e.target.value),disabled:u,className:`mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${m.name?"border-red-300":""} ${u?"bg-gray-50 dark:bg-gray-800":""}`}),m.name&&a.jsx("p",{className:"mt-1 text-sm text-red-600",children:m.name})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"البريد الإلكتروني *"}),a.jsx("input",{type:"email",value:n.email,onChange:e=>b("email",e.target.value),disabled:u,className:`mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${m.email?"border-red-300":""} ${u?"bg-gray-50 dark:bg-gray-800":""}`}),m.email&&a.jsx("p",{className:"mt-1 text-sm text-red-600",children:m.email})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"رقم الهاتف"}),a.jsx("input",{type:"tel",value:n.phone,onChange:e=>b("phone",e.target.value),disabled:u,className:`mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${u?"bg-gray-50 dark:bg-gray-800":""}`})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"الدور"}),(0,a.jsxs)("select",{value:n.role,onChange:e=>b("role",e.target.value),disabled:u,className:`mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${u?"bg-gray-50 dark:bg-gray-800":""}`,children:[a.jsx("option",{value:"client",children:"عميل"}),a.jsx("option",{value:"expert",children:"خبير"}),a.jsx("option",{value:"admin",children:"مدير"})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"الحالة"}),(0,a.jsxs)("select",{value:n.status,onChange:e=>b("status",e.target.value),disabled:u,className:`mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${u?"bg-gray-50 dark:bg-gray-800":""}`,children:[a.jsx("option",{value:"active",children:"نشط"}),a.jsx("option",{value:"pending",children:"في الانتظار"}),a.jsx("option",{value:"suspended",children:"معلق"})]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("input",{type:"checkbox",checked:n.verified,onChange:e=>b("verified",e.target.checked),disabled:u,className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"}),a.jsx("label",{className:"mr-2 block text-sm text-gray-900 dark:text-white",children:"حساب موثق"})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"نبذة شخصية"}),a.jsx("textarea",{rows:3,value:n.bio,onChange:e=>b("bio",e.target.value),disabled:u,className:`mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${u?"bg-gray-50 dark:bg-gray-800":""}`})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 rtl:space-x-reverse",children:[a.jsx("button",{type:"button",onClick:r,className:"px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500",children:u?"إغلاق":"إلغاء"}),!u&&(0,a.jsxs)("button",{type:"submit",disabled:o,className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50",children:[o&&a.jsx(h.T,{size:"sm",color:"white",className:"ml-2"}),"create"===l?"إضافة":"حفظ التغييرات"]})]})]})})}var y=t(38397);let b=[{id:"1",name:"أحمد محمد",email:"<EMAIL>",role:"expert",status:"active",verified:!0,joinDate:"2024-01-15",lastActive:"منذ ساعتين"},{id:"2",name:"فاطمة أحمد",email:"<EMAIL>",role:"client",status:"active",verified:!0,joinDate:"2024-01-10",lastActive:"منذ يوم"},{id:"3",name:"محمد علي",email:"<EMAIL>",role:"expert",status:"pending",verified:!1,joinDate:"2024-01-20",lastActive:"منذ 3 أيام"}];function f(){let[e,r]=(0,s.useState)(b),[t,m]=(0,s.useState)(""),[x,u]=(0,s.useState)("all"),[g,h]=(0,s.useState)("all"),[f,v]=(0,s.useState)(!1),[w,j]=(0,s.useState)(!1),[k,N]=(0,s.useState)(null),[C,E]=(0,s.useState)("create"),L=e.filter(e=>{let r=e.name.toLowerCase().includes(t.toLowerCase())||e.email.toLowerCase().includes(t.toLowerCase()),a="all"===x||e.role===x,s="all"===g||e.status===g;return r&&a&&s}),P=e=>{N(e),E("view"),j(!0)},S=e=>{N(e),E("edit"),j(!0)},$=async e=>{if(window.confirm("هل أنت متأكد من حذف هذا المستخدم؟")){v(!0);try{await new Promise(e=>setTimeout(e,1e3)),r(r=>r.filter(r=>r.id!==e))}catch(e){console.error("Error deleting user:",e)}finally{v(!1)}}},_=async e=>{if(window.confirm("هل أنت متأكد من تعليق هذا المستخدم؟")){v(!0);try{await new Promise(e=>setTimeout(e,1e3)),r(r=>r.map(r=>r.id===e?{...r,status:"suspended"}:r))}catch(e){console.error("Error suspending user:",e)}finally{v(!1)}}},Z=async e=>{v(!0);try{if(await new Promise(e=>setTimeout(e,1e3)),"create"===C){let t={...e,id:Date.now().toString()};r(e=>[...e,t])}else"edit"===C&&r(r=>r.map(r=>r.id===e.id?e:r))}catch(e){throw console.error("Error submitting user:",e),e}finally{v(!1)}},A=e=>a.jsx("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${{active:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",suspended:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",pending:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300"}[e]}`,children:{active:"نشط",suspended:"معلق",pending:"في الانتظار"}[e]}),F=e=>a.jsx("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${{admin:"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300",expert:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",client:"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"}[e]}`,children:{admin:"مدير",expert:"خبير",client:"عميل"}[e]});return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"sm:flex sm:items-center",children:[(0,a.jsxs)("div",{className:"sm:flex-auto",children:[a.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"إدارة المستخدمين"}),a.jsx("p",{className:"mt-2 text-sm text-gray-700 dark:text-gray-300",children:"قائمة بجميع المستخدمين في منصة فريلا سوريا مع إمكانية البحث والتصفية والإدارة"})]}),a.jsx("div",{className:"mt-4 sm:mt-0 sm:mr-16 sm:flex-none",children:(0,a.jsxs)("button",{type:"button",onClick:()=>{N(null),E("create"),j(!0)},className:"inline-flex items-center justify-center rounded-md border border-transparent bg-primary-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2",children:[a.jsx(i,{className:"h-4 w-4 ml-2"}),"إضافة مستخدم جديد"]})})]}),a.jsx("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg p-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-3",children:[(0,a.jsxs)("div",{className:"relative",children:[a.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:a.jsx(l.Z,{className:"h-5 w-5 text-gray-400"})}),a.jsx("input",{type:"text",placeholder:"البحث عن مستخدم...",value:t,onChange:e=>m(e.target.value),className:"block w-full pr-10 border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"})]}),a.jsx("div",{children:(0,a.jsxs)("select",{value:x,onChange:e=>u(e.target.value),className:"block w-full border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",children:[a.jsx("option",{value:"all",children:"جميع الأدوار"}),a.jsx("option",{value:"admin",children:"مدير"}),a.jsx("option",{value:"expert",children:"خبير"}),a.jsx("option",{value:"client",children:"عميل"})]})}),a.jsx("div",{children:(0,a.jsxs)("select",{value:g,onChange:e=>h(e.target.value),className:"block w-full border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",children:[a.jsx("option",{value:"all",children:"جميع الحالات"}),a.jsx("option",{value:"active",children:"نشط"}),a.jsx("option",{value:"pending",children:"في الانتظار"}),a.jsx("option",{value:"suspended",children:"معلق"})]})})]})}),a.jsx("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden",children:a.jsx("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-700",children:[a.jsx("thead",{className:"bg-gray-50 dark:bg-gray-900",children:(0,a.jsxs)("tr",{children:[a.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"المستخدم"}),a.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"الدور"}),a.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"الحالة"}),a.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"تاريخ الانضمام"}),a.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"آخر نشاط"}),a.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"الإجراءات"})]})}),a.jsx("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700",children:L.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50 dark:hover:bg-gray-700",children:[a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"h-10 w-10 flex-shrink-0",children:a.jsx("div",{className:"h-10 w-10 rounded-full bg-primary-500 flex items-center justify-center",children:a.jsx("span",{className:"text-sm font-medium text-white",children:e.name.charAt(0)})})}),(0,a.jsxs)("div",{className:"mr-4",children:[(0,a.jsxs)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:[e.name,e.verified&&a.jsx("span",{className:"mr-2 text-green-500",children:"✓"})]}),a.jsx("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:e.email})]})]})}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:F(e.role)}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:A(e.status)}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white",children:e.joinDate}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400",children:e.lastActive}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex space-x-2 rtl:space-x-reverse",children:[a.jsx("button",{type:"button",onClick:()=>P(e),className:"text-primary-600 hover:text-primary-900 dark:text-primary-400",title:"عرض التفاصيل",children:a.jsx(n.Z,{className:"h-4 w-4"})}),a.jsx("button",{type:"button",onClick:()=>S(e),className:"text-gray-600 hover:text-gray-900 dark:text-gray-400",title:"تعديل",children:a.jsx(d.Z,{className:"h-4 w-4"})}),a.jsx("button",{type:"button",onClick:()=>_(e.id),className:"text-yellow-600 hover:text-yellow-900 dark:text-yellow-400",title:"تعليق الحساب",children:a.jsx(o,{className:"h-4 w-4"})}),a.jsx("button",{type:"button",onClick:()=>$(e.id),className:"text-red-600 hover:text-red-900 dark:text-red-400",title:"حذف",children:a.jsx(c.Z,{className:"h-4 w-4"})})]})})]},e.id))})]})})}),(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 px-4 py-3 flex items-center justify-between border-t border-gray-200 dark:border-gray-700 sm:px-6 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex-1 flex justify-between sm:hidden",children:[a.jsx("button",{className:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50",children:"السابق"}),a.jsx("button",{className:"mr-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50",children:"التالي"})]}),(0,a.jsxs)("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[a.jsx("div",{children:(0,a.jsxs)("p",{className:"text-sm text-gray-700 dark:text-gray-300",children:["عرض ",a.jsx("span",{className:"font-medium",children:"1"})," إلى ",a.jsx("span",{className:"font-medium",children:"10"})," من"," ",a.jsx("span",{className:"font-medium",children:L.length})," نتيجة"]})}),a.jsx("div",{children:(0,a.jsxs)("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px rtl:space-x-reverse","aria-label":"Pagination",children:[a.jsx("button",{className:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50",children:"السابق"}),a.jsx("button",{className:"relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50",children:"1"}),a.jsx("button",{className:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50",children:"التالي"})]})})]})]}),a.jsx(p,{isOpen:w,onClose:()=>j(!1),onSubmit:Z,user:k,mode:C}),f&&a.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:a.jsx(y.G,{message:"جاري المعالجة..."})})]})}},9687:(e,r,t)=>{"use strict";t.d(r,{T:()=>s});var a=t(53854);function s({size:e="md",color:r="primary",className:t=""}){return a.jsx("div",{className:`animate-spin ${{sm:"h-4 w-4",md:"h-6 w-6",lg:"h-8 w-8"}[e]} ${{primary:"text-primary-600",white:"text-white",gray:"text-gray-400"}[r]} ${t}`,children:(0,a.jsxs)("svg",{className:"w-full h-full",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[a.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),a.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]})})}},38397:(e,r,t)=>{"use strict";t.d(r,{G:()=>i});var a=t(53854),s=t(9687);function i({message:e="جاري التحميل...",size:r="md",className:t=""}){return(0,a.jsxs)("div",{className:`flex flex-col items-center justify-center py-12 ${t}`,children:[a.jsx(s.T,{size:r}),a.jsx("p",{className:"mt-4 text-sm text-gray-500 dark:text-gray-400",children:e})]})}},20294:(e,r,t)=>{"use strict";t.r(r),t.d(r,{$$typeof:()=>l,__esModule:()=>i,default:()=>d});var a=t(95153);let s=(0,a.createProxy)(String.raw`C:\Users\<USER>\Documents\Freela\apps\admin-dashboard\src\app\dashboard\users\page.tsx`),{__esModule:i,$$typeof:l}=s,n=s.default,d=n},41350:(e,r,t)=>{"use strict";t.d(r,{Z:()=>i});var a=t(34218);let s=a.forwardRef(function({title:e,titleId:r,...t},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":r},t),e?a.createElement("title",{id:r},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))}),i=s},44135:(e,r,t)=>{"use strict";t.d(r,{Z:()=>i});var a=t(34218);let s=a.forwardRef(function({title:e,titleId:r,...t},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":r},t),e?a.createElement("title",{id:r},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"}))}),i=s},654:(e,r,t)=>{"use strict";t.d(r,{Z:()=>i});var a=t(34218);let s=a.forwardRef(function({title:e,titleId:r,...t},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":r},t),e?a.createElement("title",{id:r},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125"}))}),i=s},6570:(e,r,t)=>{"use strict";t.d(r,{Z:()=>i});var a=t(34218);let s=a.forwardRef(function({title:e,titleId:r,...t},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":r},t),e?a.createElement("title",{id:r},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"}))}),i=s}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[917,301,952],()=>t(55393));module.exports=a})();