(()=>{var e={};e.id=165,e.ids=[165],e.modules={5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},1877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},5319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},3535:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>p,originalPathname:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>l});var s=t(7096),a=t(6132),n=t(7284),i=t.n(n),o=t(2564),d={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);t.d(r,d);let l=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,5666)),"C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\app\\not-found.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,2594)),"C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,6097)),"C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,5666)),"C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\app\\not-found.tsx"]}],c=[],x="/_not-found",p={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/_not-found",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},2545:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,3579,23)),Promise.resolve().then(t.t.bind(t,619,23)),Promise.resolve().then(t.t.bind(t,1459,23)),Promise.resolve().then(t.t.bind(t,3456,23)),Promise.resolve().then(t.t.bind(t,847,23)),Promise.resolve().then(t.t.bind(t,7303,23))},423:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,7490,23))},2072:(e,r,t)=>{Promise.resolve().then(t.bind(t,9141))},2946:(e,r,t)=>{Promise.resolve().then(t.bind(t,5068))},9141:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>Error});var s=t(3854),a=t(4218);function Error({error:e,reset:r}){return(0,a.useEffect)(()=>{console.error(e)},[e]),s.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8 text-center",children:[(0,s.jsxs)("div",{children:[s.jsx("h1",{className:"text-9xl font-bold text-gray-300 dark:text-gray-700",children:"500"}),s.jsx("h2",{className:"mt-6 text-3xl font-bold text-gray-900 dark:text-white",children:"حدث خطأ"}),s.jsx("p",{className:"mt-2 text-sm text-gray-600 dark:text-gray-400",children:"عذراً، حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى."})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[s.jsx("button",{onClick:r,className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500",children:"المحاولة مرة أخرى"}),s.jsx("div",{children:s.jsx("a",{href:"/",className:"text-sm text-primary-600 hover:text-primary-500",children:"العودة للرئيسية"})})]})]})})}},5068:(e,r,t)=>{"use strict";t.r(r),t.d(r,{Providers:()=>l});var s=t(3854),a=t(4352),n=t(5045),i=t(6644),o=t(8111),d=t(4218);function l({children:e}){let[r]=(0,d.useState)(()=>new a.S({defaultOptions:{queries:{staleTime:6e4,retry:1}}}));return s.jsx(n.aH,{client:r,children:(0,s.jsxs)(i.f,{attribute:"class",defaultTheme:"dark",enableSystem:!1,disableTransitionOnChange:!0,children:[e,s.jsx(o.x7,{position:"top-center",toastOptions:{duration:4e3,style:{background:"#1f2937",color:"#f9fafb",border:"1px solid #374151"},success:{iconTheme:{primary:"#10b981",secondary:"#f9fafb"}},error:{iconTheme:{primary:"#ef4444",secondary:"#f9fafb"}}}})]})})}},6097:(e,r,t)=>{"use strict";t.r(r),t.d(r,{$$typeof:()=>i,__esModule:()=>n,default:()=>d});var s=t(5153);let a=(0,s.createProxy)(String.raw`C:\Users\<USER>\Documents\Freela\apps\expert-dashboard\src\app\error.tsx`),{__esModule:n,$$typeof:i}=a,o=a.default,d=o},2594:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>m,metadata:()=>x,viewport:()=>p});var s=t(4656),a=t(4302),n=t.n(a),i=t(5153);let o=(0,i.createProxy)(String.raw`C:\Users\<USER>\Documents\Freela\apps\expert-dashboard\src\app\providers.tsx`),{__esModule:d,$$typeof:l}=o;o.default;let c=(0,i.createProxy)(String.raw`C:\Users\<USER>\Documents\Freela\apps\expert-dashboard\src\app\providers.tsx#Providers`);t(5023);let x={title:"Freela Syria - Expert Dashboard",description:"Expert dashboard for Freela Syria marketplace",keywords:["freelance","syria","expert","dashboard"],authors:[{name:"Freela Syria Team"}]},p={width:"device-width",initialScale:1};function m({children:e}){return(0,s.jsxs)("html",{lang:"ar",dir:"rtl",suppressHydrationWarning:!0,children:[s.jsx("head",{children:s.jsx("link",{href:"https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap",rel:"stylesheet"})}),s.jsx("body",{className:`${n().variable} font-arabic antialiased`,children:s.jsx(c,{children:e})})]})}},5666:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i});var s=t(4656),a=t(4353),n=t.n(a);function i(){return s.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8 text-center",children:[(0,s.jsxs)("div",{children:[s.jsx("h1",{className:"text-9xl font-bold text-gray-300 dark:text-gray-700",children:"404"}),s.jsx("h2",{className:"mt-6 text-3xl font-bold text-gray-900 dark:text-white",children:"الصفحة غير موجودة"}),s.jsx("p",{className:"mt-2 text-sm text-gray-600 dark:text-gray-400",children:"عذراً، لا يمكننا العثور على الصفحة التي تبحث عنها."})]}),s.jsx("div",{children:s.jsx(n(),{href:"/",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500",children:"العودة للرئيسية"})})]})})}},5023:()=>{}};var r=require("../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[262],()=>t(3535));module.exports=s})();