(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[92],{30622:function(t,e,r){"use strict";var n=r(2265),o=Symbol.for("react.element"),i=Symbol.for("react.fragment"),a=Object.prototype.hasOwnProperty,u=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,c={key:!0,ref:!0,__self:!0,__source:!0};function l(t,e,r){var n,i={},l=null,s=null;for(n in void 0!==r&&(l=""+r),void 0!==e.key&&(l=""+e.key),void 0!==e.ref&&(s=e.ref),e)a.call(e,n)&&!c.hasOwnProperty(n)&&(i[n]=e[n]);if(t&&t.defaultProps)for(n in e=t.defaultProps)void 0===i[n]&&(i[n]=e[n]);return{$$typeof:o,type:t,key:l,ref:s,props:i,_owner:u.current}}e.Fragment=i,e.jsx=l,e.jsxs=l},57437:function(t,e,r){"use strict";t.exports=r(30622)},8236:function(t,e){"use strict";/**
 * @license React
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r=Symbol.for("react.element"),n=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),a=Symbol.for("react.profiler"),u=Symbol.for("react.provider"),c=Symbol.for("react.context"),l=Symbol.for("react.server_context"),s=Symbol.for("react.forward_ref"),f=Symbol.for("react.suspense"),p=Symbol.for("react.suspense_list"),h=Symbol.for("react.memo"),d=Symbol.for("react.lazy");Symbol.for("react.offscreen"),Symbol.for("react.module.reference"),e.isFragment=function(t){return function(t){if("object"==typeof t&&null!==t){var e=t.$$typeof;switch(e){case r:switch(t=t.type){case o:case a:case i:case f:case p:return t;default:switch(t=t&&t.$$typeof){case l:case c:case s:case d:case h:case u:return t;default:return e}}case n:return e}}}(t)===o}},9176:function(t,e,r){"use strict";t.exports=r(8236)},98917:function(t,e,r){"use strict";r.d(e,{ZP:function(){return tP}});var n=r(2265),o=r(3436),i=r.n(o),a=Object.getOwnPropertyNames,u=Object.getOwnPropertySymbols,c=Object.prototype.hasOwnProperty;function l(t,e){return function(r,n,o){return t(r,n,o)&&e(r,n,o)}}function s(t){return function(e,r,n){if(!e||!r||"object"!=typeof e||"object"!=typeof r)return t(e,r,n);var o=n.cache,i=o.get(e),a=o.get(r);if(i&&a)return i===r&&a===e;o.set(e,r),o.set(r,e);var u=t(e,r,n);return o.delete(e),o.delete(r),u}}function f(t){return a(t).concat(u(t))}var p=Object.hasOwn||function(t,e){return c.call(t,e)};function h(t,e){return t===e||!t&&!e&&t!=t&&e!=e}var d=Object.getOwnPropertyDescriptor,y=Object.keys;function v(t,e,r){var n=t.length;if(e.length!==n)return!1;for(;n-- >0;)if(!r.equals(t[n],e[n],n,n,t,e,r))return!1;return!0}function m(t,e){return h(t.getTime(),e.getTime())}function b(t,e){return t.name===e.name&&t.message===e.message&&t.cause===e.cause&&t.stack===e.stack}function g(t,e){return t===e}function x(t,e,r){var n,o,i=t.size;if(i!==e.size)return!1;if(!i)return!0;for(var a=Array(i),u=t.entries(),c=0;(n=u.next())&&!n.done;){for(var l=e.entries(),s=!1,f=0;(o=l.next())&&!o.done;){if(a[f]){f++;continue}var p=n.value,h=o.value;if(r.equals(p[0],h[0],c,f,t,e,r)&&r.equals(p[1],h[1],p[0],h[0],t,e,r)){s=a[f]=!0;break}f++}if(!s)return!1;c++}return!0}function O(t,e,r){var n=y(t),o=n.length;if(y(e).length!==o)return!1;for(;o-- >0;)if(!k(t,e,r,n[o]))return!1;return!0}function w(t,e,r){var n,o,i,a=f(t),u=a.length;if(f(e).length!==u)return!1;for(;u-- >0;)if(!k(t,e,r,n=a[u])||(o=d(t,n),i=d(e,n),(o||i)&&(!o||!i||o.configurable!==i.configurable||o.enumerable!==i.enumerable||o.writable!==i.writable)))return!1;return!0}function j(t,e){return h(t.valueOf(),e.valueOf())}function S(t,e){return t.source===e.source&&t.flags===e.flags}function P(t,e,r){var n,o,i=t.size;if(i!==e.size)return!1;if(!i)return!0;for(var a=Array(i),u=t.values();(n=u.next())&&!n.done;){for(var c=e.values(),l=!1,s=0;(o=c.next())&&!o.done;){if(!a[s]&&r.equals(n.value,o.value,n.value,o.value,t,e,r)){l=a[s]=!0;break}s++}if(!l)return!1}return!0}function A(t,e){var r=t.length;if(e.length!==r)return!1;for(;r-- >0;)if(t[r]!==e[r])return!1;return!0}function E(t,e){return t.hostname===e.hostname&&t.pathname===e.pathname&&t.protocol===e.protocol&&t.port===e.port&&t.hash===e.hash&&t.username===e.username&&t.password===e.password}function k(t,e,r,n){return("_owner"===n||"__o"===n||"__v"===n)&&(!!t.$$typeof||!!e.$$typeof)||p(e,n)&&r.equals(t[n],e[n],n,n,t,e,r)}var M=Array.isArray,_="function"==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView:null,T=Object.assign,C=Object.prototype.toString.call.bind(Object.prototype.toString),D=I();function I(t){void 0===t&&(t={});var e,r,n,o,i,a,u,c,f,p,d,y,k,D=t.circular,I=t.createInternalComparator,N=t.createState,B=t.strict,L=(r=(e=function(t){var e=t.circular,r=t.createCustomConfig,n=t.strict,o={areArraysEqual:n?w:v,areDatesEqual:m,areErrorsEqual:b,areFunctionsEqual:g,areMapsEqual:n?l(x,w):x,areNumbersEqual:h,areObjectsEqual:n?w:O,arePrimitiveWrappersEqual:j,areRegExpsEqual:S,areSetsEqual:n?l(P,w):P,areTypedArraysEqual:n?w:A,areUrlsEqual:E};if(r&&(o=T({},o,r(o))),e){var i=s(o.areArraysEqual),a=s(o.areMapsEqual),u=s(o.areObjectsEqual),c=s(o.areSetsEqual);o=T({},o,{areArraysEqual:i,areMapsEqual:a,areObjectsEqual:u,areSetsEqual:c})}return o}(t)).areArraysEqual,n=e.areDatesEqual,o=e.areErrorsEqual,i=e.areFunctionsEqual,a=e.areMapsEqual,u=e.areNumbersEqual,c=e.areObjectsEqual,f=e.arePrimitiveWrappersEqual,p=e.areRegExpsEqual,d=e.areSetsEqual,y=e.areTypedArraysEqual,k=e.areUrlsEqual,function(t,e,l){if(t===e)return!0;if(null==t||null==e)return!1;var s=typeof t;if(s!==typeof e)return!1;if("object"!==s)return"number"===s?u(t,e,l):"function"===s&&i(t,e,l);var h=t.constructor;if(h!==e.constructor)return!1;if(h===Object)return c(t,e,l);if(M(t))return r(t,e,l);if(null!=_&&_(t))return y(t,e,l);if(h===Date)return n(t,e,l);if(h===RegExp)return p(t,e,l);if(h===Map)return a(t,e,l);if(h===Set)return d(t,e,l);var v=C(t);return"[object Date]"===v?n(t,e,l):"[object RegExp]"===v?p(t,e,l):"[object Map]"===v?a(t,e,l):"[object Set]"===v?d(t,e,l):"[object Object]"===v?"function"!=typeof t.then&&"function"!=typeof e.then&&c(t,e,l):"[object URL]"===v?k(t,e,l):"[object Error]"===v?o(t,e,l):"[object Arguments]"===v?c(t,e,l):("[object Boolean]"===v||"[object Number]"===v||"[object String]"===v)&&f(t,e,l)}),R=I?I(L):function(t,e,r,n,o,i,a){return L(t,e,a)};return function(t){var e=t.circular,r=t.comparator,n=t.createState,o=t.equals,i=t.strict;if(n)return function(t,a){var u=n(),c=u.cache;return r(t,a,{cache:void 0===c?e?new WeakMap:void 0:c,equals:o,meta:u.meta,strict:i})};if(e)return function(t,e){return r(t,e,{cache:new WeakMap,equals:o,meta:void 0,strict:i})};var a={cache:void 0,equals:o,meta:void 0,strict:i};return function(t,e){return r(t,e,a)}}({circular:void 0!==D&&D,comparator:L,createState:N,equals:R,strict:void 0!==B&&B})}function N(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=-1;requestAnimationFrame(function n(o){if(r<0&&(r=o),o-r>e)t(o),r=-1;else{var i;i=n,"undefined"!=typeof requestAnimationFrame&&requestAnimationFrame(i)}})}function B(t){return(B="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function L(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function R(t){return(R="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function z(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function U(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?z(Object(r),!0).forEach(function(e){$(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):z(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function $(t,e,r){var n;return(n=function(t,e){if("object"!==R(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==R(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"===R(n)?n:String(n))in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}I({strict:!0}),I({circular:!0}),I({circular:!0,strict:!0}),I({createInternalComparator:function(){return h}}),I({strict:!0,createInternalComparator:function(){return h}}),I({circular:!0,createInternalComparator:function(){return h}}),I({circular:!0,createInternalComparator:function(){return h},strict:!0});var F=function(t){return t},Z=function(t,e){return Object.keys(e).reduce(function(r,n){return U(U({},r),{},$({},n,t(n,e[n])))},{})},W=function(t,e,r){return t.map(function(t){return"".concat(t.replace(/([A-Z])/g,function(t){return"-".concat(t.toLowerCase())})," ").concat(e,"ms ").concat(r)}).join(",")},q=function(t,e,r,n,o,i,a,u){};function Y(t,e){if(t){if("string"==typeof t)return H(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return H(t,e)}}function H(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var X=function(t,e){return[0,3*t,3*e-6*t,3*t-3*e+1]},V=function(t,e){return t.map(function(t,r){return t*Math.pow(e,r)}).reduce(function(t,e){return t+e})},G=function(t,e){return function(r){return V(X(t,e),r)}},K=function(){for(var t,e,r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];var i=n[0],a=n[1],u=n[2],c=n[3];if(1===n.length)switch(n[0]){case"linear":i=0,a=0,u=1,c=1;break;case"ease":i=.25,a=.1,u=.25,c=1;break;case"ease-in":i=.42,a=0,u=1,c=1;break;case"ease-out":i=.42,a=0,u=.58,c=1;break;case"ease-in-out":i=0,a=0,u=.58,c=1;break;default:var l=n[0].split("(");if("cubic-bezier"===l[0]&&4===l[1].split(")")[0].split(",").length){var s,f=function(t){if(Array.isArray(t))return t}(s=l[1].split(")")[0].split(",").map(function(t){return parseFloat(t)}))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(s,4)||Y(s,4)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();i=f[0],a=f[1],u=f[2],c=f[3]}else q(!1,"[configBezier]: arguments should be one of oneOf 'linear', 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', instead received %s",n)}q([i,u,a,c].every(function(t){return"number"==typeof t&&t>=0&&t<=1}),"[configBezier]: arguments should be x1, y1, x2, y2 of [0, 1] instead received %s",n);var p=G(i,u),h=G(a,c),d=(t=i,e=u,function(r){var n;return V([].concat(function(t){if(Array.isArray(t))return H(t)}(n=X(t,e).map(function(t,e){return t*e}).slice(1))||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(n)||Y(n)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),[0]),r)}),y=function(t){for(var e=t>1?1:t,r=e,n=0;n<8;++n){var o,i=p(r)-e,a=d(r);if(1e-4>Math.abs(i-e)||a<1e-4)break;r=(o=r-i/a)>1?1:o<0?0:o}return h(r)};return y.isStepper=!1,y},J=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.stiff,r=void 0===e?100:e,n=t.damping,o=void 0===n?8:n,i=t.dt,a=void 0===i?17:i,u=function(t,e,n){var i=n+(-(t-e)*r-n*o)*a/1e3,u=n*a/1e3+t;return 1e-4>Math.abs(u-e)&&1e-4>Math.abs(i)?[e,0]:[u,i]};return u.isStepper=!0,u.dt=a,u},Q=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];var n=e[0];if("string"==typeof n)switch(n){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return K(n);case"spring":return J();default:if("cubic-bezier"===n.split("(")[0])return K(n);q(!1,"[configEasing]: first argument should be one of 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', 'linear' and 'spring', instead  received %s",e)}return"function"==typeof n?n:(q(!1,"[configEasing]: first argument type should be function or string, instead received %s",e),null)};function tt(t){return(tt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function te(t){return function(t){if(Array.isArray(t))return ta(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||ti(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function tr(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tn(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tr(Object(r),!0).forEach(function(e){to(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tr(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function to(t,e,r){var n;return(n=function(t,e){if("object"!==tt(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==tt(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"===tt(n)?n:String(n))in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ti(t,e){if(t){if("string"==typeof t)return ta(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ta(t,e)}}function ta(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var tu=function(t,e,r){return t+(e-t)*r},tc=function(t){return t.from!==t.to},tl=function t(e,r,n){var o=Z(function(t,r){if(tc(r)){var n,o=function(t){if(Array.isArray(t))return t}(n=e(r.from,r.to,r.velocity))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(n,2)||ti(n,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o[1];return tn(tn({},r),{},{from:i,velocity:a})}return r},r);return n<1?Z(function(t,e){return tc(e)?tn(tn({},e),{},{velocity:tu(e.velocity,o[t].velocity,n),from:tu(e.from,o[t].from,n)}):e},r):t(e,o,n-1)},ts=function(t,e,r,n,o){var i,a,u=[Object.keys(t),Object.keys(e)].reduce(function(t,e){return t.filter(function(t){return e.includes(t)})}),c=u.reduce(function(r,n){return tn(tn({},r),{},to({},n,[t[n],e[n]]))},{}),l=u.reduce(function(r,n){return tn(tn({},r),{},to({},n,{from:t[n],velocity:0,to:e[n]}))},{}),s=-1,f=function(){return null};return f=r.isStepper?function(n){i||(i=n);var a=(n-i)/r.dt;l=tl(r,l,a),o(tn(tn(tn({},t),e),Z(function(t,e){return e.from},l))),i=n,Object.values(l).filter(tc).length&&(s=requestAnimationFrame(f))}:function(i){a||(a=i);var u=(i-a)/n,l=Z(function(t,e){return tu.apply(void 0,te(e).concat([r(u)]))},c);if(o(tn(tn(tn({},t),e),l)),u<1)s=requestAnimationFrame(f);else{var p=Z(function(t,e){return tu.apply(void 0,te(e).concat([r(1)]))},c);o(tn(tn(tn({},t),e),p))}},function(){return requestAnimationFrame(f),function(){cancelAnimationFrame(s)}}};function tf(t){return(tf="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var tp=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function th(t){return function(t){if(Array.isArray(t))return td(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return td(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return td(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function td(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function ty(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tv(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ty(Object(r),!0).forEach(function(e){tm(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ty(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tm(t,e,r){return(e=tg(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function tb(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,tg(n.key),n)}}function tg(t){var e=function(t,e){if("object"!==tf(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==tf(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===tf(e)?e:String(e)}function tx(t,e){return(tx=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function tO(t,e){if(e&&("object"===tf(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return tw(t)}function tw(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function tj(t){return(tj=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var tS=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&tx(t,e)}(a,t);var e,r,o,i=(e=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,r=tj(a);if(e){var n=tj(this).constructor;t=Reflect.construct(r,arguments,n)}else t=r.apply(this,arguments);return tO(this,t)});function a(t,e){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,a);var r,n=(r=i.call(this,t,e)).props,o=n.isActive,u=n.attributeName,c=n.from,l=n.to,s=n.steps,f=n.children,p=n.duration;if(r.handleStyleChange=r.handleStyleChange.bind(tw(r)),r.changeStyle=r.changeStyle.bind(tw(r)),!o||p<=0)return r.state={style:{}},"function"==typeof f&&(r.state={style:l}),tO(r);if(s&&s.length)r.state={style:s[0].style};else if(c){if("function"==typeof f)return r.state={style:c},tO(r);r.state={style:u?tm({},u,c):c}}else r.state={style:{}};return r}return r=[{key:"componentDidMount",value:function(){var t=this.props,e=t.isActive,r=t.canBegin;this.mounted=!0,e&&r&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(t){var e=this.props,r=e.isActive,n=e.canBegin,o=e.attributeName,i=e.shouldReAnimate,a=e.to,u=e.from,c=this.state.style;if(n){if(!r){var l={style:o?tm({},o,a):a};this.state&&c&&(o&&c[o]!==a||!o&&c!==a)&&this.setState(l);return}if(!D(t.to,a)||!t.canBegin||!t.isActive){var s=!t.canBegin||!t.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var f=s||i?u:t.to;if(this.state&&c){var p={style:o?tm({},o,f):f};(o&&c[o]!==f||!o&&c!==f)&&this.setState(p)}this.runAnimation(tv(tv({},this.props),{},{from:f,begin:0}))}}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var t=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),t&&t()}},{key:"handleStyleChange",value:function(t){this.changeStyle(t)}},{key:"changeStyle",value:function(t){this.mounted&&this.setState({style:t})}},{key:"runJSAnimation",value:function(t){var e=this,r=t.from,n=t.to,o=t.duration,i=t.easing,a=t.begin,u=t.onAnimationEnd,c=t.onAnimationStart,l=ts(r,n,Q(i),o,this.changeStyle);this.manager.start([c,a,function(){e.stopJSAnimation=l()},o,u])}},{key:"runStepAnimation",value:function(t){var e=this,r=t.steps,n=t.begin,o=t.onAnimationStart,i=r[0],a=i.style,u=i.duration;return this.manager.start([o].concat(th(r.reduce(function(t,n,o){if(0===o)return t;var i=n.duration,a=n.easing,u=void 0===a?"ease":a,c=n.style,l=n.properties,s=n.onAnimationEnd,f=o>0?r[o-1]:n,p=l||Object.keys(c);if("function"==typeof u||"spring"===u)return[].concat(th(t),[e.runJSAnimation.bind(e,{from:f.style,to:c,duration:i,easing:u}),i]);var h=W(p,i,u),d=tv(tv(tv({},f.style),c),{},{transition:h});return[].concat(th(t),[d,i,s]).filter(F)},[a,Math.max(void 0===u?0:u,n)])),[t.onAnimationEnd]))}},{key:"runAnimation",value:function(t){if(!this.manager){var e,r,n;this.manager=(e=function(){return null},r=!1,n=function t(n){if(!r){if(Array.isArray(n)){if(!n.length)return;var o=function(t){if(Array.isArray(t))return t}(n)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(n)||function(t,e){if(t){if("string"==typeof t)return L(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return L(t,e)}}(n)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o.slice(1);if("number"==typeof i){N(t.bind(null,a),i);return}t(i),N(t.bind(null,a));return}"object"===B(n)&&e(n),"function"==typeof n&&n()}},{stop:function(){r=!0},start:function(t){r=!1,n(t)},subscribe:function(t){return e=t,function(){e=function(){return null}}}})}var o=t.begin,i=t.duration,a=t.attributeName,u=t.to,c=t.easing,l=t.onAnimationStart,s=t.onAnimationEnd,f=t.steps,p=t.children,h=this.manager;if(this.unSubscribe=h.subscribe(this.handleStyleChange),"function"==typeof c||"function"==typeof p||"spring"===c){this.runJSAnimation(t);return}if(f.length>1){this.runStepAnimation(t);return}var d=a?tm({},a,u):u,y=W(Object.keys(d),i,c);h.start([l,o,tv(tv({},d),{},{transition:y}),i,s])}},{key:"render",value:function(){var t=this.props,e=t.children,r=(t.begin,t.duration),o=(t.attributeName,t.easing,t.isActive),i=(t.steps,t.from,t.to,t.canBegin,t.onAnimationEnd,t.shouldReAnimate,t.onAnimationReStart,function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r,n,o={},i=Object.keys(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||(o[r]=t[r]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,tp)),a=n.Children.count(e),u=this.state.style;if("function"==typeof e)return e(u);if(!o||0===a||r<=0)return e;var c=function(t){var e=t.props,r=e.style,o=e.className;return(0,n.cloneElement)(t,tv(tv({},i),{},{style:tv(tv({},void 0===r?{}:r),u),className:o}))};return 1===a?c(n.Children.only(e)):n.createElement("div",null,n.Children.map(e,function(t){return c(t)}))}}],tb(a.prototype,r),o&&tb(a,o),Object.defineProperty(a,"prototype",{writable:!1}),a}(n.PureComponent);tS.displayName="Animate",tS.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}},tS.propTypes={from:i().oneOfType([i().object,i().string]),to:i().oneOfType([i().object,i().string]),attributeName:i().string,duration:i().number,begin:i().number,easing:i().oneOfType([i().string,i().func]),steps:i().arrayOf(i().shape({duration:i().number.isRequired,style:i().object.isRequired,easing:i().oneOfType([i().oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),i().func]),properties:i().arrayOf("string"),onAnimationEnd:i().func})),children:i().oneOfType([i().node,i().func]),isActive:i().bool,canBegin:i().bool,onAnimationEnd:i().func,shouldReAnimate:i().bool,onAnimationStart:i().func,onAnimationReStart:i().func};var tP=tS},31346:function(t,e,r){"use strict";r.d(e,{$:function(){return U}});var n=r(2265),o=r(90794),i=r(98917),a=r(62927),u=r.n(a),c=r(66952),l=r.n(c),s=r(88357),f=r(42494),p=r(36612),h=r(561),d=r(97281),y=r(43843),v=r(3841),m=r(41586),b=r(12655),g=r(16389),x=r(77688),O=["x","y"];function w(t){return(w="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function j(){return(j=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function S(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function P(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?S(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=w(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=w(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==w(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):S(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function A(t,e){var r=t.x,n=t.y,o=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,O),i=parseInt("".concat(r),10),a=parseInt("".concat(n),10),u=parseInt("".concat(e.height||o.height),10),c=parseInt("".concat(e.width||o.width),10);return P(P(P(P(P({},e),o),i?{x:i}:{}),a?{y:a}:{}),{},{height:u,width:c,name:e.name,radius:e.radius})}function E(t){return n.createElement(x.bn,j({shapeType:"rectangle",propTransformer:A,activeClassName:"recharts-active-bar"},t))}var k=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return function(r,n){if("number"==typeof t)return t;var o="number"==typeof r;return o?t(r,n):(o||(0,g.Z)(!1),e)}},M=["value","background"];function _(t){return(_="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function T(){return(T=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function C(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function D(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?C(Object(r),!0).forEach(function(e){R(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):C(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function I(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,z(n.key),n)}}function N(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(N=function(){return!!t})()}function B(t){return(B=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function L(t,e){return(L=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function R(t,e,r){return(e=z(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function z(t){var e=function(t,e){if("object"!=_(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=_(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==_(e)?e:e+""}var U=function(t){var e,r;function a(){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,a);for(var t,e,r,n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];return e=a,r=[].concat(o),e=B(e),R(t=function(t,e){if(e&&("object"===_(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,N()?Reflect.construct(e,r||[],B(this).constructor):e.apply(this,r)),"state",{isAnimationFinished:!1}),R(t,"id",(0,d.EL)("recharts-bar-")),R(t,"handleAnimationEnd",function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),e&&e()}),R(t,"handleAnimationStart",function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),e&&e()}),t}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&L(t,e)}(a,t),e=[{key:"renderRectanglesStatically",value:function(t){var e=this,r=this.props,o=r.shape,i=r.dataKey,a=r.activeIndex,u=r.activeBar,c=(0,y.L6)(this.props,!1);return t&&t.map(function(t,r){var l=r===a,f=D(D(D({},c),t),{},{isActive:l,option:l?u:o,index:r,dataKey:i,onAnimationStart:e.handleAnimationStart,onAnimationEnd:e.handleAnimationEnd});return n.createElement(s.m,T({className:"recharts-bar-rectangle"},(0,b.bw)(e.props,t,r),{key:"rectangle-".concat(null==t?void 0:t.x,"-").concat(null==t?void 0:t.y,"-").concat(null==t?void 0:t.value,"-").concat(r)}),n.createElement(E,f))})}},{key:"renderRectanglesWithAnimation",value:function(){var t=this,e=this.props,r=e.data,o=e.layout,a=e.isAnimationActive,u=e.animationBegin,c=e.animationDuration,l=e.animationEasing,f=e.animationId,p=this.state.prevData;return n.createElement(i.ZP,{begin:u,duration:c,isActive:a,easing:l,from:{t:0},to:{t:1},key:"bar-".concat(f),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(e){var i=e.t,a=r.map(function(t,e){var r=p&&p[e];if(r){var n=(0,d.k4)(r.x,t.x),a=(0,d.k4)(r.y,t.y),u=(0,d.k4)(r.width,t.width),c=(0,d.k4)(r.height,t.height);return D(D({},t),{},{x:n(i),y:a(i),width:u(i),height:c(i)})}if("horizontal"===o){var l=(0,d.k4)(0,t.height)(i);return D(D({},t),{},{y:t.y+t.height-l,height:l})}var s=(0,d.k4)(0,t.width)(i);return D(D({},t),{},{width:s})});return n.createElement(s.m,null,t.renderRectanglesStatically(a))})}},{key:"renderRectangles",value:function(){var t=this.props,e=t.data,r=t.isAnimationActive,n=this.state.prevData;return r&&e&&e.length&&(!n||!u()(n,e))?this.renderRectanglesWithAnimation():this.renderRectanglesStatically(e)}},{key:"renderBackground",value:function(){var t=this,e=this.props,r=e.data,o=e.dataKey,i=e.activeIndex,a=(0,y.L6)(this.props.background,!1);return r.map(function(e,r){e.value;var u=e.background,c=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(e,M);if(!u)return null;var l=D(D(D(D(D({},c),{},{fill:"#eee"},u),a),(0,b.bw)(t.props,e,r)),{},{onAnimationStart:t.handleAnimationStart,onAnimationEnd:t.handleAnimationEnd,dataKey:o,index:r,className:"recharts-bar-background-rectangle"});return n.createElement(E,T({key:"background-bar-".concat(r),option:t.props.background,isActive:r===i},l))})}},{key:"renderErrorBar",value:function(t,e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,o=r.data,i=r.xAxis,a=r.yAxis,u=r.layout,c=r.children,l=(0,y.NN)(c,f.W);if(!l)return null;var p="vertical"===u?o[0].height/2:o[0].width/2,h=function(t,e){var r=Array.isArray(t.value)?t.value[1]:t.value;return{x:t.x,y:t.y,value:r,errorVal:(0,m.F$)(t,e)}};return n.createElement(s.m,{clipPath:t?"url(#clipPath-".concat(e,")"):null},l.map(function(t){return n.cloneElement(t,{key:"error-bar-".concat(e,"-").concat(t.props.dataKey),data:o,xAxis:i,yAxis:a,layout:u,offset:p,dataPointFormatter:h})}))}},{key:"render",value:function(){var t=this.props,e=t.hide,r=t.data,i=t.className,a=t.xAxis,u=t.yAxis,c=t.left,f=t.top,p=t.width,d=t.height,y=t.isAnimationActive,v=t.background,m=t.id;if(e||!r||!r.length)return null;var b=this.state.isAnimationFinished,g=(0,o.Z)("recharts-bar",i),x=a&&a.allowDataOverflow,O=u&&u.allowDataOverflow,w=x||O,j=l()(m)?this.id:m;return n.createElement(s.m,{className:g},x||O?n.createElement("defs",null,n.createElement("clipPath",{id:"clipPath-".concat(j)},n.createElement("rect",{x:x?c:c-p/2,y:O?f:f-d/2,width:x?p:2*p,height:O?d:2*d}))):null,n.createElement(s.m,{className:"recharts-bar-rectangles",clipPath:w?"url(#clipPath-".concat(j,")"):null},v?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(w,j),(!y||b)&&h.e.renderCallByParent(this.props,r))}}],r=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curData:t.data,prevData:e.curData}:t.data!==e.curData?{curData:t.data}:null}}],e&&I(a.prototype,e),r&&I(a,r),Object.defineProperty(a,"prototype",{writable:!1}),a}(n.PureComponent);R(U,"displayName","Bar"),R(U,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!v.x.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"}),R(U,"getComposedData",function(t){var e=t.props,r=t.item,n=t.barPosition,o=t.bandSize,i=t.xAxis,a=t.yAxis,u=t.xAxisTicks,c=t.yAxisTicks,l=t.stackedData,s=t.dataStartIndex,f=t.displayedData,h=t.offset,v=(0,m.Bu)(n,r);if(!v)return null;var b=e.layout,g=r.type.defaultProps,x=void 0!==g?D(D({},g),r.props):r.props,O=x.dataKey,w=x.children,j=x.minPointSize,S="horizontal"===b?a:i,P=l?S.scale.domain():null,A=(0,m.Yj)({numericAxis:S}),E=(0,y.NN)(w,p.b),M=f.map(function(t,e){l?f=(0,m.Vv)(l[s+e],P):Array.isArray(f=(0,m.F$)(t,O))||(f=[A,f]);var n=k(j,U.defaultProps.minPointSize)(f[1],e);if("horizontal"===b){var f,p,h,y,g,x,w,S=[a.scale(f[0]),a.scale(f[1])],M=S[0],_=S[1];p=(0,m.Fy)({axis:i,ticks:u,bandSize:o,offset:v.offset,entry:t,index:e}),h=null!==(w=null!=_?_:M)&&void 0!==w?w:void 0,y=v.size;var T=M-_;if(g=Number.isNaN(T)?0:T,x={x:p,y:a.y,width:y,height:a.height},Math.abs(n)>0&&Math.abs(g)<Math.abs(n)){var C=(0,d.uY)(g||n)*(Math.abs(n)-Math.abs(g));h-=C,g+=C}}else{var I=[i.scale(f[0]),i.scale(f[1])],N=I[0],B=I[1];if(p=N,h=(0,m.Fy)({axis:a,ticks:c,bandSize:o,offset:v.offset,entry:t,index:e}),y=B-N,g=v.size,x={x:i.x,y:h,width:i.width,height:g},Math.abs(n)>0&&Math.abs(y)<Math.abs(n)){var L=(0,d.uY)(y||n)*(Math.abs(n)-Math.abs(y));y+=L}}return D(D(D({},t),{},{x:p,y:h,width:y,height:g,value:l?f:f[1],payload:t,background:x},E&&E[e]&&E[e].props),{},{tooltipPayload:[(0,m.Qo)(r,t)],tooltipPosition:{x:p+y/2,y:h+g/2}})});return D({data:M,layout:b},h)})},41573:function(t,e,r){"use strict";r.d(e,{O:function(){return T}});var n=r(2265),o=r(39390),i=r.n(o),a=r(1995),u=r.n(a),c=r(90794),l=r(1374),s=r(88357),f=r(71224),p=r(33343),h=r(97281),d=r(12655),y=r(43843),v=r(37089),m=["viewBox"],b=["viewBox"],g=["ticks"];function x(t){return(x="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function O(){return(O=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function w(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function j(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?w(Object(r),!0).forEach(function(e){M(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):w(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function S(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function P(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,_(n.key),n)}}function A(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(A=function(){return!!t})()}function E(t){return(E=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function k(t,e){return(k=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function M(t,e,r){return(e=_(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function _(t){var e=function(t,e){if("object"!=x(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=x(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==x(e)?e:e+""}var T=function(t){var e,r;function o(t){var e,r,n;return!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,o),r=o,n=[t],r=E(r),(e=function(t,e){if(e&&("object"===x(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,A()?Reflect.construct(r,n||[],E(this).constructor):r.apply(this,n))).state={fontSize:"",letterSpacing:""},e}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&k(t,e)}(o,t),e=[{key:"shouldComponentUpdate",value:function(t,e){var r=t.viewBox,n=S(t,m),o=this.props,i=o.viewBox,a=S(o,b);return!(0,l.w)(r,i)||!(0,l.w)(n,a)||!(0,l.w)(e,this.state)}},{key:"componentDidMount",value:function(){var t=this.layerReference;if(t){var e=t.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];e&&this.setState({fontSize:window.getComputedStyle(e).fontSize,letterSpacing:window.getComputedStyle(e).letterSpacing})}}},{key:"getTickLineCoord",value:function(t){var e,r,n,o,i,a,u=this.props,c=u.x,l=u.y,s=u.width,f=u.height,p=u.orientation,d=u.tickSize,y=u.mirror,v=u.tickMargin,m=y?-1:1,b=t.tickSize||d,g=(0,h.hj)(t.tickCoord)?t.tickCoord:t.coordinate;switch(p){case"top":e=r=t.coordinate,a=(n=(o=l+ +!y*f)-m*b)-m*v,i=g;break;case"left":n=o=t.coordinate,i=(e=(r=c+ +!y*s)-m*b)-m*v,a=g;break;case"right":n=o=t.coordinate,i=(e=(r=c+ +y*s)+m*b)+m*v,a=g;break;default:e=r=t.coordinate,a=(n=(o=l+ +y*f)+m*b)+m*v,i=g}return{line:{x1:e,y1:n,x2:r,y2:o},tick:{x:i,y:a}}}},{key:"getTickTextAnchor",value:function(){var t,e=this.props,r=e.orientation,n=e.mirror;switch(r){case"left":t=n?"start":"end";break;case"right":t=n?"end":"start";break;default:t="middle"}return t}},{key:"getTickVerticalAnchor",value:function(){var t=this.props,e=t.orientation,r=t.mirror,n="end";switch(e){case"left":case"right":n="middle";break;case"top":n=r?"start":"end";break;default:n=r?"end":"start"}return n}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.x,r=t.y,o=t.width,i=t.height,a=t.orientation,l=t.mirror,s=t.axisLine,f=j(j(j({},(0,y.L6)(this.props,!1)),(0,y.L6)(s,!1)),{},{fill:"none"});if("top"===a||"bottom"===a){var p=+("top"===a&&!l||"bottom"===a&&l);f=j(j({},f),{},{x1:e,y1:r+p*i,x2:e+o,y2:r+p*i})}else{var h=+("left"===a&&!l||"right"===a&&l);f=j(j({},f),{},{x1:e+h*o,y1:r,x2:e+h*o,y2:r+i})}return n.createElement("line",O({},f,{className:(0,c.Z)("recharts-cartesian-axis-line",u()(s,"className"))}))}},{key:"renderTicks",value:function(t,e,r){var a=this,l=this.props,f=l.tickLine,p=l.stroke,h=l.tick,m=l.tickFormatter,b=l.unit,g=(0,v.f)(j(j({},this.props),{},{ticks:t}),e,r),x=this.getTickTextAnchor(),w=this.getTickVerticalAnchor(),S=(0,y.L6)(this.props,!1),P=(0,y.L6)(h,!1),A=j(j({},S),{},{fill:"none"},(0,y.L6)(f,!1)),E=g.map(function(t,e){var r=a.getTickLineCoord(t),l=r.line,y=r.tick,v=j(j(j(j({textAnchor:x,verticalAnchor:w},S),{},{stroke:"none",fill:p},P),y),{},{index:e,payload:t,visibleTicksCount:g.length,tickFormatter:m});return n.createElement(s.m,O({className:"recharts-cartesian-axis-tick",key:"tick-".concat(t.value,"-").concat(t.coordinate,"-").concat(t.tickCoord)},(0,d.bw)(a.props,t,e)),f&&n.createElement("line",O({},A,l,{className:(0,c.Z)("recharts-cartesian-axis-tick-line",u()(f,"className"))})),h&&o.renderTickItem(h,v,"".concat(i()(m)?m(t.value,e):t.value).concat(b||"")))});return n.createElement("g",{className:"recharts-cartesian-axis-ticks"},E)}},{key:"render",value:function(){var t=this,e=this.props,r=e.axisLine,o=e.width,a=e.height,u=e.ticksGenerator,l=e.className;if(e.hide)return null;var f=this.props,h=f.ticks,d=S(f,g),y=h;return(i()(u)&&(y=u(h&&h.length>0?this.props:d)),o<=0||a<=0||!y||!y.length)?null:n.createElement(s.m,{className:(0,c.Z)("recharts-cartesian-axis",l),ref:function(e){t.layerReference=e}},r&&this.renderAxisLine(),this.renderTicks(y,this.state.fontSize,this.state.letterSpacing),p._.renderCallByParent(this.props))}}],r=[{key:"renderTickItem",value:function(t,e,r){return n.isValidElement(t)?n.cloneElement(t,e):i()(t)?t(e):n.createElement(f.x,O({},e,{className:"recharts-cartesian-axis-tick-value"}),r)}}],e&&P(o.prototype,e),r&&P(o,r),Object.defineProperty(o,"prototype",{writable:!1}),o}(n.Component);M(T,"displayName","CartesianAxis"),M(T,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"})},16573:function(t,e,r){"use strict";r.d(e,{q:function(){return M}});var n=r(2265),o=r(39390),i=r.n(o),a=r(47205),u=r(97281),c=r(43843),l=r(41586),s=r(37089),f=r(41573),p=r(32794),h=["x1","y1","x2","y2","key"],d=["offset"];function y(t){return(y="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function v(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function m(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?v(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=y(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=y(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==y(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):v(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function b(){return(b=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function g(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var x=function(t){var e=t.fill;if(!e||"none"===e)return null;var r=t.fillOpacity,o=t.x,i=t.y,a=t.width,u=t.height,c=t.ry;return n.createElement("rect",{x:o,y:i,ry:c,width:a,height:u,stroke:"none",fill:e,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function O(t,e){var r;if(n.isValidElement(t))r=n.cloneElement(t,e);else if(i()(t))r=t(e);else{var o=e.x1,a=e.y1,u=e.x2,l=e.y2,s=e.key,f=g(e,h),p=(0,c.L6)(f,!1),y=(p.offset,g(p,d));r=n.createElement("line",b({},y,{x1:o,y1:a,x2:u,y2:l,fill:"none",key:s}))}return r}function w(t){var e=t.x,r=t.width,o=t.horizontal,i=void 0===o||o,a=t.horizontalPoints;if(!i||!a||!a.length)return null;var u=a.map(function(n,o){return O(i,m(m({},t),{},{x1:e,y1:n,x2:e+r,y2:n,key:"line-".concat(o),index:o}))});return n.createElement("g",{className:"recharts-cartesian-grid-horizontal"},u)}function j(t){var e=t.y,r=t.height,o=t.vertical,i=void 0===o||o,a=t.verticalPoints;if(!i||!a||!a.length)return null;var u=a.map(function(n,o){return O(i,m(m({},t),{},{x1:n,y1:e,x2:n,y2:e+r,key:"line-".concat(o),index:o}))});return n.createElement("g",{className:"recharts-cartesian-grid-vertical"},u)}function S(t){var e=t.horizontalFill,r=t.fillOpacity,o=t.x,i=t.y,a=t.width,u=t.height,c=t.horizontalPoints,l=t.horizontal;if(!(void 0===l||l)||!e||!e.length)return null;var s=c.map(function(t){return Math.round(t+i-i)}).sort(function(t,e){return t-e});i!==s[0]&&s.unshift(0);var f=s.map(function(t,c){var l=s[c+1]?s[c+1]-t:i+u-t;if(l<=0)return null;var f=c%e.length;return n.createElement("rect",{key:"react-".concat(c),y:t,x:o,height:l,width:a,stroke:"none",fill:e[f],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return n.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},f)}function P(t){var e=t.vertical,r=t.verticalFill,o=t.fillOpacity,i=t.x,a=t.y,u=t.width,c=t.height,l=t.verticalPoints;if(!(void 0===e||e)||!r||!r.length)return null;var s=l.map(function(t){return Math.round(t+i-i)}).sort(function(t,e){return t-e});i!==s[0]&&s.unshift(0);var f=s.map(function(t,e){var l=s[e+1]?s[e+1]-t:i+u-t;if(l<=0)return null;var f=e%r.length;return n.createElement("rect",{key:"react-".concat(e),x:t,y:a,width:l,height:c,stroke:"none",fill:r[f],fillOpacity:o,className:"recharts-cartesian-grid-bg"})});return n.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},f)}var A=function(t,e){var r=t.xAxis,n=t.width,o=t.height,i=t.offset;return(0,l.Rf)((0,s.f)(m(m(m({},f.O.defaultProps),r),{},{ticks:(0,l.uY)(r,!0),viewBox:{x:0,y:0,width:n,height:o}})),i.left,i.left+i.width,e)},E=function(t,e){var r=t.yAxis,n=t.width,o=t.height,i=t.offset;return(0,l.Rf)((0,s.f)(m(m(m({},f.O.defaultProps),r),{},{ticks:(0,l.uY)(r,!0),viewBox:{x:0,y:0,width:n,height:o}})),i.top,i.top+i.height,e)},k={horizontal:!0,vertical:!0,stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function M(t){var e,r,o,c,l,s,f=(0,p.zn)(),h=(0,p.Mw)(),d=(0,p.qD)(),v=m(m({},t),{},{stroke:null!==(e=t.stroke)&&void 0!==e?e:k.stroke,fill:null!==(r=t.fill)&&void 0!==r?r:k.fill,horizontal:null!==(o=t.horizontal)&&void 0!==o?o:k.horizontal,horizontalFill:null!==(c=t.horizontalFill)&&void 0!==c?c:k.horizontalFill,vertical:null!==(l=t.vertical)&&void 0!==l?l:k.vertical,verticalFill:null!==(s=t.verticalFill)&&void 0!==s?s:k.verticalFill,x:(0,u.hj)(t.x)?t.x:d.left,y:(0,u.hj)(t.y)?t.y:d.top,width:(0,u.hj)(t.width)?t.width:d.width,height:(0,u.hj)(t.height)?t.height:d.height}),g=v.x,O=v.y,M=v.width,_=v.height,T=v.syncWithTicks,C=v.horizontalValues,D=v.verticalValues,I=(0,p.CW)(),N=(0,p.Nf)();if(!(0,u.hj)(M)||M<=0||!(0,u.hj)(_)||_<=0||!(0,u.hj)(g)||g!==+g||!(0,u.hj)(O)||O!==+O)return null;var B=v.verticalCoordinatesGenerator||A,L=v.horizontalCoordinatesGenerator||E,R=v.horizontalPoints,z=v.verticalPoints;if((!R||!R.length)&&i()(L)){var U=C&&C.length,$=L({yAxis:N?m(m({},N),{},{ticks:U?C:N.ticks}):void 0,width:f,height:h,offset:d},!!U||T);(0,a.Z)(Array.isArray($),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(y($),"]")),Array.isArray($)&&(R=$)}if((!z||!z.length)&&i()(B)){var F=D&&D.length,Z=B({xAxis:I?m(m({},I),{},{ticks:F?D:I.ticks}):void 0,width:f,height:h,offset:d},!!F||T);(0,a.Z)(Array.isArray(Z),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(y(Z),"]")),Array.isArray(Z)&&(z=Z)}return n.createElement("g",{className:"recharts-cartesian-grid"},n.createElement(x,{fill:v.fill,fillOpacity:v.fillOpacity,x:v.x,y:v.y,width:v.width,height:v.height,ry:v.ry}),n.createElement(w,b({},v,{offset:d,horizontalPoints:R,xAxis:I,yAxis:N})),n.createElement(j,b({},v,{offset:d,verticalPoints:z,xAxis:I,yAxis:N})),n.createElement(S,b({},v,{horizontalPoints:R})),n.createElement(P,b({},v,{verticalPoints:z})))}M.displayName="CartesianGrid"},42494:function(t,e,r){"use strict";r.d(e,{W:function(){return m}});var n=r(2265),o=r(16389),i=r(88357),a=r(43843),u=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function c(t){return(c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function l(){return(l=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function s(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function f(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,v(n.key),n)}}function p(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(p=function(){return!!t})()}function h(t){return(h=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function d(t,e){return(d=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function y(t,e,r){return(e=v(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function v(t){var e=function(t,e){if("object"!=c(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=c(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==c(e)?e:e+""}var m=function(t){var e,r;function y(){var t,e;return!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,y),t=y,e=arguments,t=h(t),function(t,e){if(e&&("object"===c(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,p()?Reflect.construct(t,e||[],h(this).constructor):t.apply(this,e))}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&d(t,e)}(y,t),e=[{key:"render",value:function(){var t=this.props,e=t.offset,r=t.layout,c=t.width,f=t.dataKey,p=t.data,h=t.dataPointFormatter,d=t.xAxis,y=t.yAxis,v=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,u),m=(0,a.L6)(v,!1);"x"===this.props.direction&&"number"!==d.type&&(0,o.Z)(!1);var b=p.map(function(t){var o,a,u=h(t,f),p=u.x,v=u.y,b=u.value,g=u.errorVal;if(!g)return null;var x=[];if(Array.isArray(g)){var O=function(t){if(Array.isArray(t))return t}(g)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(g,2)||function(t,e){if(t){if("string"==typeof t)return s(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return s(t,e)}}(g,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();o=O[0],a=O[1]}else o=a=g;if("vertical"===r){var w=d.scale,j=v+e,S=j+c,P=j-c,A=w(b-o),E=w(b+a);x.push({x1:E,y1:S,x2:E,y2:P}),x.push({x1:A,y1:j,x2:E,y2:j}),x.push({x1:A,y1:S,x2:A,y2:P})}else if("horizontal"===r){var k=y.scale,M=p+e,_=M-c,T=M+c,C=k(b-o),D=k(b+a);x.push({x1:_,y1:D,x2:T,y2:D}),x.push({x1:M,y1:C,x2:M,y2:D}),x.push({x1:_,y1:C,x2:T,y2:C})}return n.createElement(i.m,l({className:"recharts-errorBar",key:"bar-".concat(x.map(function(t){return"".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))},m),x.map(function(t){return n.createElement("line",l({},t,{key:"line-".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))}))});return n.createElement(i.m,{className:"recharts-errorBars"},b)}}],f(y.prototype,e),r&&f(y,r),Object.defineProperty(y,"prototype",{writable:!1}),y}(n.Component);y(m,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"}),y(m,"displayName","ErrorBar")},19223:function(t,e,r){"use strict";r.d(e,{x:function(){return B}});var n=r(2265),o=r(98917),i=r(39390),a=r.n(i),u=r(66952),c=r.n(u),l=r(62927),s=r.n(l),f=r(90794),p=r(11697),h=r(14304),d=r(88357),y=r(561),v=r(42494),m=r(97281),b=r(43843),g=r(3841),x=r(41586),O=["type","layout","connectNulls","ref"],w=["key"];function j(t){return(j="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function S(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function P(){return(P=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function A(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function E(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?A(Object(r),!0).forEach(function(e){I(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):A(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function k(t){return function(t){if(Array.isArray(t))return M(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return M(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return M(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function M(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function _(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,N(n.key),n)}}function T(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(T=function(){return!!t})()}function C(t){return(C=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function D(t,e){return(D=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function I(t,e,r){return(e=N(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function N(t){var e=function(t,e){if("object"!=j(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=j(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==j(e)?e:e+""}var B=function(t){var e,r;function i(){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,i);for(var t,e,r,n=arguments.length,o=Array(n),a=0;a<n;a++)o[a]=arguments[a];return e=i,r=[].concat(o),e=C(e),I(t=function(t,e){if(e&&("object"===j(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,T()?Reflect.construct(e,r||[],C(this).constructor):e.apply(this,r)),"state",{isAnimationFinished:!0,totalLength:0}),I(t,"generateSimpleStrokeDasharray",function(t,e){return"".concat(e,"px ").concat(t-e,"px")}),I(t,"getStrokeDasharray",function(e,r,n){var o=n.reduce(function(t,e){return t+e});if(!o)return t.generateSimpleStrokeDasharray(r,e);for(var a=e%o,u=r-e,c=[],l=0,s=0;l<n.length;s+=n[l],++l)if(s+n[l]>a){c=[].concat(k(n.slice(0,l)),[a-s]);break}var f=c.length%2==0?[0,u]:[u];return[].concat(k(i.repeat(n,Math.floor(e/o))),k(c),f).map(function(t){return"".concat(t,"px")}).join(", ")}),I(t,"id",(0,m.EL)("recharts-line-")),I(t,"pathRef",function(e){t.mainCurve=e}),I(t,"handleAnimationEnd",function(){t.setState({isAnimationFinished:!0}),t.props.onAnimationEnd&&t.props.onAnimationEnd()}),I(t,"handleAnimationStart",function(){t.setState({isAnimationFinished:!1}),t.props.onAnimationStart&&t.props.onAnimationStart()}),t}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&D(t,e)}(i,t),e=[{key:"componentDidMount",value:function(){if(this.props.isAnimationActive){var t=this.getTotalLength();this.setState({totalLength:t})}}},{key:"componentDidUpdate",value:function(){if(this.props.isAnimationActive){var t=this.getTotalLength();t!==this.state.totalLength&&this.setState({totalLength:t})}}},{key:"getTotalLength",value:function(){var t=this.mainCurve;try{return t&&t.getTotalLength&&t.getTotalLength()||0}catch(t){return 0}}},{key:"renderErrorBar",value:function(t,e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,o=r.points,i=r.xAxis,a=r.yAxis,u=r.layout,c=r.children,l=(0,b.NN)(c,v.W);if(!l)return null;var s=function(t,e){return{x:t.x,y:t.y,value:t.value,errorVal:(0,x.F$)(t.payload,e)}};return n.createElement(d.m,{clipPath:t?"url(#clipPath-".concat(e,")"):null},l.map(function(t){return n.cloneElement(t,{key:"bar-".concat(t.props.dataKey),data:o,xAxis:i,yAxis:a,layout:u,dataPointFormatter:s})}))}},{key:"renderDots",value:function(t,e,r){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var o=this.props,a=o.dot,u=o.points,c=o.dataKey,l=(0,b.L6)(this.props,!1),s=(0,b.L6)(a,!0),f=u.map(function(t,e){var r=E(E(E({key:"dot-".concat(e),r:3},l),s),{},{index:e,cx:t.x,cy:t.y,value:t.value,dataKey:c,payload:t.payload,points:u});return i.renderDotItem(a,r)}),p={clipPath:t?"url(#clipPath-".concat(e?"":"dots-").concat(r,")"):null};return n.createElement(d.m,P({className:"recharts-line-dots",key:"dots"},p),f)}},{key:"renderCurveStatically",value:function(t,e,r,o){var i=this.props,a=i.type,u=i.layout,c=i.connectNulls,l=(i.ref,S(i,O)),s=E(E(E({},(0,b.L6)(l,!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:e?"url(#clipPath-".concat(r,")"):null,points:t},o),{},{type:a,layout:u,connectNulls:c});return n.createElement(p.H,P({},s,{pathRef:this.pathRef}))}},{key:"renderCurveWithAnimation",value:function(t,e){var r=this,i=this.props,a=i.points,u=i.strokeDasharray,c=i.isAnimationActive,l=i.animationBegin,s=i.animationDuration,f=i.animationEasing,p=i.animationId,h=i.animateNewValues,d=i.width,y=i.height,v=this.state,b=v.prevPoints,g=v.totalLength;return n.createElement(o.ZP,{begin:l,duration:s,isActive:c,easing:f,from:{t:0},to:{t:1},key:"line-".concat(p),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(n){var o,i=n.t;if(b){var c=b.length/a.length,l=a.map(function(t,e){var r=Math.floor(e*c);if(b[r]){var n=b[r],o=(0,m.k4)(n.x,t.x),a=(0,m.k4)(n.y,t.y);return E(E({},t),{},{x:o(i),y:a(i)})}if(h){var u=(0,m.k4)(2*d,t.x),l=(0,m.k4)(y/2,t.y);return E(E({},t),{},{x:u(i),y:l(i)})}return E(E({},t),{},{x:t.x,y:t.y})});return r.renderCurveStatically(l,t,e)}var s=(0,m.k4)(0,g)(i);if(u){var f="".concat(u).split(/[,\s]+/gim).map(function(t){return parseFloat(t)});o=r.getStrokeDasharray(s,g,f)}else o=r.generateSimpleStrokeDasharray(g,s);return r.renderCurveStatically(a,t,e,{strokeDasharray:o})})}},{key:"renderCurve",value:function(t,e){var r=this.props,n=r.points,o=r.isAnimationActive,i=this.state,a=i.prevPoints,u=i.totalLength;return o&&n&&n.length&&(!a&&u>0||!s()(a,n))?this.renderCurveWithAnimation(t,e):this.renderCurveStatically(n,t,e)}},{key:"render",value:function(){var t,e=this.props,r=e.hide,o=e.dot,i=e.points,a=e.className,u=e.xAxis,l=e.yAxis,s=e.top,p=e.left,h=e.width,v=e.height,m=e.isAnimationActive,g=e.id;if(r||!i||!i.length)return null;var x=this.state.isAnimationFinished,O=1===i.length,w=(0,f.Z)("recharts-line",a),j=u&&u.allowDataOverflow,S=l&&l.allowDataOverflow,P=j||S,A=c()(g)?this.id:g,E=null!==(t=(0,b.L6)(o,!1))&&void 0!==t?t:{r:3,strokeWidth:2},k=E.r,M=E.strokeWidth,_=((0,b.jf)(o)?o:{}).clipDot,T=void 0===_||_,C=2*(void 0===k?3:k)+(void 0===M?2:M);return n.createElement(d.m,{className:w},j||S?n.createElement("defs",null,n.createElement("clipPath",{id:"clipPath-".concat(A)},n.createElement("rect",{x:j?p:p-h/2,y:S?s:s-v/2,width:j?h:2*h,height:S?v:2*v})),!T&&n.createElement("clipPath",{id:"clipPath-dots-".concat(A)},n.createElement("rect",{x:p-C/2,y:s-C/2,width:h+C,height:v+C}))):null,!O&&this.renderCurve(P,A),this.renderErrorBar(P,A),(O||o)&&this.renderDots(P,T,A),(!m||x)&&y.e.renderCallByParent(this.props,i))}}],r=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curPoints:t.points,prevPoints:e.curPoints}:t.points!==e.curPoints?{curPoints:t.points}:null}},{key:"repeat",value:function(t,e){for(var r=t.length%2!=0?[].concat(k(t),[0]):t,n=[],o=0;o<e;++o)n=[].concat(k(n),k(r));return n}},{key:"renderDotItem",value:function(t,e){var r;if(n.isValidElement(t))r=n.cloneElement(t,e);else if(a()(t))r=t(e);else{var o=e.key,i=S(e,w),u=(0,f.Z)("recharts-line-dot","boolean"!=typeof t?t.className:"");r=n.createElement(h.o,P({key:o},i,{className:u}))}return r}}],e&&_(i.prototype,e),r&&_(i,r),Object.defineProperty(i,"prototype",{writable:!1}),i}(n.PureComponent);I(B,"displayName","Line"),I(B,"defaultProps",{xAxisId:0,yAxisId:0,connectNulls:!1,activeDot:!0,dot:!0,legendType:"line",stroke:"#3182bd",strokeWidth:1,fill:"#fff",points:[],isAnimationActive:!g.x.isSsr,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",hide:!1,label:!1}),I(B,"getComposedData",function(t){var e=t.props,r=t.xAxis,n=t.yAxis,o=t.xAxisTicks,i=t.yAxisTicks,a=t.dataKey,u=t.bandSize,l=t.displayedData,s=t.offset,f=e.layout;return E({points:l.map(function(t,e){var l=(0,x.F$)(t,a);return"horizontal"===f?{x:(0,x.Hv)({axis:r,ticks:o,bandSize:u,entry:t,index:e}),y:c()(l)?null:n.scale(l),value:l,payload:t}:{x:c()(l)?null:r.scale(l),y:(0,x.Hv)({axis:n,ticks:i,bandSize:u,entry:t,index:e}),value:l,payload:t}}),layout:f},s)})},24235:function(t,e,r){"use strict";r.d(e,{K:function(){return m}});var n=r(2265),o=r(90794),i=r(32794),a=r(41573),u=r(41586);function c(t){return(c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function l(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,d(n.key),n)}}function s(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(s=function(){return!!t})()}function f(t){return(f=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function p(t,e){return(p=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function h(t,e,r){return(e=d(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function d(t){var e=function(t,e){if("object"!=c(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=c(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==c(e)?e:e+""}function y(){return(y=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function v(t){var e=t.xAxisId,r=(0,i.zn)(),c=(0,i.Mw)(),l=(0,i.bH)(e);return null==l?null:n.createElement(a.O,y({},l,{className:(0,o.Z)("recharts-".concat(l.axisType," ").concat(l.axisType),l.className),viewBox:{x:0,y:0,width:r,height:c},ticksGenerator:function(t){return(0,u.uY)(t,!0)}}))}var m=function(t){var e,r;function o(){var t,e;return!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,o),t=o,e=arguments,t=f(t),function(t,e){if(e&&("object"===c(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,s()?Reflect.construct(t,e||[],f(this).constructor):t.apply(this,e))}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&p(t,e)}(o,t),e=[{key:"render",value:function(){return n.createElement(v,this.props)}}],l(o.prototype,e),r&&l(o,r),Object.defineProperty(o,"prototype",{writable:!1}),o}(n.Component);h(m,"displayName","XAxis"),h(m,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0})},50039:function(t,e,r){"use strict";r.d(e,{B:function(){return m}});var n=r(2265),o=r(90794),i=r(32794),a=r(41573),u=r(41586);function c(t){return(c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function l(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,d(n.key),n)}}function s(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(s=function(){return!!t})()}function f(t){return(f=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function p(t,e){return(p=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function h(t,e,r){return(e=d(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function d(t){var e=function(t,e){if("object"!=c(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=c(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==c(e)?e:e+""}function y(){return(y=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var v=function(t){var e=t.yAxisId,r=(0,i.zn)(),c=(0,i.Mw)(),l=(0,i.Ud)(e);return null==l?null:n.createElement(a.O,y({},l,{className:(0,o.Z)("recharts-".concat(l.axisType," ").concat(l.axisType),l.className),viewBox:{x:0,y:0,width:r,height:c},ticksGenerator:function(t){return(0,u.uY)(t,!0)}}))},m=function(t){var e,r;function o(){var t,e;return!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,o),t=o,e=arguments,t=f(t),function(t,e){if(e&&("object"===c(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,s()?Reflect.construct(t,e||[],f(this).constructor):t.apply(this,e))}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&p(t,e)}(o,t),e=[{key:"render",value:function(){return n.createElement(v,this.props)}}],l(o.prototype,e),r&&l(o,r),Object.defineProperty(o,"prototype",{writable:!1}),o}(n.Component);h(m,"displayName","YAxis"),h(m,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1})},37089:function(t,e,r){"use strict";r.d(e,{f:function(){return d}});var n=r(39390),o=r.n(n),i=r(97281),a=r(54768),u=r(3841),c=r(39677);function l(t,e,r){if(e<1)return[];if(1===e&&void 0===r)return t;for(var n=[],o=0;o<t.length;o+=e){if(void 0!==r&&!0!==r(t[o]))return;n.push(t[o])}return n}function s(t,e,r,n,o){if(t*e<t*n||t*e>t*o)return!1;var i=r();return t*(e-t*i/2-n)>=0&&t*(e+t*i/2-o)<=0}function f(t){return(f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function p(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function h(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?p(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=f(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=f(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):p(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function d(t,e,r){var n,f,p,d,y,v=t.tick,m=t.ticks,b=t.viewBox,g=t.minTickGap,x=t.orientation,O=t.interval,w=t.tickFormatter,j=t.unit,S=t.angle;if(!m||!m.length||!v)return[];if((0,i.hj)(O)||u.x.isSsr)return l(m,("number"==typeof O&&(0,i.hj)(O)?O:0)+1);var P="top"===x||"bottom"===x?"width":"height",A=j&&"width"===P?(0,a.xE)(j,{fontSize:e,letterSpacing:r}):{width:0,height:0},E=function(t,n){var i,u,l=o()(w)?w(t.value,n):t.value;return"width"===P?(u={width:(i=(0,a.xE)(l,{fontSize:e,letterSpacing:r})).width+A.width,height:i.height+A.height},(0,c.xE)(u,S)):(0,a.xE)(l,{fontSize:e,letterSpacing:r})[P]},k=m.length>=2?(0,i.uY)(m[1].coordinate-m[0].coordinate):1,M=(n="width"===P,f=b.x,p=b.y,d=b.width,y=b.height,1===k?{start:n?f:p,end:n?f+d:p+y}:{start:n?f+d:p+y,end:n?f:p});return"equidistantPreserveStart"===O?function(t,e,r,n,o){for(var i,a=(n||[]).slice(),u=e.start,c=e.end,f=0,p=1,h=u;p<=a.length;)if(i=function(){var e,i=null==n?void 0:n[f];if(void 0===i)return{v:l(n,p)};var a=f,d=function(){return void 0===e&&(e=r(i,a)),e},y=i.coordinate,v=0===f||s(t,y,d,h,c);v||(f=0,h=u,p+=1),v&&(h=y+t*(d()/2+o),f+=p)}())return i.v;return[]}(k,M,E,m,g):("preserveStart"===O||"preserveStartEnd"===O?function(t,e,r,n,o,i){var a=(n||[]).slice(),u=a.length,c=e.start,l=e.end;if(i){var f=n[u-1],p=r(f,u-1),d=t*(f.coordinate+t*p/2-l);a[u-1]=f=h(h({},f),{},{tickCoord:d>0?f.coordinate-d*t:f.coordinate}),s(t,f.tickCoord,function(){return p},c,l)&&(l=f.tickCoord-t*(p/2+o),a[u-1]=h(h({},f),{},{isShow:!0}))}for(var y=i?u-1:u,v=function(e){var n,i=a[e],u=function(){return void 0===n&&(n=r(i,e)),n};if(0===e){var f=t*(i.coordinate-t*u()/2-c);a[e]=i=h(h({},i),{},{tickCoord:f<0?i.coordinate-f*t:i.coordinate})}else a[e]=i=h(h({},i),{},{tickCoord:i.coordinate});s(t,i.tickCoord,u,c,l)&&(c=i.tickCoord+t*(u()/2+o),a[e]=h(h({},i),{},{isShow:!0}))},m=0;m<y;m++)v(m);return a}(k,M,E,m,g,"preserveStartEnd"===O):function(t,e,r,n,o){for(var i=(n||[]).slice(),a=i.length,u=e.start,c=e.end,l=function(e){var n,l=i[e],f=function(){return void 0===n&&(n=r(l,e)),n};if(e===a-1){var p=t*(l.coordinate+t*f()/2-c);i[e]=l=h(h({},l),{},{tickCoord:p>0?l.coordinate-p*t:l.coordinate})}else i[e]=l=h(h({},l),{},{tickCoord:l.coordinate});s(t,l.tickCoord,f,u,c)&&(c=l.tickCoord-t*(f()/2+o),i[e]=h(h({},l),{},{isShow:!0}))},f=a-1;f>=0;f--)l(f);return i}(k,M,E,m,g)).filter(function(t){return t.isShow})}},38227:function(t,e,r){"use strict";r.d(e,{w:function(){return c}});var n=r(39205),o=r(19223),i=r(24235),a=r(50039),u=r(39677),c=(0,n.z)({chartName:"LineChart",GraphicalChild:o.x,axisComponents:[{axisType:"xAxis",AxisComp:i.K},{axisType:"yAxis",AxisComp:a.B}],formatAxisMap:u.t9})},39205:function(t,e,r){"use strict";r.d(e,{z:function(){return eR}});var n,o,i=r(2265),a=r(66952),u=r.n(a),c=r(39390),l=r.n(c),s=r(98561),f=r.n(s),p=r(1995),h=r.n(p),d=r(64677),y=r.n(d),v=r(91836),m=r.n(v),b=r(90794),g=r(16389),x=r(37434),O=r(88357),w=r(86812),j=r(79857),S=r(14304),P=r(200),A=r(43843),E=r(83896),Text=r(71224),k=r(41586),M=r(97281);function _(t){return(_="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function T(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function C(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?T(Object(r),!0).forEach(function(e){D(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):T(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function D(t,e,r){var n;return(n=function(t,e){if("object"!=_(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=_(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==_(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var I=["Webkit","Moz","O","ms"],N=function(t,e){if(!t)return null;var r=t.replace(/(\w)/,function(t){return t.toUpperCase()}),n=I.reduce(function(t,n){return C(C({},t),{},D({},n+r,e))},{});return n[t]=e,n};function B(t){return(B="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function L(){return(L=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function R(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function z(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?R(Object(r),!0).forEach(function(e){W(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):R(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function U(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,q(n.key),n)}}function $(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return($=function(){return!!t})()}function F(t){return(F=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function Z(t,e){return(Z=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function W(t,e,r){return(e=q(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function q(t){var e=function(t,e){if("object"!=B(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=B(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==B(e)?e:e+""}var Y=function(t){var e=t.data,r=t.startIndex,n=t.endIndex,o=t.x,i=t.width,a=t.travellerWidth;if(!e||!e.length)return{};var u=e.length,c=(0,E.x)().domain(f()(0,u)).range([o,o+i-a]),l=c.domain().map(function(t){return c(t)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:c(r),endX:c(n),scale:c,scaleValues:l}},H=function(t){return t.changedTouches&&!!t.changedTouches.length},X=function(t){var e,r;function n(t){var e,r,o;return!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),r=n,o=[t],r=F(r),W(e=function(t,e){if(e&&("object"===B(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,$()?Reflect.construct(r,o||[],F(this).constructor):r.apply(this,o)),"handleDrag",function(t){e.leaveTimer&&(clearTimeout(e.leaveTimer),e.leaveTimer=null),e.state.isTravellerMoving?e.handleTravellerMove(t):e.state.isSlideMoving&&e.handleSlideDrag(t)}),W(e,"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&e.handleDrag(t.changedTouches[0])}),W(e,"handleDragEnd",function(){e.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var t=e.props,r=t.endIndex,n=t.onDragEnd,o=t.startIndex;null==n||n({endIndex:r,startIndex:o})}),e.detachDragEndListener()}),W(e,"handleLeaveWrapper",function(){(e.state.isTravellerMoving||e.state.isSlideMoving)&&(e.leaveTimer=window.setTimeout(e.handleDragEnd,e.props.leaveTimeOut))}),W(e,"handleEnterSlideOrTraveller",function(){e.setState({isTextActive:!0})}),W(e,"handleLeaveSlideOrTraveller",function(){e.setState({isTextActive:!1})}),W(e,"handleSlideDragStart",function(t){var r=H(t)?t.changedTouches[0]:t;e.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:r.pageX}),e.attachDragEndListener()}),e.travellerDragStartHandlers={startX:e.handleTravellerDragStart.bind(e,"startX"),endX:e.handleTravellerDragStart.bind(e,"endX")},e.state={},e}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Z(t,e)}(n,t),e=[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(t){var e=t.startX,r=t.endX,o=this.state.scaleValues,i=this.props,a=i.gap,u=i.data.length-1,c=n.getIndexInRange(o,Math.min(e,r)),l=n.getIndexInRange(o,Math.max(e,r));return{startIndex:c-c%a,endIndex:l===u?u:l-l%a}}},{key:"getTextOfTick",value:function(t){var e=this.props,r=e.data,n=e.tickFormatter,o=e.dataKey,i=(0,k.F$)(r[t],o,t);return l()(n)?n(i,t):i}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(t){var e=this.state,r=e.slideMoveStartX,n=e.startX,o=e.endX,i=this.props,a=i.x,u=i.width,c=i.travellerWidth,l=i.startIndex,s=i.endIndex,f=i.onChange,p=t.pageX-r;p>0?p=Math.min(p,a+u-c-o,a+u-c-n):p<0&&(p=Math.max(p,a-n,a-o));var h=this.getIndex({startX:n+p,endX:o+p});(h.startIndex!==l||h.endIndex!==s)&&f&&f(h),this.setState({startX:n+p,endX:o+p,slideMoveStartX:t.pageX})}},{key:"handleTravellerDragStart",value:function(t,e){var r=H(e)?e.changedTouches[0]:e;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:t,brushMoveStartX:r.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(t){var e=this.state,r=e.brushMoveStartX,n=e.movingTravellerId,o=e.endX,i=e.startX,a=this.state[n],u=this.props,c=u.x,l=u.width,s=u.travellerWidth,f=u.onChange,p=u.gap,h=u.data,d={startX:this.state.startX,endX:this.state.endX},y=t.pageX-r;y>0?y=Math.min(y,c+l-s-a):y<0&&(y=Math.max(y,c-a)),d[n]=a+y;var v=this.getIndex(d),m=v.startIndex,b=v.endIndex,g=function(){var t=h.length-1;return"startX"===n&&(o>i?m%p==0:b%p==0)||o<i&&b===t||"endX"===n&&(o>i?b%p==0:m%p==0)||o>i&&b===t};this.setState(W(W({},n,a+y),"brushMoveStartX",t.pageX),function(){f&&g()&&f(v)})}},{key:"handleTravellerMoveKeyboard",value:function(t,e){var r=this,n=this.state,o=n.scaleValues,i=n.startX,a=n.endX,u=this.state[e],c=o.indexOf(u);if(-1!==c){var l=c+t;if(-1!==l&&!(l>=o.length)){var s=o[l];"startX"===e&&s>=a||"endX"===e&&s<=i||this.setState(W({},e,s),function(){r.props.onChange(r.getIndex({startX:r.state.startX,endX:r.state.endX}))})}}}},{key:"renderBackground",value:function(){var t=this.props,e=t.x,r=t.y,n=t.width,o=t.height,a=t.fill,u=t.stroke;return i.createElement("rect",{stroke:u,fill:a,x:e,y:r,width:n,height:o})}},{key:"renderPanorama",value:function(){var t=this.props,e=t.x,r=t.y,n=t.width,o=t.height,a=t.data,u=t.children,c=t.padding,l=i.Children.only(u);return l?i.cloneElement(l,{x:e,y:r,width:n,height:o,margin:c,compact:!0,data:a}):null}},{key:"renderTravellerLayer",value:function(t,e){var r,o,a=this,u=this.props,c=u.y,l=u.travellerWidth,s=u.height,f=u.traveller,p=u.ariaLabel,h=u.data,d=u.startIndex,y=u.endIndex,v=Math.max(t,this.props.x),m=z(z({},(0,A.L6)(this.props,!1)),{},{x:v,y:c,width:l,height:s}),b=p||"Min value: ".concat(null===(r=h[d])||void 0===r?void 0:r.name,", Max value: ").concat(null===(o=h[y])||void 0===o?void 0:o.name);return i.createElement(O.m,{tabIndex:0,role:"slider","aria-label":b,"aria-valuenow":t,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[e],onTouchStart:this.travellerDragStartHandlers[e],onKeyDown:function(t){["ArrowLeft","ArrowRight"].includes(t.key)&&(t.preventDefault(),t.stopPropagation(),a.handleTravellerMoveKeyboard("ArrowRight"===t.key?1:-1,e))},onFocus:function(){a.setState({isTravellerFocused:!0})},onBlur:function(){a.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},n.renderTraveller(f,m))}},{key:"renderSlide",value:function(t,e){var r=this.props,n=r.y,o=r.height,a=r.stroke,u=r.travellerWidth;return i.createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:a,fillOpacity:.2,x:Math.min(t,e)+u,y:n,width:Math.max(Math.abs(e-t)-u,0),height:o})}},{key:"renderText",value:function(){var t=this.props,e=t.startIndex,r=t.endIndex,n=t.y,o=t.height,a=t.travellerWidth,u=t.stroke,c=this.state,l=c.startX,s=c.endX,f={pointerEvents:"none",fill:u};return i.createElement(O.m,{className:"recharts-brush-texts"},i.createElement(Text.x,L({textAnchor:"end",verticalAnchor:"middle",x:Math.min(l,s)-5,y:n+o/2},f),this.getTextOfTick(e)),i.createElement(Text.x,L({textAnchor:"start",verticalAnchor:"middle",x:Math.max(l,s)+a+5,y:n+o/2},f),this.getTextOfTick(r)))}},{key:"render",value:function(){var t=this.props,e=t.data,r=t.className,n=t.children,o=t.x,a=t.y,u=t.width,c=t.height,l=t.alwaysShowText,s=this.state,f=s.startX,p=s.endX,h=s.isTextActive,d=s.isSlideMoving,y=s.isTravellerMoving,v=s.isTravellerFocused;if(!e||!e.length||!(0,M.hj)(o)||!(0,M.hj)(a)||!(0,M.hj)(u)||!(0,M.hj)(c)||u<=0||c<=0)return null;var m=(0,b.Z)("recharts-brush",r),g=1===i.Children.count(n),x=N("userSelect","none");return i.createElement(O.m,{className:m,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:x},this.renderBackground(),g&&this.renderPanorama(),this.renderSlide(f,p),this.renderTravellerLayer(f,"startX"),this.renderTravellerLayer(p,"endX"),(h||d||y||v||l)&&this.renderText())}}],r=[{key:"renderDefaultTraveller",value:function(t){var e=t.x,r=t.y,n=t.width,o=t.height,a=t.stroke,u=Math.floor(r+o/2)-1;return i.createElement(i.Fragment,null,i.createElement("rect",{x:e,y:r,width:n,height:o,fill:a,stroke:"none"}),i.createElement("line",{x1:e+1,y1:u,x2:e+n-1,y2:u,fill:"none",stroke:"#fff"}),i.createElement("line",{x1:e+1,y1:u+2,x2:e+n-1,y2:u+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(t,e){return i.isValidElement(t)?i.cloneElement(t,e):l()(t)?t(e):n.renderDefaultTraveller(e)}},{key:"getDerivedStateFromProps",value:function(t,e){var r=t.data,n=t.width,o=t.x,i=t.travellerWidth,a=t.updateId,u=t.startIndex,c=t.endIndex;if(r!==e.prevData||a!==e.prevUpdateId)return z({prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n},r&&r.length?Y({data:r,width:n,x:o,travellerWidth:i,startIndex:u,endIndex:c}):{scale:null,scaleValues:null});if(e.scale&&(n!==e.prevWidth||o!==e.prevX||i!==e.prevTravellerWidth)){e.scale.range([o,o+n-i]);var l=e.scale.domain().map(function(t){return e.scale(t)});return{prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n,startX:e.scale(t.startIndex),endX:e.scale(t.endIndex),scaleValues:l}}return null}},{key:"getIndexInRange",value:function(t,e){for(var r=t.length,n=0,o=r-1;o-n>1;){var i=Math.floor((n+o)/2);t[i]>e?o=i:n=i}return e>=t[o]?o:n}}],e&&U(n.prototype,e),r&&U(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);W(X,"displayName","Brush"),W(X,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var V=r(54768),G=r(9776),K=r(33343),J=function(t,e){var r=t.alwaysShow,n=t.ifOverflow;return r&&(n="extendDomain"),n===e},Q=r(39677),tt=r(47205);function te(){return(te=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function tr(t){return(tr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tn(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function to(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tn(Object(r),!0).forEach(function(e){tl(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tn(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function ti(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,ts(n.key),n)}}function ta(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(ta=function(){return!!t})()}function tu(t){return(tu=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function tc(t,e){return(tc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function tl(t,e,r){return(e=ts(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ts(t){var e=function(t,e){if("object"!=tr(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tr(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tr(e)?e:e+""}var tf=function(t){var e=t.x,r=t.y,n=t.xAxis,o=t.yAxis,i=(0,Q.Ky)({x:n.scale,y:o.scale}),a=i.apply({x:e,y:r},{bandAware:!0});return J(t,"discard")&&!i.isInRange(a)?null:a},tp=function(t){var e,r;function n(){var t,e;return!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),t=n,e=arguments,t=tu(t),function(t,e){if(e&&("object"===tr(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,ta()?Reflect.construct(t,e||[],tu(this).constructor):t.apply(this,e))}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&tc(t,e)}(n,t),e=[{key:"render",value:function(){var t=this.props,e=t.x,r=t.y,o=t.r,a=t.alwaysShow,u=t.clipPathId,c=(0,M.P2)(e),l=(0,M.P2)(r);if((0,tt.Z)(void 0===a,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!c||!l)return null;var s=tf(this.props);if(!s)return null;var f=s.x,p=s.y,h=this.props,d=h.shape,y=h.className,v=to(to({clipPath:J(this.props,"hidden")?"url(#".concat(u,")"):void 0},(0,A.L6)(this.props,!0)),{},{cx:f,cy:p});return i.createElement(O.m,{className:(0,b.Z)("recharts-reference-dot",y)},n.renderDot(d,v),K._.renderCallByParent(this.props,{x:f-o,y:p-o,width:2*o,height:2*o}))}}],ti(n.prototype,e),r&&ti(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.Component);tl(tp,"displayName","ReferenceDot"),tl(tp,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1}),tl(tp,"renderDot",function(t,e){return i.isValidElement(t)?i.cloneElement(t,e):l()(t)?t(e):i.createElement(S.o,te({},e,{cx:e.cx,cy:e.cy,className:"recharts-reference-dot-dot"}))});var th=r(79186),td=r.n(th),ty=r(32794);function tv(t){return(tv="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tm(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,tS(n.key),n)}}function tb(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(tb=function(){return!!t})()}function tg(t){return(tg=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function tx(t,e){return(tx=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function tO(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tw(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tO(Object(r),!0).forEach(function(e){tj(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tO(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tj(t,e,r){return(e=tS(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function tS(t){var e=function(t,e){if("object"!=tv(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tv(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tv(e)?e:e+""}function tP(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function tA(){return(tA=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var tE=function(t,e,r,n,o,i,a,u,c){var l=o.x,s=o.y,f=o.width,p=o.height;if(r){var h=c.y,d=t.y.apply(h,{position:i});if(J(c,"discard")&&!t.y.isInRange(d))return null;var y=[{x:l+f,y:d},{x:l,y:d}];return"left"===u?y.reverse():y}if(e){var v=c.x,m=t.x.apply(v,{position:i});if(J(c,"discard")&&!t.x.isInRange(m))return null;var b=[{x:m,y:s+p},{x:m,y:s}];return"top"===a?b.reverse():b}if(n){var g=c.segment.map(function(e){return t.apply(e,{position:i})});return J(c,"discard")&&td()(g,function(e){return!t.isInRange(e)})?null:g}return null};function tk(t){var e,r,n=t.x,o=t.y,a=t.segment,u=t.xAxisId,c=t.yAxisId,s=t.shape,f=t.className,p=t.alwaysShow,h=(0,ty.sp)(),d=(0,ty.bH)(u),y=(0,ty.Ud)(c),v=(0,ty.d2)();if(!h||!v)return null;(0,tt.Z)(void 0===p,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var m=tE((0,Q.Ky)({x:d.scale,y:y.scale}),(0,M.P2)(n),(0,M.P2)(o),a&&2===a.length,v,t.position,d.orientation,y.orientation,t);if(!m)return null;var g=function(t){if(Array.isArray(t))return t}(m)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(m,2)||function(t,e){if(t){if("string"==typeof t)return tP(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return tP(t,e)}}(m,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),x=g[0],w=x.x,j=x.y,S=g[1],P=S.x,E=S.y,k=tw(tw({clipPath:J(t,"hidden")?"url(#".concat(h,")"):void 0},(0,A.L6)(t,!0)),{},{x1:w,y1:j,x2:P,y2:E});return i.createElement(O.m,{className:(0,b.Z)("recharts-reference-line",f)},(e=s,r=k,i.isValidElement(e)?i.cloneElement(e,r):l()(e)?e(r):i.createElement("line",tA({},r,{className:"recharts-reference-line-line"}))),K._.renderCallByParent(t,(0,Q._b)({x1:w,y1:j,x2:P,y2:E})))}var tM=function(t){var e,r;function n(){var t,e;return!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),t=n,e=arguments,t=tg(t),function(t,e){if(e&&("object"===tv(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,tb()?Reflect.construct(t,e||[],tg(this).constructor):t.apply(this,e))}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&tx(t,e)}(n,t),e=[{key:"render",value:function(){return i.createElement(tk,this.props)}}],tm(n.prototype,e),r&&tm(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.Component);function t_(){return(t_=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function tT(t){return(tT="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tC(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tD(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tC(Object(r),!0).forEach(function(e){tR(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tC(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tI(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,tz(n.key),n)}}function tN(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(tN=function(){return!!t})()}function tB(t){return(tB=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function tL(t,e){return(tL=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function tR(t,e,r){return(e=tz(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function tz(t){var e=function(t,e){if("object"!=tT(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tT(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tT(e)?e:e+""}tj(tM,"displayName","ReferenceLine"),tj(tM,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});var tU=function(t,e,r,n,o){var i=o.x1,a=o.x2,u=o.y1,c=o.y2,l=o.xAxis,s=o.yAxis;if(!l||!s)return null;var f=(0,Q.Ky)({x:l.scale,y:s.scale}),p={x:t?f.x.apply(i,{position:"start"}):f.x.rangeMin,y:r?f.y.apply(u,{position:"start"}):f.y.rangeMin},h={x:e?f.x.apply(a,{position:"end"}):f.x.rangeMax,y:n?f.y.apply(c,{position:"end"}):f.y.rangeMax};return!J(o,"discard")||f.isInRange(p)&&f.isInRange(h)?(0,Q.O1)(p,h):null},t$=function(t){var e,r;function n(){var t,e;return!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),t=n,e=arguments,t=tB(t),function(t,e){if(e&&("object"===tT(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,tN()?Reflect.construct(t,e||[],tB(this).constructor):t.apply(this,e))}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&tL(t,e)}(n,t),e=[{key:"render",value:function(){var t=this.props,e=t.x1,r=t.x2,o=t.y1,a=t.y2,u=t.className,c=t.alwaysShow,l=t.clipPathId;(0,tt.Z)(void 0===c,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var s=(0,M.P2)(e),f=(0,M.P2)(r),p=(0,M.P2)(o),h=(0,M.P2)(a),d=this.props.shape;if(!s&&!f&&!p&&!h&&!d)return null;var y=tU(s,f,p,h,this.props);if(!y&&!d)return null;var v=J(this.props,"hidden")?"url(#".concat(l,")"):void 0;return i.createElement(O.m,{className:(0,b.Z)("recharts-reference-area",u)},n.renderRect(d,tD(tD({clipPath:v},(0,A.L6)(this.props,!0)),y)),K._.renderCallByParent(this.props,y))}}],tI(n.prototype,e),r&&tI(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.Component);function tF(t){return function(t){if(Array.isArray(t))return tZ(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return tZ(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return tZ(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function tZ(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}tR(t$,"displayName","ReferenceArea"),tR(t$,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1}),tR(t$,"renderRect",function(t,e){return i.isValidElement(t)?i.cloneElement(t,e):l()(t)?t(e):i.createElement(P.A,t_({},e,{className:"recharts-reference-area-rect"}))});var tW=function(t,e,r,n,o){var i=(0,A.NN)(t,tM),a=(0,A.NN)(t,tp),u=[].concat(tF(i),tF(a)),c=(0,A.NN)(t,t$),l="".concat(n,"Id"),s=n[0],f=e;if(u.length&&(f=u.reduce(function(t,e){if(e.props[l]===r&&J(e.props,"extendDomain")&&(0,M.hj)(e.props[s])){var n=e.props[s];return[Math.min(t[0],n),Math.max(t[1],n)]}return t},f)),c.length){var p="".concat(s,"1"),h="".concat(s,"2");f=c.reduce(function(t,e){if(e.props[l]===r&&J(e.props,"extendDomain")&&(0,M.hj)(e.props[p])&&(0,M.hj)(e.props[h])){var n=e.props[p],o=e.props[h];return[Math.min(t[0],n,o),Math.max(t[1],n,o)]}return t},f)}return o&&o.length&&(f=o.reduce(function(t,e){return(0,M.hj)(e)?[Math.min(t[0],e),Math.max(t[1],e)]:t},f)),f},tq=r(56120),tY=r(1374),tH=r(12771),tX=new(r.n(tH)()),tV="recharts.syncMouseEvents",tG=r(12655);function tK(t){return(tK="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tJ(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,t0(n.key),n)}}function tQ(t,e,r){return(e=t0(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function t0(t){var e=function(t,e){if("object"!=tK(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tK(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tK(e)?e:e+""}var t1=(tJ((n=function t(){(function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")})(this,t),tQ(this,"activeIndex",0),tQ(this,"coordinateList",[]),tQ(this,"layout","horizontal")}).prototype,[{key:"setDetails",value:function(t){var e,r=t.coordinateList,n=void 0===r?null:r,o=t.container,i=void 0===o?null:o,a=t.layout,u=void 0===a?null:a,c=t.offset,l=void 0===c?null:c,s=t.mouseHandlerCallback,f=void 0===s?null:s;this.coordinateList=null!==(e=null!=n?n:this.coordinateList)&&void 0!==e?e:[],this.container=null!=i?i:this.container,this.layout=null!=u?u:this.layout,this.offset=null!=l?l:this.offset,this.mouseHandlerCallback=null!=f?f:this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(t){if(0!==this.coordinateList.length)switch(t.key){case"ArrowRight":if("horizontal"!==this.layout)return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break;case"ArrowLeft":if("horizontal"!==this.layout)return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse()}}},{key:"setIndex",value:function(t){this.activeIndex=t}},{key:"spoofMouse",value:function(){if("horizontal"===this.layout&&0!==this.coordinateList.length){var t,e,r=this.container.getBoundingClientRect(),n=r.x,o=r.y,i=r.height,a=this.coordinateList[this.activeIndex].coordinate,u=(null===(t=window)||void 0===t?void 0:t.scrollX)||0,c=(null===(e=window)||void 0===e?void 0:e.scrollY)||0,l=o+this.offset.top+i/2+c;this.mouseHandlerCallback({pageX:n+a+u,pageY:l})}}}]),o&&tJ(n,o),Object.defineProperty(n,"prototype",{writable:!1}),n),t2=r(77688),t3=r(11697);function t6(t){return(t6="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var t4=["x","y","top","left","width","height","className"];function t5(){return(t5=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function t7(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}var t8=function(t){var e=t.x,r=void 0===e?0:e,n=t.y,o=void 0===n?0:n,a=t.top,u=void 0===a?0:a,c=t.left,l=void 0===c?0:c,s=t.width,f=void 0===s?0:s,p=t.height,h=void 0===p?0:p,d=t.className,y=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?t7(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=t6(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=t6(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==t6(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):t7(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({x:r,y:o,top:u,left:l,width:f,height:h},function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,t4));return(0,M.hj)(r)&&(0,M.hj)(o)&&(0,M.hj)(f)&&(0,M.hj)(h)&&(0,M.hj)(u)&&(0,M.hj)(l)?i.createElement("path",t5({},(0,A.L6)(y,!0),{className:(0,b.Z)("recharts-cross",d),d:"M".concat(r,",").concat(u,"v").concat(h,"M").concat(l,",").concat(o,"h").concat(f)})):null};function t9(t){var e=t.cx,r=t.cy,n=t.radius,o=t.startAngle,i=t.endAngle;return{points:[(0,tq.op)(e,r,n,o),(0,tq.op)(e,r,n,i)],cx:e,cy:r,radius:n,startAngle:o,endAngle:i}}var et=r(77795);function ee(t){return(ee="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function er(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function en(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?er(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=ee(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=ee(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==ee(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):er(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function eo(t){var e,r,n,o,a=t.element,u=t.tooltipEventType,c=t.isActive,l=t.activeCoordinate,s=t.activePayload,f=t.offset,p=t.activeTooltipIndex,h=t.tooltipAxisBandSize,d=t.layout,y=t.chartName,v=null!==(r=a.props.cursor)&&void 0!==r?r:null===(n=a.type.defaultProps)||void 0===n?void 0:n.cursor;if(!a||!v||!c||!l||"ScatterChart"!==y&&"axis"!==u)return null;var m=t3.H;if("ScatterChart"===y)o=l,m=t8;else if("BarChart"===y)e=h/2,o={stroke:"none",fill:"#ccc",x:"horizontal"===d?l.x-e:f.left+.5,y:"horizontal"===d?f.top+.5:l.y-e,width:"horizontal"===d?h:f.width-1,height:"horizontal"===d?f.height-1:h},m=P.A;else if("radial"===d){var g=t9(l),x=g.cx,O=g.cy,w=g.radius;o={cx:x,cy:O,startAngle:g.startAngle,endAngle:g.endAngle,innerRadius:w,outerRadius:w},m=et.L}else o={points:function(t,e,r){var n,o,i,a;if("horizontal"===t)i=n=e.x,o=r.top,a=r.top+r.height;else if("vertical"===t)a=o=e.y,n=r.left,i=r.left+r.width;else if(null!=e.cx&&null!=e.cy){if("centric"!==t)return t9(e);var u=e.cx,c=e.cy,l=e.innerRadius,s=e.outerRadius,f=e.angle,p=(0,tq.op)(u,c,l,f),h=(0,tq.op)(u,c,s,f);n=p.x,o=p.y,i=h.x,a=h.y}return[{x:n,y:o},{x:i,y:a}]}(d,l,f)},m=t3.H;var j=en(en(en(en({stroke:"#ccc",pointerEvents:"none"},f),o),(0,A.L6)(v,!1)),{},{payload:s,payloadIndex:p,className:(0,b.Z)("recharts-tooltip-cursor",v.className)});return(0,i.isValidElement)(v)?(0,i.cloneElement)(v,j):(0,i.createElement)(m,j)}var ei=["item"],ea=["children","className","width","height","style","compact","title","desc"];function eu(t){return(eu="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ec(){return(ec=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function el(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(t,e)||ev(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function es(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function ef(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,eO(n.key),n)}}function ep(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(ep=function(){return!!t})()}function eh(t){return(eh=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function ed(t,e){return(ed=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function ey(t){return function(t){if(Array.isArray(t))return em(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||ev(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ev(t,e){if(t){if("string"==typeof t)return em(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return em(t,e)}}function em(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function eb(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function eg(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?eb(Object(r),!0).forEach(function(e){ex(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):eb(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function ex(t,e,r){return(e=eO(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function eO(t){var e=function(t,e){if("object"!=eu(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=eu(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==eu(e)?e:e+""}var ew={xAxis:["bottom","top"],yAxis:["left","right"]},ej={width:"100%",height:"100%"},eS={x:0,y:0};function eP(t){return t}var eA=function(t,e,r,n){var o=e.find(function(t){return t&&t.index===r});if(o){if("horizontal"===t)return{x:o.coordinate,y:n.y};if("vertical"===t)return{x:n.x,y:o.coordinate};if("centric"===t){var i=o.coordinate,a=n.radius;return eg(eg(eg({},n),(0,tq.op)(n.cx,n.cy,a,i)),{},{angle:i,radius:a})}var u=o.coordinate,c=n.angle;return eg(eg(eg({},n),(0,tq.op)(n.cx,n.cy,u,c)),{},{angle:c,radius:u})}return eS},eE=function(t,e){var r=e.graphicalItems,n=e.dataStartIndex,o=e.dataEndIndex,i=(null!=r?r:[]).reduce(function(t,e){var r=e.props.data;return r&&r.length?[].concat(ey(t),ey(r)):t},[]);return i.length>0?i:t&&t.length&&(0,M.hj)(n)&&(0,M.hj)(o)?t.slice(n,o+1):[]};function ek(t){return"number"===t?[0,"auto"]:void 0}var eM=function(t,e,r,n){var o=t.graphicalItems,i=t.tooltipAxis,a=eE(e,t);return r<0||!o||!o.length||r>=a.length?null:o.reduce(function(o,u){var c,l,s=null!==(c=u.props.data)&&void 0!==c?c:e;if(s&&t.dataStartIndex+t.dataEndIndex!==0&&t.dataEndIndex-t.dataStartIndex>=r&&(s=s.slice(t.dataStartIndex,t.dataEndIndex+1)),i.dataKey&&!i.allowDuplicatedCategory){var f=void 0===s?a:s;l=(0,M.Ap)(f,i.dataKey,n)}else l=s&&s[r]||a[r];return l?[].concat(ey(o),[(0,k.Qo)(u,l)]):o},[])},e_=function(t,e,r,n){var o=n||{x:t.chartX,y:t.chartY},i="horizontal"===r?o.x:"vertical"===r?o.y:"centric"===r?o.angle:o.radius,a=t.orderedTooltipTicks,u=t.tooltipAxis,c=t.tooltipTicks,l=(0,k.VO)(i,a,c,u);if(l>=0&&c){var s=c[l]&&c[l].value,f=eM(t,e,l,s),p=eA(r,a,l,o);return{activeTooltipIndex:l,activeLabel:s,activePayload:f,activeCoordinate:p}}return null},eT=function(t,e){var r=e.axes,n=e.graphicalItems,o=e.axisType,i=e.axisIdKey,a=e.stackGroups,c=e.dataStartIndex,l=e.dataEndIndex,s=t.layout,p=t.children,h=t.stackOffset,d=(0,k.NA)(s,o);return r.reduce(function(e,r){var y=void 0!==r.type.defaultProps?eg(eg({},r.type.defaultProps),r.props):r.props,v=y.type,m=y.dataKey,b=y.allowDataOverflow,g=y.allowDuplicatedCategory,x=y.scale,O=y.ticks,w=y.includeHidden,j=y[i];if(e[j])return e;var S=eE(t.data,{graphicalItems:n.filter(function(t){var e;return(i in t.props?t.props[i]:null===(e=t.type.defaultProps)||void 0===e?void 0:e[i])===j}),dataStartIndex:c,dataEndIndex:l}),P=S.length;(function(t,e,r){if("number"===r&&!0===e&&Array.isArray(t)){var n=null==t?void 0:t[0],o=null==t?void 0:t[1];if(n&&o&&(0,M.hj)(n)&&(0,M.hj)(o))return!0}return!1})(y.domain,b,v)&&(_=(0,k.LG)(y.domain,null,b),d&&("number"===v||"auto"!==x)&&(C=(0,k.gF)(S,m,"category")));var A=ek(v);if(!_||0===_.length){var E,_,T,C,D,I=null!==(D=y.domain)&&void 0!==D?D:A;if(m){if(_=(0,k.gF)(S,m,v),"category"===v&&d){var N=(0,M.bv)(_);g&&N?(T=_,_=f()(0,P)):g||(_=(0,k.ko)(I,_,r).reduce(function(t,e){return t.indexOf(e)>=0?t:[].concat(ey(t),[e])},[]))}else if("category"===v)_=g?_.filter(function(t){return""!==t&&!u()(t)}):(0,k.ko)(I,_,r).reduce(function(t,e){return t.indexOf(e)>=0||""===e||u()(e)?t:[].concat(ey(t),[e])},[]);else if("number"===v){var B=(0,k.ZI)(S,n.filter(function(t){var e,r,n=i in t.props?t.props[i]:null===(e=t.type.defaultProps)||void 0===e?void 0:e[i],o="hide"in t.props?t.props.hide:null===(r=t.type.defaultProps)||void 0===r?void 0:r.hide;return n===j&&(w||!o)}),m,o,s);B&&(_=B)}d&&("number"===v||"auto"!==x)&&(C=(0,k.gF)(S,m,"category"))}else _=d?f()(0,P):a&&a[j]&&a[j].hasStack&&"number"===v?"expand"===h?[0,1]:(0,k.EB)(a[j].stackGroups,c,l):(0,k.s6)(S,n.filter(function(t){var e=i in t.props?t.props[i]:t.type.defaultProps[i],r="hide"in t.props?t.props.hide:t.type.defaultProps.hide;return e===j&&(w||!r)}),v,s,!0);"number"===v?(_=tW(p,_,j,o,O),I&&(_=(0,k.LG)(I,_,b))):"category"===v&&I&&_.every(function(t){return I.indexOf(t)>=0})&&(_=I)}return eg(eg({},e),{},ex({},j,eg(eg({},y),{},{axisType:o,domain:_,categoricalDomain:C,duplicateDomain:T,originalDomain:null!==(E=y.domain)&&void 0!==E?E:A,isCategorical:d,layout:s})))},{})},eC=function(t,e){var r=e.graphicalItems,n=e.Axis,o=e.axisType,i=e.axisIdKey,a=e.stackGroups,u=e.dataStartIndex,c=e.dataEndIndex,l=t.layout,s=t.children,p=eE(t.data,{graphicalItems:r,dataStartIndex:u,dataEndIndex:c}),d=p.length,y=(0,k.NA)(l,o),v=-1;return r.reduce(function(t,e){var m,b=(void 0!==e.type.defaultProps?eg(eg({},e.type.defaultProps),e.props):e.props)[i],g=ek("number");return t[b]?t:(v++,m=y?f()(0,d):a&&a[b]&&a[b].hasStack?tW(s,m=(0,k.EB)(a[b].stackGroups,u,c),b,o):tW(s,m=(0,k.LG)(g,(0,k.s6)(p,r.filter(function(t){var e,r,n=i in t.props?t.props[i]:null===(e=t.type.defaultProps)||void 0===e?void 0:e[i],o="hide"in t.props?t.props.hide:null===(r=t.type.defaultProps)||void 0===r?void 0:r.hide;return n===b&&!o}),"number",l),n.defaultProps.allowDataOverflow),b,o),eg(eg({},t),{},ex({},b,eg(eg({axisType:o},n.defaultProps),{},{hide:!0,orientation:h()(ew,"".concat(o,".").concat(v%2),null),domain:m,originalDomain:g,isCategorical:y,layout:l}))))},{})},eD=function(t,e){var r=e.axisType,n=void 0===r?"xAxis":r,o=e.AxisComp,i=e.graphicalItems,a=e.stackGroups,u=e.dataStartIndex,c=e.dataEndIndex,l=t.children,s="".concat(n,"Id"),f=(0,A.NN)(l,o),p={};return f&&f.length?p=eT(t,{axes:f,graphicalItems:i,axisType:n,axisIdKey:s,stackGroups:a,dataStartIndex:u,dataEndIndex:c}):i&&i.length&&(p=eC(t,{Axis:o,graphicalItems:i,axisType:n,axisIdKey:s,stackGroups:a,dataStartIndex:u,dataEndIndex:c})),p},eI=function(t){var e=(0,M.Kt)(t),r=(0,k.uY)(e,!1,!0);return{tooltipTicks:r,orderedTooltipTicks:y()(r,function(t){return t.coordinate}),tooltipAxis:e,tooltipAxisBandSize:(0,k.zT)(e,r)}},eN=function(t){var e=t.children,r=t.defaultShowTooltip,n=(0,A.sP)(e,X),o=0,i=0;return t.data&&0!==t.data.length&&(i=t.data.length-1),n&&n.props&&(n.props.startIndex>=0&&(o=n.props.startIndex),n.props.endIndex>=0&&(i=n.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:o,dataEndIndex:i,activeTooltipIndex:-1,isTooltipActive:!!r}},eB=function(t){return"horizontal"===t?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:"vertical"===t?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:"centric"===t?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},eL=function(t,e){var r=t.props,n=t.graphicalItems,o=t.xAxisMap,i=void 0===o?{}:o,a=t.yAxisMap,u=void 0===a?{}:a,c=r.width,l=r.height,s=r.children,f=r.margin||{},p=(0,A.sP)(s,X),d=(0,A.sP)(s,j.D),y=Object.keys(u).reduce(function(t,e){var r=u[e],n=r.orientation;return r.mirror||r.hide?t:eg(eg({},t),{},ex({},n,t[n]+r.width))},{left:f.left||0,right:f.right||0}),v=Object.keys(i).reduce(function(t,e){var r=i[e],n=r.orientation;return r.mirror||r.hide?t:eg(eg({},t),{},ex({},n,h()(t,"".concat(n))+r.height))},{top:f.top||0,bottom:f.bottom||0}),m=eg(eg({},v),y),b=m.bottom;p&&(m.bottom+=p.props.height||X.defaultProps.height),d&&e&&(m=(0,k.By)(m,n,r,e));var g=c-m.left-m.right,x=l-m.top-m.bottom;return eg(eg({brushBottom:b},m),{},{width:Math.max(g,0),height:Math.max(x,0)})},eR=function(t){var e=t.chartName,r=t.GraphicalChild,n=t.defaultTooltipEventType,o=void 0===n?"axis":n,a=t.validateTooltipEventTypes,c=void 0===a?["axis"]:a,s=t.axisComponents,f=t.legendContent,p=t.formatAxisMap,d=t.defaultProps,y=function(t,e){var r=e.graphicalItems,n=e.stackGroups,o=e.offset,i=e.updateId,a=e.dataStartIndex,c=e.dataEndIndex,l=t.barSize,f=t.layout,p=t.barGap,h=t.barCategoryGap,d=t.maxBarSize,y=eB(f),v=y.numericAxisName,m=y.cateAxisName,b=!!r&&!!r.length&&r.some(function(t){var e=(0,A.Gf)(t&&t.type);return e&&e.indexOf("Bar")>=0}),x=[];return r.forEach(function(r,y){var O=eE(t.data,{graphicalItems:[r],dataStartIndex:a,dataEndIndex:c}),w=void 0!==r.type.defaultProps?eg(eg({},r.type.defaultProps),r.props):r.props,j=w.dataKey,S=w.maxBarSize,P=w["".concat(v,"Id")],E=w["".concat(m,"Id")],M=s.reduce(function(t,r){var n=e["".concat(r.axisType,"Map")],o=w["".concat(r.axisType,"Id")];n&&n[o]||"zAxis"===r.axisType||(0,g.Z)(!1);var i=n[o];return eg(eg({},t),{},ex(ex({},r.axisType,i),"".concat(r.axisType,"Ticks"),(0,k.uY)(i)))},{}),_=M[m],T=M["".concat(m,"Ticks")],C=n&&n[P]&&n[P].hasStack&&(0,k.O3)(r,n[P].stackGroups),D=(0,A.Gf)(r.type).indexOf("Bar")>=0,I=(0,k.zT)(_,T),N=[],B=b&&(0,k.pt)({barSize:l,stackGroups:n,totalSize:"xAxis"===m?M[m].width:"yAxis"===m?M[m].height:void 0});if(D){var L,R,z=u()(S)?d:S,U=null!==(L=null!==(R=(0,k.zT)(_,T,!0))&&void 0!==R?R:z)&&void 0!==L?L:0;N=(0,k.qz)({barGap:p,barCategoryGap:h,bandSize:U!==I?U:I,sizeList:B[E],maxBarSize:z}),U!==I&&(N=N.map(function(t){return eg(eg({},t),{},{position:eg(eg({},t.position),{},{offset:t.position.offset-U/2})})}))}var $=r&&r.type&&r.type.getComposedData;$&&x.push({props:eg(eg({},$(eg(eg({},M),{},{displayedData:O,props:t,dataKey:j,item:r,bandSize:I,barPosition:N,offset:o,stackedData:C,layout:f,dataStartIndex:a,dataEndIndex:c}))),{},ex(ex(ex({key:r.key||"item-".concat(y)},v,M[v]),m,M[m]),"animationId",i)),childIndex:(0,A.$R)(r,t.children),item:r})}),x},v=function(t,n){var o=t.props,i=t.dataStartIndex,a=t.dataEndIndex,u=t.updateId;if(!(0,A.TT)({props:o}))return null;var c=o.children,l=o.layout,f=o.stackOffset,h=o.data,d=o.reverseStackOrder,v=eB(l),m=v.numericAxisName,b=v.cateAxisName,g=(0,A.NN)(c,r),x=(0,k.wh)(h,g,"".concat(m,"Id"),"".concat(b,"Id"),f,d),O=s.reduce(function(t,e){var r="".concat(e.axisType,"Map");return eg(eg({},t),{},ex({},r,eD(o,eg(eg({},e),{},{graphicalItems:g,stackGroups:e.axisType===m&&x,dataStartIndex:i,dataEndIndex:a}))))},{}),w=eL(eg(eg({},O),{},{props:o,graphicalItems:g}),null==n?void 0:n.legendBBox);Object.keys(O).forEach(function(t){O[t]=p(o,O[t],w,t.replace("Map",""),e)});var j=eI(O["".concat(b,"Map")]),S=y(o,eg(eg({},O),{},{dataStartIndex:i,dataEndIndex:a,updateId:u,graphicalItems:g,stackGroups:x,offset:w}));return eg(eg({formattedGraphicalItems:S,graphicalItems:g,offset:w,stackGroups:x},j),O)},j=function(t){var r,n;function a(t){var r,n,o,c,s;return!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,a),c=a,s=[t],c=eh(c),ex(o=function(t,e){if(e&&("object"===eu(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,ep()?Reflect.construct(c,s||[],eh(this).constructor):c.apply(this,s)),"eventEmitterSymbol",Symbol("rechartsEventEmitter")),ex(o,"accessibilityManager",new t1),ex(o,"handleLegendBBoxUpdate",function(t){if(t){var e=o.state,r=e.dataStartIndex,n=e.dataEndIndex,i=e.updateId;o.setState(eg({legendBBox:t},v({props:o.props,dataStartIndex:r,dataEndIndex:n,updateId:i},eg(eg({},o.state),{},{legendBBox:t}))))}}),ex(o,"handleReceiveSyncEvent",function(t,e,r){o.props.syncId===t&&(r!==o.eventEmitterSymbol||"function"==typeof o.props.syncMethod)&&o.applySyncEvent(e)}),ex(o,"handleBrushChange",function(t){var e=t.startIndex,r=t.endIndex;if(e!==o.state.dataStartIndex||r!==o.state.dataEndIndex){var n=o.state.updateId;o.setState(function(){return eg({dataStartIndex:e,dataEndIndex:r},v({props:o.props,dataStartIndex:e,dataEndIndex:r,updateId:n},o.state))}),o.triggerSyncEvent({dataStartIndex:e,dataEndIndex:r})}}),ex(o,"handleMouseEnter",function(t){var e=o.getMouseInfo(t);if(e){var r=eg(eg({},e),{},{isTooltipActive:!0});o.setState(r),o.triggerSyncEvent(r);var n=o.props.onMouseEnter;l()(n)&&n(r,t)}}),ex(o,"triggeredAfterMouseMove",function(t){var e=o.getMouseInfo(t),r=e?eg(eg({},e),{},{isTooltipActive:!0}):{isTooltipActive:!1};o.setState(r),o.triggerSyncEvent(r);var n=o.props.onMouseMove;l()(n)&&n(r,t)}),ex(o,"handleItemMouseEnter",function(t){o.setState(function(){return{isTooltipActive:!0,activeItem:t,activePayload:t.tooltipPayload,activeCoordinate:t.tooltipPosition||{x:t.cx,y:t.cy}}})}),ex(o,"handleItemMouseLeave",function(){o.setState(function(){return{isTooltipActive:!1}})}),ex(o,"handleMouseMove",function(t){t.persist(),o.throttleTriggeredAfterMouseMove(t)}),ex(o,"handleMouseLeave",function(t){o.throttleTriggeredAfterMouseMove.cancel();var e={isTooltipActive:!1};o.setState(e),o.triggerSyncEvent(e);var r=o.props.onMouseLeave;l()(r)&&r(e,t)}),ex(o,"handleOuterEvent",function(t){var e,r=(0,A.Bh)(t),n=h()(o.props,"".concat(r));r&&l()(n)&&n(null!==(e=/.*touch.*/i.test(r)?o.getMouseInfo(t.changedTouches[0]):o.getMouseInfo(t))&&void 0!==e?e:{},t)}),ex(o,"handleClick",function(t){var e=o.getMouseInfo(t);if(e){var r=eg(eg({},e),{},{isTooltipActive:!0});o.setState(r),o.triggerSyncEvent(r);var n=o.props.onClick;l()(n)&&n(r,t)}}),ex(o,"handleMouseDown",function(t){var e=o.props.onMouseDown;l()(e)&&e(o.getMouseInfo(t),t)}),ex(o,"handleMouseUp",function(t){var e=o.props.onMouseUp;l()(e)&&e(o.getMouseInfo(t),t)}),ex(o,"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&o.throttleTriggeredAfterMouseMove(t.changedTouches[0])}),ex(o,"handleTouchStart",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&o.handleMouseDown(t.changedTouches[0])}),ex(o,"handleTouchEnd",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&o.handleMouseUp(t.changedTouches[0])}),ex(o,"handleDoubleClick",function(t){var e=o.props.onDoubleClick;l()(e)&&e(o.getMouseInfo(t),t)}),ex(o,"handleContextMenu",function(t){var e=o.props.onContextMenu;l()(e)&&e(o.getMouseInfo(t),t)}),ex(o,"triggerSyncEvent",function(t){void 0!==o.props.syncId&&tX.emit(tV,o.props.syncId,t,o.eventEmitterSymbol)}),ex(o,"applySyncEvent",function(t){var e=o.props,r=e.layout,n=e.syncMethod,i=o.state.updateId,a=t.dataStartIndex,u=t.dataEndIndex;if(void 0!==t.dataStartIndex||void 0!==t.dataEndIndex)o.setState(eg({dataStartIndex:a,dataEndIndex:u},v({props:o.props,dataStartIndex:a,dataEndIndex:u,updateId:i},o.state)));else if(void 0!==t.activeTooltipIndex){var c=t.chartX,l=t.chartY,s=t.activeTooltipIndex,f=o.state,p=f.offset,h=f.tooltipTicks;if(!p)return;if("function"==typeof n)s=n(h,t);else if("value"===n){s=-1;for(var d=0;d<h.length;d++)if(h[d].value===t.activeLabel){s=d;break}}var y=eg(eg({},p),{},{x:p.left,y:p.top}),m=Math.min(c,y.x+y.width),b=Math.min(l,y.y+y.height),g=h[s]&&h[s].value,x=eM(o.state,o.props.data,s),O=h[s]?{x:"horizontal"===r?h[s].coordinate:m,y:"horizontal"===r?b:h[s].coordinate}:eS;o.setState(eg(eg({},t),{},{activeLabel:g,activeCoordinate:O,activePayload:x,activeTooltipIndex:s}))}else o.setState(t)}),ex(o,"renderCursor",function(t){var r,n=o.state,a=n.isTooltipActive,u=n.activeCoordinate,c=n.activePayload,l=n.offset,s=n.activeTooltipIndex,f=n.tooltipAxisBandSize,p=o.getTooltipEventType(),h=null!==(r=t.props.active)&&void 0!==r?r:a,d=o.props.layout,y=t.key||"_recharts-cursor";return i.createElement(eo,{key:y,activeCoordinate:u,activePayload:c,activeTooltipIndex:s,chartName:e,element:t,isActive:h,layout:d,offset:l,tooltipAxisBandSize:f,tooltipEventType:p})}),ex(o,"renderPolarAxis",function(t,e,r){var n=h()(t,"type.axisType"),a=h()(o.state,"".concat(n,"Map")),u=t.type.defaultProps,c=void 0!==u?eg(eg({},u),t.props):t.props,l=a&&a[c["".concat(n,"Id")]];return(0,i.cloneElement)(t,eg(eg({},l),{},{className:(0,b.Z)(n,l.className),key:t.key||"".concat(e,"-").concat(r),ticks:(0,k.uY)(l,!0)}))}),ex(o,"renderPolarGrid",function(t){var e=t.props,r=e.radialLines,n=e.polarAngles,a=e.polarRadius,u=o.state,c=u.radiusAxisMap,l=u.angleAxisMap,s=(0,M.Kt)(c),f=(0,M.Kt)(l),p=f.cx,h=f.cy,d=f.innerRadius,y=f.outerRadius;return(0,i.cloneElement)(t,{polarAngles:Array.isArray(n)?n:(0,k.uY)(f,!0).map(function(t){return t.coordinate}),polarRadius:Array.isArray(a)?a:(0,k.uY)(s,!0).map(function(t){return t.coordinate}),cx:p,cy:h,innerRadius:d,outerRadius:y,key:t.key||"polar-grid",radialLines:r})}),ex(o,"renderLegend",function(){var t=o.state.formattedGraphicalItems,e=o.props,r=e.children,n=e.width,a=e.height,u=o.props.margin||{},c=n-(u.left||0)-(u.right||0),l=(0,G.z)({children:r,formattedGraphicalItems:t,legendWidth:c,legendContent:f});if(!l)return null;var s=l.item,p=es(l,ei);return(0,i.cloneElement)(s,eg(eg({},p),{},{chartWidth:n,chartHeight:a,margin:u,onBBoxUpdate:o.handleLegendBBoxUpdate}))}),ex(o,"renderTooltip",function(){var t,e=o.props,r=e.children,n=e.accessibilityLayer,a=(0,A.sP)(r,w.u);if(!a)return null;var u=o.state,c=u.isTooltipActive,l=u.activeCoordinate,s=u.activePayload,f=u.activeLabel,p=u.offset,h=null!==(t=a.props.active)&&void 0!==t?t:c;return(0,i.cloneElement)(a,{viewBox:eg(eg({},p),{},{x:p.left,y:p.top}),active:h,label:f,payload:h?s:[],coordinate:l,accessibilityLayer:n})}),ex(o,"renderBrush",function(t){var e=o.props,r=e.margin,n=e.data,a=o.state,u=a.offset,c=a.dataStartIndex,l=a.dataEndIndex,s=a.updateId;return(0,i.cloneElement)(t,{key:t.key||"_recharts-brush",onChange:(0,k.DO)(o.handleBrushChange,t.props.onChange),data:n,x:(0,M.hj)(t.props.x)?t.props.x:u.left,y:(0,M.hj)(t.props.y)?t.props.y:u.top+u.height+u.brushBottom-(r.bottom||0),width:(0,M.hj)(t.props.width)?t.props.width:u.width,startIndex:c,endIndex:l,updateId:"brush-".concat(s)})}),ex(o,"renderReferenceElement",function(t,e,r){if(!t)return null;var n=o.clipPathId,a=o.state,u=a.xAxisMap,c=a.yAxisMap,l=a.offset,s=t.type.defaultProps||{},f=t.props,p=f.xAxisId,h=void 0===p?s.xAxisId:p,d=f.yAxisId,y=void 0===d?s.yAxisId:d;return(0,i.cloneElement)(t,{key:t.key||"".concat(e,"-").concat(r),xAxis:u[h],yAxis:c[y],viewBox:{x:l.left,y:l.top,width:l.width,height:l.height},clipPathId:n})}),ex(o,"renderActivePoints",function(t){var e=t.item,r=t.activePoint,n=t.basePoint,o=t.childIndex,i=t.isRange,u=[],c=e.props.key,l=void 0!==e.item.type.defaultProps?eg(eg({},e.item.type.defaultProps),e.item.props):e.item.props,s=l.activeDot,f=eg(eg({index:o,dataKey:l.dataKey,cx:r.x,cy:r.y,r:4,fill:(0,k.fk)(e.item),strokeWidth:2,stroke:"#fff",payload:r.payload,value:r.value},(0,A.L6)(s,!1)),(0,tG.Ym)(s));return u.push(a.renderActiveDot(s,f,"".concat(c,"-activePoint-").concat(o))),n?u.push(a.renderActiveDot(s,eg(eg({},f),{},{cx:n.x,cy:n.y}),"".concat(c,"-basePoint-").concat(o))):i&&u.push(null),u}),ex(o,"renderGraphicChild",function(t,e,r){var n=o.filterFormatItem(t,e,r);if(!n)return null;var a=o.getTooltipEventType(),c=o.state,l=c.isTooltipActive,s=c.tooltipAxis,f=c.activeTooltipIndex,p=c.activeLabel,h=o.props.children,d=(0,A.sP)(h,w.u),y=n.props,v=y.points,m=y.isRange,b=y.baseLine,g=void 0!==n.item.type.defaultProps?eg(eg({},n.item.type.defaultProps),n.item.props):n.item.props,x=g.activeDot,O=g.hide,j=g.activeBar,S=g.activeShape,P={};"axis"!==a&&d&&"click"===d.props.trigger?P={onClick:(0,k.DO)(o.handleItemMouseEnter,t.props.onClick)}:"axis"!==a&&(P={onMouseLeave:(0,k.DO)(o.handleItemMouseLeave,t.props.onMouseLeave),onMouseEnter:(0,k.DO)(o.handleItemMouseEnter,t.props.onMouseEnter)});var E=(0,i.cloneElement)(t,eg(eg({},n.props),P));if(!O&&l&&d&&(x||j||S)){if(f>=0){if(s.dataKey&&!s.allowDuplicatedCategory){var _="function"==typeof s.dataKey?function(t){return"function"==typeof s.dataKey?s.dataKey(t.payload):null}:"payload.".concat(s.dataKey.toString());C=(0,M.Ap)(v,_,p),D=m&&b&&(0,M.Ap)(b,_,p)}else C=null==v?void 0:v[f],D=m&&b&&b[f];if(S||j){var T=void 0!==t.props.activeIndex?t.props.activeIndex:f;return[(0,i.cloneElement)(t,eg(eg(eg({},n.props),P),{},{activeIndex:T})),null,null]}if(!u()(C))return[E].concat(ey(o.renderActivePoints({item:n,activePoint:C,basePoint:D,childIndex:f,isRange:m})))}else{var C,D,I,N=(null!==(I=o.getItemByXY(o.state.activeCoordinate))&&void 0!==I?I:{graphicalItem:E}).graphicalItem,B=N.item,L=void 0===B?t:B,R=N.childIndex,z=eg(eg(eg({},n.props),P),{},{activeIndex:R});return[(0,i.cloneElement)(L,z),null,null]}}return m?[E,null,null]:[E,null]}),ex(o,"renderCustomized",function(t,e,r){return(0,i.cloneElement)(t,eg(eg({key:"recharts-customized-".concat(r)},o.props),o.state))}),ex(o,"renderMap",{CartesianGrid:{handler:eP,once:!0},ReferenceArea:{handler:o.renderReferenceElement},ReferenceLine:{handler:eP},ReferenceDot:{handler:o.renderReferenceElement},XAxis:{handler:eP},YAxis:{handler:eP},Brush:{handler:o.renderBrush,once:!0},Bar:{handler:o.renderGraphicChild},Line:{handler:o.renderGraphicChild},Area:{handler:o.renderGraphicChild},Radar:{handler:o.renderGraphicChild},RadialBar:{handler:o.renderGraphicChild},Scatter:{handler:o.renderGraphicChild},Pie:{handler:o.renderGraphicChild},Funnel:{handler:o.renderGraphicChild},Tooltip:{handler:o.renderCursor,once:!0},PolarGrid:{handler:o.renderPolarGrid,once:!0},PolarAngleAxis:{handler:o.renderPolarAxis},PolarRadiusAxis:{handler:o.renderPolarAxis},Customized:{handler:o.renderCustomized}}),o.clipPathId="".concat(null!==(r=t.id)&&void 0!==r?r:(0,M.EL)("recharts"),"-clip"),o.throttleTriggeredAfterMouseMove=m()(o.triggeredAfterMouseMove,null!==(n=t.throttleDelay)&&void 0!==n?n:1e3/60),o.state={},o}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&ed(t,e)}(a,t),r=[{key:"componentDidMount",value:function(){var t,e;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:null!==(t=this.props.margin.left)&&void 0!==t?t:0,top:null!==(e=this.props.margin.top)&&void 0!==e?e:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var t=this.props,e=t.children,r=t.data,n=t.height,o=t.layout,i=(0,A.sP)(e,w.u);if(i){var a=i.props.defaultIndex;if("number"==typeof a&&!(a<0)&&!(a>this.state.tooltipTicks.length-1)){var u=this.state.tooltipTicks[a]&&this.state.tooltipTicks[a].value,c=eM(this.state,r,a,u),l=this.state.tooltipTicks[a].coordinate,s=(this.state.offset.top+n)/2,f="horizontal"===o?{x:l,y:s}:{y:l,x:s},p=this.state.formattedGraphicalItems.find(function(t){return"Scatter"===t.item.type.name});p&&(f=eg(eg({},f),p.props.points[a].tooltipPosition),c=p.props.points[a].tooltipPayload);var h={activeTooltipIndex:a,isTooltipActive:!0,activeLabel:u,activePayload:c,activeCoordinate:f};this.setState(h),this.renderCursor(i),this.accessibilityManager.setIndex(a)}}}},{key:"getSnapshotBeforeUpdate",value:function(t,e){if(!this.props.accessibilityLayer)return null;if(this.state.tooltipTicks!==e.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==t.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==t.margin){var r,n;this.accessibilityManager.setDetails({offset:{left:null!==(r=this.props.margin.left)&&void 0!==r?r:0,top:null!==(n=this.props.margin.top)&&void 0!==n?n:0}})}return null}},{key:"componentDidUpdate",value:function(t){(0,A.rL)([(0,A.sP)(t.children,w.u)],[(0,A.sP)(this.props.children,w.u)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var t=(0,A.sP)(this.props.children,w.u);if(t&&"boolean"==typeof t.props.shared){var e=t.props.shared?"axis":"item";return c.indexOf(e)>=0?e:o}return o}},{key:"getMouseInfo",value:function(t){if(!this.container)return null;var e=this.container,r=e.getBoundingClientRect(),n=(0,V.os)(r),o={chartX:Math.round(t.pageX-n.left),chartY:Math.round(t.pageY-n.top)},i=r.width/e.offsetWidth||1,a=this.inRange(o.chartX,o.chartY,i);if(!a)return null;var u=this.state,c=u.xAxisMap,l=u.yAxisMap,s=this.getTooltipEventType(),f=e_(this.state,this.props.data,this.props.layout,a);if("axis"!==s&&c&&l){var p=(0,M.Kt)(c).scale,h=(0,M.Kt)(l).scale,d=p&&p.invert?p.invert(o.chartX):null,y=h&&h.invert?h.invert(o.chartY):null;return eg(eg({},o),{},{xValue:d,yValue:y},f)}return f?eg(eg({},o),f):null}},{key:"inRange",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=this.props.layout,o=t/r,i=e/r;if("horizontal"===n||"vertical"===n){var a=this.state.offset;return o>=a.left&&o<=a.left+a.width&&i>=a.top&&i<=a.top+a.height?{x:o,y:i}:null}var u=this.state,c=u.angleAxisMap,l=u.radiusAxisMap;if(c&&l){var s=(0,M.Kt)(c);return(0,tq.z3)({x:o,y:i},s)}return null}},{key:"parseEventsOfWrapper",value:function(){var t=this.props.children,e=this.getTooltipEventType(),r=(0,A.sP)(t,w.u),n={};return r&&"axis"===e&&(n="click"===r.props.trigger?{onClick:this.handleClick}:{onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu}),eg(eg({},(0,tG.Ym)(this.props,this.handleOuterEvent)),n)}},{key:"addListener",value:function(){tX.on(tV,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){tX.removeListener(tV,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(t,e,r){for(var n=this.state.formattedGraphicalItems,o=0,i=n.length;o<i;o++){var a=n[o];if(a.item===t||a.props.key===t.key||e===(0,A.Gf)(a.item.type)&&r===a.childIndex)return a}return null}},{key:"renderClipPath",value:function(){var t=this.clipPathId,e=this.state.offset,r=e.left,n=e.top,o=e.height,a=e.width;return i.createElement("defs",null,i.createElement("clipPath",{id:t},i.createElement("rect",{x:r,y:n,height:o,width:a})))}},{key:"getXScales",value:function(){var t=this.state.xAxisMap;return t?Object.entries(t).reduce(function(t,e){var r=el(e,2),n=r[0],o=r[1];return eg(eg({},t),{},ex({},n,o.scale))},{}):null}},{key:"getYScales",value:function(){var t=this.state.yAxisMap;return t?Object.entries(t).reduce(function(t,e){var r=el(e,2),n=r[0],o=r[1];return eg(eg({},t),{},ex({},n,o.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(t){var e;return null===(e=this.state.xAxisMap)||void 0===e||null===(e=e[t])||void 0===e?void 0:e.scale}},{key:"getYScaleByAxisId",value:function(t){var e;return null===(e=this.state.yAxisMap)||void 0===e||null===(e=e[t])||void 0===e?void 0:e.scale}},{key:"getItemByXY",value:function(t){var e=this.state,r=e.formattedGraphicalItems,n=e.activeItem;if(r&&r.length)for(var o=0,i=r.length;o<i;o++){var a=r[o],u=a.props,c=a.item,l=void 0!==c.type.defaultProps?eg(eg({},c.type.defaultProps),c.props):c.props,s=(0,A.Gf)(c.type);if("Bar"===s){var f=(u.data||[]).find(function(e){return(0,P.X)(t,e)});if(f)return{graphicalItem:a,payload:f}}else if("RadialBar"===s){var p=(u.data||[]).find(function(e){return(0,tq.z3)(t,e)});if(p)return{graphicalItem:a,payload:p}}else if((0,t2.lT)(a,n)||(0,t2.V$)(a,n)||(0,t2.w7)(a,n)){var h=(0,t2.a3)({graphicalItem:a,activeTooltipItem:n,itemData:l.data}),d=void 0===l.activeIndex?h:l.activeIndex;return{graphicalItem:eg(eg({},a),{},{childIndex:d}),payload:(0,t2.w7)(a,n)?l.data[h]:a.props.data[h]}}}return null}},{key:"render",value:function(){var t,e,r=this;if(!(0,A.TT)(this))return null;var n=this.props,o=n.children,a=n.className,u=n.width,c=n.height,l=n.style,s=n.compact,f=n.title,p=n.desc,h=es(n,ea),d=(0,A.L6)(h,!1);if(s)return i.createElement(ty.br,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},i.createElement(x.T,ec({},d,{width:u,height:c,title:f,desc:p}),this.renderClipPath(),(0,A.eu)(o,this.renderMap)));this.props.accessibilityLayer&&(d.tabIndex=null!==(t=this.props.tabIndex)&&void 0!==t?t:0,d.role=null!==(e=this.props.role)&&void 0!==e?e:"application",d.onKeyDown=function(t){r.accessibilityManager.keyboardEvent(t)},d.onFocus=function(){r.accessibilityManager.focus()});var y=this.parseEventsOfWrapper();return i.createElement(ty.br,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},i.createElement("div",ec({className:(0,b.Z)("recharts-wrapper",a),style:eg({position:"relative",cursor:"default",width:u,height:c},l)},y,{ref:function(t){r.container=t}}),i.createElement(x.T,ec({},d,{width:u,height:c,title:f,desc:p,style:ej}),this.renderClipPath(),(0,A.eu)(o,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}],ef(a.prototype,r),n&&ef(a,n),Object.defineProperty(a,"prototype",{writable:!1}),a}(i.Component);ex(j,"displayName",e),ex(j,"defaultProps",eg({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},d)),ex(j,"getDerivedStateFromProps",function(t,e){var r=t.dataKey,n=t.data,o=t.children,i=t.width,a=t.height,c=t.layout,l=t.stackOffset,s=t.margin,f=e.dataStartIndex,p=e.dataEndIndex;if(void 0===e.updateId){var h=eN(t);return eg(eg(eg({},h),{},{updateId:0},v(eg(eg({props:t},h),{},{updateId:0}),e)),{},{prevDataKey:r,prevData:n,prevWidth:i,prevHeight:a,prevLayout:c,prevStackOffset:l,prevMargin:s,prevChildren:o})}if(r!==e.prevDataKey||n!==e.prevData||i!==e.prevWidth||a!==e.prevHeight||c!==e.prevLayout||l!==e.prevStackOffset||!(0,tY.w)(s,e.prevMargin)){var d=eN(t),y={chartX:e.chartX,chartY:e.chartY,isTooltipActive:e.isTooltipActive},m=eg(eg({},e_(e,n,c)),{},{updateId:e.updateId+1}),b=eg(eg(eg({},d),y),m);return eg(eg(eg({},b),v(eg({props:t},b),e)),{},{prevDataKey:r,prevData:n,prevWidth:i,prevHeight:a,prevLayout:c,prevStackOffset:l,prevMargin:s,prevChildren:o})}if(!(0,A.rL)(o,e.prevChildren)){var g,x,O,w,j=(0,A.sP)(o,X),S=j&&null!==(g=null===(x=j.props)||void 0===x?void 0:x.startIndex)&&void 0!==g?g:f,P=j&&null!==(O=null===(w=j.props)||void 0===w?void 0:w.endIndex)&&void 0!==O?O:p,E=u()(n)||S!==f||P!==p?e.updateId+1:e.updateId;return eg(eg({updateId:E},v(eg(eg({props:t},e),{},{updateId:E,dataStartIndex:S,dataEndIndex:P}),e)),{},{prevChildren:o,dataStartIndex:S,dataEndIndex:P})}return null}),ex(j,"renderActiveDot",function(t,e,r){var n;return n=(0,i.isValidElement)(t)?(0,i.cloneElement)(t,e):l()(t)?t(e):i.createElement(S.o,e),i.createElement(O.m,{className:"recharts-active-dot",key:r},n)});var E=(0,i.forwardRef)(function(t,e){return i.createElement(j,ec({},t,{ref:e}))});return E.displayName=j.displayName,E}},36612:function(t,e,r){"use strict";r.d(e,{b:function(){return n}});var n=function(t){return null};n.displayName="Cell"},33343:function(t,e,r){"use strict";r.d(e,{_:function(){return P}});var n=r(2265),o=r(66952),i=r.n(o),a=r(39390),u=r.n(a),c=r(41548),l=r.n(c),s=r(90794),f=r(71224),p=r(43843),h=r(97281),d=r(56120);function y(t){return(y="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var v=["offset"];function m(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function b(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function g(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?b(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=y(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=y(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==y(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function x(){return(x=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var O=function(t){var e=t.value,r=t.formatter,n=i()(t.children)?e:t.children;return u()(r)?r(n):n},w=function(t,e,r){var o,a,u=t.position,c=t.viewBox,l=t.offset,f=t.className,p=c.cx,y=c.cy,v=c.innerRadius,m=c.outerRadius,b=c.startAngle,g=c.endAngle,O=c.clockWise,w=(v+m)/2,j=(0,h.uY)(g-b)*Math.min(Math.abs(g-b),360),S=j>=0?1:-1;"insideStart"===u?(o=b+S*l,a=O):"insideEnd"===u?(o=g-S*l,a=!O):"end"===u&&(o=g+S*l,a=O),a=j<=0?a:!a;var P=(0,d.op)(p,y,w,o),A=(0,d.op)(p,y,w,o+(a?1:-1)*359),E="M".concat(P.x,",").concat(P.y,"\n    A").concat(w,",").concat(w,",0,1,").concat(a?0:1,",\n    ").concat(A.x,",").concat(A.y),k=i()(t.id)?(0,h.EL)("recharts-radial-line-"):t.id;return n.createElement("text",x({},r,{dominantBaseline:"central",className:(0,s.Z)("recharts-radial-bar-label",f)}),n.createElement("defs",null,n.createElement("path",{id:k,d:E})),n.createElement("textPath",{xlinkHref:"#".concat(k)},e))},j=function(t){var e=t.viewBox,r=t.offset,n=t.position,o=e.cx,i=e.cy,a=e.innerRadius,u=e.outerRadius,c=(e.startAngle+e.endAngle)/2;if("outside"===n){var l=(0,d.op)(o,i,u+r,c),s=l.x;return{x:s,y:l.y,textAnchor:s>=o?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"end"};var f=(0,d.op)(o,i,(a+u)/2,c);return{x:f.x,y:f.y,textAnchor:"middle",verticalAnchor:"middle"}},S=function(t){var e=t.viewBox,r=t.parentViewBox,n=t.offset,o=t.position,i=e.x,a=e.y,u=e.width,c=e.height,s=c>=0?1:-1,f=s*n,p=s>0?"end":"start",d=s>0?"start":"end",y=u>=0?1:-1,v=y*n,m=y>0?"end":"start",b=y>0?"start":"end";if("top"===o)return g(g({},{x:i+u/2,y:a-s*n,textAnchor:"middle",verticalAnchor:p}),r?{height:Math.max(a-r.y,0),width:u}:{});if("bottom"===o)return g(g({},{x:i+u/2,y:a+c+f,textAnchor:"middle",verticalAnchor:d}),r?{height:Math.max(r.y+r.height-(a+c),0),width:u}:{});if("left"===o){var x={x:i-v,y:a+c/2,textAnchor:m,verticalAnchor:"middle"};return g(g({},x),r?{width:Math.max(x.x-r.x,0),height:c}:{})}if("right"===o){var O={x:i+u+v,y:a+c/2,textAnchor:b,verticalAnchor:"middle"};return g(g({},O),r?{width:Math.max(r.x+r.width-O.x,0),height:c}:{})}var w=r?{width:u,height:c}:{};return"insideLeft"===o?g({x:i+v,y:a+c/2,textAnchor:b,verticalAnchor:"middle"},w):"insideRight"===o?g({x:i+u-v,y:a+c/2,textAnchor:m,verticalAnchor:"middle"},w):"insideTop"===o?g({x:i+u/2,y:a+f,textAnchor:"middle",verticalAnchor:d},w):"insideBottom"===o?g({x:i+u/2,y:a+c-f,textAnchor:"middle",verticalAnchor:p},w):"insideTopLeft"===o?g({x:i+v,y:a+f,textAnchor:b,verticalAnchor:d},w):"insideTopRight"===o?g({x:i+u-v,y:a+f,textAnchor:m,verticalAnchor:d},w):"insideBottomLeft"===o?g({x:i+v,y:a+c-f,textAnchor:b,verticalAnchor:p},w):"insideBottomRight"===o?g({x:i+u-v,y:a+c-f,textAnchor:m,verticalAnchor:p},w):l()(o)&&((0,h.hj)(o.x)||(0,h.hU)(o.x))&&((0,h.hj)(o.y)||(0,h.hU)(o.y))?g({x:i+(0,h.h1)(o.x,u),y:a+(0,h.h1)(o.y,c),textAnchor:"end",verticalAnchor:"end"},w):g({x:i+u/2,y:a+c/2,textAnchor:"middle",verticalAnchor:"middle"},w)};function P(t){var e,r=t.offset,o=g({offset:void 0===r?5:r},function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,v)),a=o.viewBox,c=o.position,l=o.value,d=o.children,y=o.content,m=o.className,b=o.textBreakAll;if(!a||i()(l)&&i()(d)&&!(0,n.isValidElement)(y)&&!u()(y))return null;if((0,n.isValidElement)(y))return(0,n.cloneElement)(y,o);if(u()(y)){if(e=(0,n.createElement)(y,o),(0,n.isValidElement)(e))return e}else e=O(o);var P="cx"in a&&(0,h.hj)(a.cx),A=(0,p.L6)(o,!0);if(P&&("insideStart"===c||"insideEnd"===c||"end"===c))return w(o,e,A);var E=P?j(o):S(o);return n.createElement(f.x,x({className:(0,s.Z)("recharts-label",void 0===m?"":m)},A,E,{breakAll:b}),e)}P.displayName="Label";var A=function(t){var e=t.cx,r=t.cy,n=t.angle,o=t.startAngle,i=t.endAngle,a=t.r,u=t.radius,c=t.innerRadius,l=t.outerRadius,s=t.x,f=t.y,p=t.top,d=t.left,y=t.width,v=t.height,m=t.clockWise,b=t.labelViewBox;if(b)return b;if((0,h.hj)(y)&&(0,h.hj)(v)){if((0,h.hj)(s)&&(0,h.hj)(f))return{x:s,y:f,width:y,height:v};if((0,h.hj)(p)&&(0,h.hj)(d))return{x:p,y:d,width:y,height:v}}return(0,h.hj)(s)&&(0,h.hj)(f)?{x:s,y:f,width:0,height:0}:(0,h.hj)(e)&&(0,h.hj)(r)?{cx:e,cy:r,startAngle:o||n||0,endAngle:i||n||0,innerRadius:c||0,outerRadius:l||u||a||0,clockWise:m}:t.viewBox?t.viewBox:{}};P.parseViewBox=A,P.renderCallByParent=function(t,e){var r,o,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!t||!t.children&&i&&!t.label)return null;var a=t.children,c=A(t),s=(0,p.NN)(a,P).map(function(t,r){return(0,n.cloneElement)(t,{viewBox:e||c,key:"label-".concat(r)})});return i?[(r=t.label,o=e||c,r?!0===r?n.createElement(P,{key:"label-implicit",viewBox:o}):(0,h.P2)(r)?n.createElement(P,{key:"label-implicit",viewBox:o,value:r}):(0,n.isValidElement)(r)?r.type===P?(0,n.cloneElement)(r,{key:"label-implicit",viewBox:o}):n.createElement(P,{key:"label-implicit",content:r,viewBox:o}):u()(r)?n.createElement(P,{key:"label-implicit",content:r,viewBox:o}):l()(r)?n.createElement(P,x({viewBox:o},r,{key:"label-implicit"})):null:null)].concat(function(t){if(Array.isArray(t))return m(t)}(s)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(s)||function(t,e){if(t){if("string"==typeof t)return m(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return m(t,e)}}(s)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()):s}},561:function(t,e,r){"use strict";r.d(e,{e:function(){return P}});var n=r(2265),o=r(66952),i=r.n(o),a=r(41548),u=r.n(a),c=r(39390),l=r.n(c),s=r(87474),f=r.n(s),p=r(33343),h=r(88357),d=r(43843),y=r(41586);function v(t){return(v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var m=["valueAccessor"],b=["data","dataKey","clockWise","id","textBreakAll"];function g(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function x(){return(x=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function O(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function w(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?O(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=v(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=v(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==v(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):O(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function j(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var S=function(t){return Array.isArray(t.value)?f()(t.value):t.value};function P(t){var e=t.valueAccessor,r=void 0===e?S:e,o=j(t,m),a=o.data,u=o.dataKey,c=o.clockWise,l=o.id,s=o.textBreakAll,f=j(o,b);return a&&a.length?n.createElement(h.m,{className:"recharts-label-list"},a.map(function(t,e){var o=i()(u)?r(t,e):(0,y.F$)(t&&t.payload,u),a=i()(l)?{}:{id:"".concat(l,"-").concat(e)};return n.createElement(p._,x({},(0,d.L6)(t,!0),f,a,{parentViewBox:t.parentViewBox,value:o,textBreakAll:s,viewBox:p._.parseViewBox(i()(c)?t:w(w({},t),{},{clockWise:c})),key:"label-".concat(e),index:e}))})):null}P.displayName="LabelList",P.renderCallByParent=function(t,e){var r,o=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!t||!t.children&&o&&!t.label)return null;var i=t.children,a=(0,d.NN)(i,P).map(function(t,r){return(0,n.cloneElement)(t,{data:e,key:"labelList-".concat(r)})});return o?[(r=t.label)?!0===r?n.createElement(P,{key:"labelList-implicit",data:e}):n.isValidElement(r)||l()(r)?n.createElement(P,{key:"labelList-implicit",data:e,content:r}):u()(r)?n.createElement(P,x({data:e},r,{key:"labelList-implicit"})):null:null].concat(function(t){if(Array.isArray(t))return g(t)}(a)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(a)||function(t,e){if(t){if("string"==typeof t)return g(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return g(t,e)}}(a)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()):a}},79857:function(t,e,r){"use strict";r.d(e,{D:function(){return I}});var n=r(2265),o=r(39390),i=r.n(o),a=r(90794),u=r(47205),c=r(37434),l=r(34519),s=r(12655);function f(t){return(f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function p(){return(p=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function h(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function d(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,g(n.key),n)}}function y(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(y=function(){return!!t})()}function v(t){return(v=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function m(t,e){return(m=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function b(t,e,r){return(e=g(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function g(t){var e=function(t,e){if("object"!=f(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=f(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f(e)?e:e+""}var x=function(t){var e,r;function o(){var t,e;return!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,o),t=o,e=arguments,t=v(t),function(t,e){if(e&&("object"===f(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,y()?Reflect.construct(t,e||[],v(this).constructor):t.apply(this,e))}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&m(t,e)}(o,t),e=[{key:"renderIcon",value:function(t){var e=this.props.inactiveColor,r=32/6,o=32/3,i=t.inactive?e:t.color;if("plainline"===t.type)return n.createElement("line",{strokeWidth:4,fill:"none",stroke:i,strokeDasharray:t.payload.strokeDasharray,x1:0,y1:16,x2:32,y2:16,className:"recharts-legend-icon"});if("line"===t.type)return n.createElement("path",{strokeWidth:4,fill:"none",stroke:i,d:"M0,".concat(16,"h").concat(o,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(2*o,",").concat(16,"\n            H").concat(32,"M").concat(2*o,",").concat(16,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(o,",").concat(16),className:"recharts-legend-icon"});if("rect"===t.type)return n.createElement("path",{stroke:"none",fill:i,d:"M0,".concat(4,"h").concat(32,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(n.isValidElement(t.legendIcon)){var a=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?h(Object(r),!0).forEach(function(e){b(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):h(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({},t);return delete a.legendIcon,n.cloneElement(t.legendIcon,a)}return n.createElement(l.v,{fill:i,cx:16,cy:16,size:32,sizeType:"diameter",type:t.type})}},{key:"renderItems",value:function(){var t=this,e=this.props,r=e.payload,o=e.iconSize,l=e.layout,f=e.formatter,h=e.inactiveColor,d={x:0,y:0,width:32,height:32},y={display:"horizontal"===l?"inline-block":"block",marginRight:10},v={display:"inline-block",verticalAlign:"middle",marginRight:4};return r.map(function(e,r){var l=e.formatter||f,m=(0,a.Z)(b(b({"recharts-legend-item":!0},"legend-item-".concat(r),!0),"inactive",e.inactive));if("none"===e.type)return null;var g=i()(e.value)?null:e.value;(0,u.Z)(!i()(e.value),'The name property is also required when using a function for the dataKey of a chart\'s cartesian components. Ex: <Bar name="Name of my Data"/>');var x=e.inactive?h:e.color;return n.createElement("li",p({className:m,style:y,key:"legend-item-".concat(r)},(0,s.bw)(t.props,e,r)),n.createElement(c.T,{width:o,height:o,viewBox:d,style:v},t.renderIcon(e)),n.createElement("span",{className:"recharts-legend-item-text",style:{color:x}},l?l(g,e,r):g))})}},{key:"render",value:function(){var t=this.props,e=t.payload,r=t.layout,o=t.align;return e&&e.length?n.createElement("ul",{className:"recharts-default-legend",style:{padding:0,margin:0,textAlign:"horizontal"===r?o:"left"}},this.renderItems()):null}}],d(o.prototype,e),r&&d(o,r),Object.defineProperty(o,"prototype",{writable:!1}),o}(n.PureComponent);b(x,"displayName","Legend"),b(x,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var O=r(97281),w=r(50200);function j(t){return(j="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var S=["ref"];function P(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function A(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?P(Object(r),!0).forEach(function(e){T(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):P(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function E(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,C(n.key),n)}}function k(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(k=function(){return!!t})()}function M(t){return(M=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function _(t,e){return(_=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function T(t,e,r){return(e=C(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function C(t){var e=function(t,e){if("object"!=j(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=j(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==j(e)?e:e+""}function D(t){return t.value}var I=function(t){var e,r;function o(){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,o);for(var t,e,r,n=arguments.length,i=Array(n),a=0;a<n;a++)i[a]=arguments[a];return e=o,r=[].concat(i),e=M(e),T(t=function(t,e){if(e&&("object"===j(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,k()?Reflect.construct(e,r||[],M(this).constructor):e.apply(this,r)),"lastBoundingBox",{width:-1,height:-1}),t}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&_(t,e)}(o,t),e=[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();return t.height=this.wrapperNode.offsetHeight,t.width=this.wrapperNode.offsetWidth,t}return null}},{key:"updateBBox",value:function(){var t=this.props.onBBoxUpdate,e=this.getBBox();e?(Math.abs(e.width-this.lastBoundingBox.width)>1||Math.abs(e.height-this.lastBoundingBox.height)>1)&&(this.lastBoundingBox.width=e.width,this.lastBoundingBox.height=e.height,t&&t(e)):(-1!==this.lastBoundingBox.width||-1!==this.lastBoundingBox.height)&&(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,t&&t(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?A({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(t){var e,r,n=this.props,o=n.layout,i=n.align,a=n.verticalAlign,u=n.margin,c=n.chartWidth,l=n.chartHeight;return t&&(void 0!==t.left&&null!==t.left||void 0!==t.right&&null!==t.right)||(e="center"===i&&"vertical"===o?{left:((c||0)-this.getBBoxSnapshot().width)/2}:"right"===i?{right:u&&u.right||0}:{left:u&&u.left||0}),t&&(void 0!==t.top&&null!==t.top||void 0!==t.bottom&&null!==t.bottom)||(r="middle"===a?{top:((l||0)-this.getBBoxSnapshot().height)/2}:"bottom"===a?{bottom:u&&u.bottom||0}:{top:u&&u.top||0}),A(A({},e),r)}},{key:"render",value:function(){var t=this,e=this.props,r=e.content,o=e.width,i=e.height,a=e.wrapperStyle,u=e.payloadUniqBy,c=e.payload,l=A(A({position:"absolute",width:o||"auto",height:i||"auto"},this.getDefaultPosition(a)),a);return n.createElement("div",{className:"recharts-legend-wrapper",style:l,ref:function(e){t.wrapperNode=e}},function(t,e){if(n.isValidElement(t))return n.cloneElement(t,e);if("function"==typeof t)return n.createElement(t,e);e.ref;var r=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(e,S);return n.createElement(x,r)}(r,A(A({},this.props),{},{payload:(0,w.z)(c,u,D)})))}}],r=[{key:"getWithHeight",value:function(t,e){var r=A(A({},this.defaultProps),t.props).layout;return"vertical"===r&&(0,O.hj)(t.props.height)?{height:t.props.height}:"horizontal"===r?{width:t.props.width||e}:null}}],e&&E(o.prototype,e),r&&E(o,r),Object.defineProperty(o,"prototype",{writable:!1}),o}(n.PureComponent);T(I,"displayName","Legend"),T(I,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"})},45253:function(t,e,r){"use strict";r.d(e,{h:function(){return d}});var n=r(90794),o=r(2265),i=r(91836),a=r.n(i),u=r(97281),c=r(47205),l=r(43843);function s(t){return(s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function f(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function p(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?f(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=s(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=s(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==s(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function h(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var d=(0,o.forwardRef)(function(t,e){var r,i=t.aspect,s=t.initialDimension,f=void 0===s?{width:-1,height:-1}:s,d=t.width,y=void 0===d?"100%":d,v=t.height,m=void 0===v?"100%":v,b=t.minWidth,g=void 0===b?0:b,x=t.minHeight,O=t.maxHeight,w=t.children,j=t.debounce,S=void 0===j?0:j,P=t.id,A=t.className,E=t.onResize,k=t.style,M=(0,o.useRef)(null),_=(0,o.useRef)();_.current=E,(0,o.useImperativeHandle)(e,function(){return Object.defineProperty(M.current,"current",{get:function(){return console.warn("The usage of ref.current.current is deprecated and will no longer be supported."),M.current},configurable:!0})});var T=function(t){if(Array.isArray(t))return t}(r=(0,o.useState)({containerWidth:f.width,containerHeight:f.height}))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(r,2)||function(t,e){if(t){if("string"==typeof t)return h(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return h(t,e)}}(r,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),C=T[0],D=T[1],I=(0,o.useCallback)(function(t,e){D(function(r){var n=Math.round(t),o=Math.round(e);return r.containerWidth===n&&r.containerHeight===o?r:{containerWidth:n,containerHeight:o}})},[]);(0,o.useEffect)(function(){var t=function(t){var e,r=t[0].contentRect,n=r.width,o=r.height;I(n,o),null===(e=_.current)||void 0===e||e.call(_,n,o)};S>0&&(t=a()(t,S,{trailing:!0,leading:!1}));var e=new ResizeObserver(t),r=M.current.getBoundingClientRect();return I(r.width,r.height),e.observe(M.current),function(){e.disconnect()}},[I,S]);var N=(0,o.useMemo)(function(){var t=C.containerWidth,e=C.containerHeight;if(t<0||e<0)return null;(0,c.Z)((0,u.hU)(y)||(0,u.hU)(m),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",y,m),(0,c.Z)(!i||i>0,"The aspect(%s) must be greater than zero.",i);var r=(0,u.hU)(y)?t:y,n=(0,u.hU)(m)?e:m;i&&i>0&&(r?n=r/i:n&&(r=n*i),O&&n>O&&(n=O)),(0,c.Z)(r>0||n>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",r,n,y,m,g,x,i);var a=!Array.isArray(w)&&(0,l.Gf)(w.type).endsWith("Chart");return o.Children.map(w,function(t){return o.isValidElement(t)?(0,o.cloneElement)(t,p({width:r,height:n},a?{style:p({height:"100%",width:"100%",maxHeight:n,maxWidth:r},t.props.style)}:{})):t})},[i,w,m,O,x,g,C,y]);return o.createElement("div",{id:P?"".concat(P):void 0,className:(0,n.Z)("recharts-responsive-container",A),style:p(p({},void 0===k?{}:k),{},{width:y,height:m,minWidth:g,minHeight:x,maxHeight:O}),ref:M},N)})},71224:function(t,e,r){"use strict";r.d(e,{x:function(){return Text}});var n=r(2265),o=r(66952),i=r.n(o),a=r(90794),u=r(97281),c=r(3841),l=r(43843),s=r(54768);function f(t){return(f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function p(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(t,e)||function(t,e){if(t){if("string"==typeof t)return h(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return h(t,e)}}(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function h(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function d(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,function(t){var e=function(t,e){if("object"!=f(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=f(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f(e)?e:e+""}(n.key),n)}}var y=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,v=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,m=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,b=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,g={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},x=Object.keys(g),O=function(){var t,e;function r(t,e){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),this.num=t,this.unit=e,this.num=t,this.unit=e,Number.isNaN(t)&&(this.unit=""),""===e||m.test(e)||(this.num=NaN,this.unit=""),x.includes(e)&&(this.num=t*g[e],this.unit="px")}return t=[{key:"add",value:function(t){return this.unit!==t.unit?new r(NaN,""):new r(this.num+t.num,this.unit)}},{key:"subtract",value:function(t){return this.unit!==t.unit?new r(NaN,""):new r(this.num-t.num,this.unit)}},{key:"multiply",value:function(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new r(NaN,""):new r(this.num*t.num,this.unit||t.unit)}},{key:"divide",value:function(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new r(NaN,""):new r(this.num/t.num,this.unit||t.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}],e=[{key:"parse",value:function(t){var e,n=p(null!==(e=b.exec(t))&&void 0!==e?e:[],3),o=n[1],i=n[2];return new r(parseFloat(o),null!=i?i:"")}}],t&&d(r.prototype,t),e&&d(r,e),Object.defineProperty(r,"prototype",{writable:!1}),r}();function w(t){if(t.includes("NaN"))return"NaN";for(var e=t;e.includes("*")||e.includes("/");){var r,n=p(null!==(r=y.exec(e))&&void 0!==r?r:[],4),o=n[1],i=n[2],a=n[3],u=O.parse(null!=o?o:""),c=O.parse(null!=a?a:""),l="*"===i?u.multiply(c):u.divide(c);if(l.isNaN())return"NaN";e=e.replace(y,l.toString())}for(;e.includes("+")||/.-\d+(?:\.\d+)?/.test(e);){var s,f=p(null!==(s=v.exec(e))&&void 0!==s?s:[],4),h=f[1],d=f[2],m=f[3],b=O.parse(null!=h?h:""),g=O.parse(null!=m?m:""),x="+"===d?b.add(g):b.subtract(g);if(x.isNaN())return"NaN";e=e.replace(v,x.toString())}return e}var j=/\(([^()]*)\)/;function S(t){var e=function(t){try{var e;return e=t.replace(/\s+/g,""),e=function(t){for(var e=t;e.includes("(");){var r=p(j.exec(e),2)[1];e=e.replace(j,w(r))}return e}(e),e=w(e)}catch(t){return"NaN"}}(t.slice(5,-1));return"NaN"===e?"":e}var P=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],A=["dx","dy","angle","className","breakAll"];function E(){return(E=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function k(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function M(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(t,e)||function(t,e){if(t){if("string"==typeof t)return _(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return _(t,e)}}(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var T=/[ \f\n\r\t\v\u2028\u2029]+/,C=function(t){var e=t.children,r=t.breakAll,n=t.style;try{var o=[];i()(e)||(o=r?e.toString().split(""):e.toString().split(T));var a=o.map(function(t){return{word:t,width:(0,s.xE)(t,n).width}}),u=r?0:(0,s.xE)("\xa0",n).width;return{wordsWithComputedWidth:a,spaceWidth:u}}catch(t){return null}},D=function(t,e,r,n,o){var i,a=t.maxLines,c=t.children,l=t.style,s=t.breakAll,f=(0,u.hj)(a),p=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t.reduce(function(t,e){var i=e.word,a=e.width,u=t[t.length-1];return u&&(null==n||o||u.width+a+r<Number(n))?(u.words.push(i),u.width+=a+r):t.push({words:[i],width:a}),t},[])},h=p(e);if(!f)return h;for(var d=function(t){var e=p(C({breakAll:s,style:l,children:c.slice(0,t)+"…"}).wordsWithComputedWidth);return[e.length>a||e.reduce(function(t,e){return t.width>e.width?t:e}).width>Number(n),e]},y=0,v=c.length-1,m=0;y<=v&&m<=c.length-1;){var b=Math.floor((y+v)/2),g=M(d(b-1),2),x=g[0],O=g[1],w=M(d(b),1)[0];if(x||w||(y=b+1),x&&w&&(v=b-1),!x&&w){i=O;break}m++}return i||h},I=function(t){return[{words:i()(t)?[]:t.toString().split(T)}]},N=function(t){var e=t.width,r=t.scaleToFit,n=t.children,o=t.style,i=t.breakAll,a=t.maxLines;if((e||r)&&!c.x.isSsr){var u=C({breakAll:i,children:n,style:o});return u?D({breakAll:i,children:n,maxLines:a,style:o},u.wordsWithComputedWidth,u.spaceWidth,e,r):I(n)}return I(n)},B="#808080",Text=function(t){var e,r=t.x,o=void 0===r?0:r,i=t.y,c=void 0===i?0:i,s=t.lineHeight,f=void 0===s?"1em":s,p=t.capHeight,h=void 0===p?"0.71em":p,d=t.scaleToFit,y=void 0!==d&&d,v=t.textAnchor,m=t.verticalAnchor,b=t.fill,g=void 0===b?B:b,x=k(t,P),O=(0,n.useMemo)(function(){return N({breakAll:x.breakAll,children:x.children,maxLines:x.maxLines,scaleToFit:y,style:x.style,width:x.width})},[x.breakAll,x.children,x.maxLines,y,x.style,x.width]),w=x.dx,j=x.dy,M=x.angle,_=x.className,T=x.breakAll,C=k(x,A);if(!(0,u.P2)(o)||!(0,u.P2)(c))return null;var D=o+((0,u.hj)(w)?w:0),I=c+((0,u.hj)(j)?j:0);switch(void 0===m?"end":m){case"start":e=S("calc(".concat(h,")"));break;case"middle":e=S("calc(".concat((O.length-1)/2," * -").concat(f," + (").concat(h," / 2))"));break;default:e=S("calc(".concat(O.length-1," * -").concat(f,")"))}var L=[];if(y){var R=O[0].width,z=x.width;L.push("scale(".concat(((0,u.hj)(z)?z/R:1)/R,")"))}return M&&L.push("rotate(".concat(M,", ").concat(D,", ").concat(I,")")),L.length&&(C.transform=L.join(" ")),n.createElement("text",E({},(0,l.L6)(C,!0),{x:D,y:I,className:(0,a.Z)("recharts-text",_),textAnchor:void 0===v?"start":v,fill:g.includes("url")?B:g}),O.map(function(t,r){var o=t.words.join(T?"":" ");return n.createElement("tspan",{x:D,dy:0===r?e:f,key:"".concat(o,"-").concat(r)},o)}))}},86812:function(t,e,r){"use strict";r.d(e,{u:function(){return W}});var n=r(2265),o=r(64677),i=r.n(o),a=r(66952),u=r.n(a),c=r(90794),l=r(97281);function s(t){return(s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function f(){return(f=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function p(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function h(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function d(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?h(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=s(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=s(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==s(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):h(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function y(t){return Array.isArray(t)&&(0,l.P2)(t[0])&&(0,l.P2)(t[1])?t.join(" ~ "):t}var v=function(t){var e=t.separator,r=void 0===e?" : ":e,o=t.contentStyle,a=t.itemStyle,s=void 0===a?{}:a,h=t.labelStyle,v=t.payload,m=t.formatter,b=t.itemSorter,g=t.wrapperClassName,x=t.labelClassName,O=t.label,w=t.labelFormatter,j=t.accessibilityLayer,S=d({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},void 0===o?{}:o),P=d({margin:0},void 0===h?{}:h),A=!u()(O),E=A?O:"",k=(0,c.Z)("recharts-default-tooltip",g),M=(0,c.Z)("recharts-tooltip-label",x);return A&&w&&null!=v&&(E=w(O,v)),n.createElement("div",f({className:k,style:S},void 0!==j&&j?{role:"status","aria-live":"assertive"}:{}),n.createElement("p",{className:M,style:P},n.isValidElement(E)?E:"".concat(E)),function(){if(v&&v.length){var t=(b?i()(v,b):v).map(function(t,e){if("none"===t.type)return null;var o=d({display:"block",paddingTop:4,paddingBottom:4,color:t.color||"#000"},s),i=t.formatter||m||y,a=t.value,u=t.name,c=a,f=u;if(i&&null!=c&&null!=f){var h=i(a,u,t,e,v);if(Array.isArray(h)){var b=function(t){if(Array.isArray(t))return t}(h)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(h,2)||function(t,e){if(t){if("string"==typeof t)return p(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return p(t,e)}}(h,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();c=b[0],f=b[1]}else c=h}return n.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(e),style:o},(0,l.P2)(f)?n.createElement("span",{className:"recharts-tooltip-item-name"},f):null,(0,l.P2)(f)?n.createElement("span",{className:"recharts-tooltip-item-separator"},r):null,n.createElement("span",{className:"recharts-tooltip-item-value"},c),n.createElement("span",{className:"recharts-tooltip-item-unit"},t.unit||""))});return n.createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},t)}return null}())};function m(t){return(m="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function b(t,e,r){var n;return(n=function(t,e){if("object"!=m(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=m(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==m(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var g="recharts-tooltip-wrapper",x={visibility:"hidden"};function O(t){var e=t.allowEscapeViewBox,r=t.coordinate,n=t.key,o=t.offsetTopLeft,i=t.position,a=t.reverseDirection,u=t.tooltipDimension,c=t.viewBox,s=t.viewBoxDimension;if(i&&(0,l.hj)(i[n]))return i[n];var f=r[n]-u-o,p=r[n]+o;return e[n]?a[n]?f:p:a[n]?f<c[n]?Math.max(p,c[n]):Math.max(f,c[n]):p+u>c[n]+s?Math.max(f,c[n]):Math.max(p,c[n])}function w(t){return(w="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function j(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function S(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?j(Object(r),!0).forEach(function(e){M(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):j(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function P(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,_(n.key),n)}}function A(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(A=function(){return!!t})()}function E(t){return(E=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function k(t,e){return(k=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function M(t,e,r){return(e=_(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function _(t){var e=function(t,e){if("object"!=w(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=w(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==w(e)?e:e+""}var T=function(t){var e,r;function o(){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,o);for(var t,e,r,n=arguments.length,i=Array(n),a=0;a<n;a++)i[a]=arguments[a];return e=o,r=[].concat(i),e=E(e),M(t=function(t,e){if(e&&("object"===w(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,A()?Reflect.construct(e,r||[],E(this).constructor):e.apply(this,r)),"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),M(t,"handleKeyDown",function(e){if("Escape"===e.key){var r,n,o,i;t.setState({dismissed:!0,dismissedAtCoordinate:{x:null!==(r=null===(n=t.props.coordinate)||void 0===n?void 0:n.x)&&void 0!==r?r:0,y:null!==(o=null===(i=t.props.coordinate)||void 0===i?void 0:i.y)&&void 0!==o?o:0}})}}),t}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&k(t,e)}(o,t),e=[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();(Math.abs(t.width-this.state.lastBoundingBox.width)>1||Math.abs(t.height-this.state.lastBoundingBox.height)>1)&&this.setState({lastBoundingBox:{width:t.width,height:t.height}})}else(-1!==this.state.lastBoundingBox.width||-1!==this.state.lastBoundingBox.height)&&this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var t,e;this.props.active&&this.updateBBox(),this.state.dismissed&&((null===(t=this.props.coordinate)||void 0===t?void 0:t.x)!==this.state.dismissedAtCoordinate.x||(null===(e=this.props.coordinate)||void 0===e?void 0:e.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}},{key:"render",value:function(){var t,e,r,o,i,a,u,s,f,p,h,d,y,v,m,w,j,P,A,E=this,k=this.props,M=k.active,_=k.allowEscapeViewBox,T=k.animationDuration,C=k.animationEasing,D=k.children,I=k.coordinate,N=k.hasPayload,B=k.isAnimationActive,L=k.offset,R=k.position,z=k.reverseDirection,U=k.useTranslate3d,$=k.viewBox,F=k.wrapperStyle,Z=(d=(t={allowEscapeViewBox:_,coordinate:I,offsetTopLeft:L,position:R,reverseDirection:z,tooltipBox:this.state.lastBoundingBox,useTranslate3d:U,viewBox:$}).allowEscapeViewBox,y=t.coordinate,v=t.offsetTopLeft,m=t.position,w=t.reverseDirection,j=t.tooltipBox,P=t.useTranslate3d,A=t.viewBox,j.height>0&&j.width>0&&y?(r=(e={translateX:p=O({allowEscapeViewBox:d,coordinate:y,key:"x",offsetTopLeft:v,position:m,reverseDirection:w,tooltipDimension:j.width,viewBox:A,viewBoxDimension:A.width}),translateY:h=O({allowEscapeViewBox:d,coordinate:y,key:"y",offsetTopLeft:v,position:m,reverseDirection:w,tooltipDimension:j.height,viewBox:A,viewBoxDimension:A.height}),useTranslate3d:P}).translateX,o=e.translateY,f={transform:e.useTranslate3d?"translate3d(".concat(r,"px, ").concat(o,"px, 0)"):"translate(".concat(r,"px, ").concat(o,"px)")}):f=x,{cssProperties:f,cssClasses:(a=(i={translateX:p,translateY:h,coordinate:y}).coordinate,u=i.translateX,s=i.translateY,(0,c.Z)(g,b(b(b(b({},"".concat(g,"-right"),(0,l.hj)(u)&&a&&(0,l.hj)(a.x)&&u>=a.x),"".concat(g,"-left"),(0,l.hj)(u)&&a&&(0,l.hj)(a.x)&&u<a.x),"".concat(g,"-bottom"),(0,l.hj)(s)&&a&&(0,l.hj)(a.y)&&s>=a.y),"".concat(g,"-top"),(0,l.hj)(s)&&a&&(0,l.hj)(a.y)&&s<a.y)))}),W=Z.cssClasses,q=Z.cssProperties,Y=S(S({transition:B&&M?"transform ".concat(T,"ms ").concat(C):void 0},q),{},{pointerEvents:"none",visibility:!this.state.dismissed&&M&&N?"visible":"hidden",position:"absolute",top:0,left:0},F);return n.createElement("div",{tabIndex:-1,className:W,style:Y,ref:function(t){E.wrapperNode=t}},D)}}],P(o.prototype,e),r&&P(o,r),Object.defineProperty(o,"prototype",{writable:!1}),o}(n.PureComponent),C=r(3841),D=r(50200);function I(t){return(I="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function N(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function B(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?N(Object(r),!0).forEach(function(e){$(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):N(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function L(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,F(n.key),n)}}function R(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(R=function(){return!!t})()}function z(t){return(z=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function U(t,e){return(U=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function $(t,e,r){return(e=F(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function F(t){var e=function(t,e){if("object"!=I(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=I(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==I(e)?e:e+""}function Z(t){return t.dataKey}var W=function(t){var e,r;function o(){var t,e;return!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,o),t=o,e=arguments,t=z(t),function(t,e){if(e&&("object"===I(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,R()?Reflect.construct(t,e||[],z(this).constructor):t.apply(this,e))}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&U(t,e)}(o,t),e=[{key:"render",value:function(){var t,e=this,r=this.props,o=r.active,i=r.allowEscapeViewBox,a=r.animationDuration,u=r.animationEasing,c=r.content,l=r.coordinate,s=r.filterNull,f=r.isAnimationActive,p=r.offset,h=r.payload,d=r.payloadUniqBy,y=r.position,m=r.reverseDirection,b=r.useTranslate3d,g=r.viewBox,x=r.wrapperStyle,O=null!=h?h:[];s&&O.length&&(O=(0,D.z)(h.filter(function(t){return null!=t.value&&(!0!==t.hide||e.props.includeHidden)}),d,Z));var w=O.length>0;return n.createElement(T,{allowEscapeViewBox:i,animationDuration:a,animationEasing:u,isAnimationActive:f,active:o,coordinate:l,hasPayload:w,offset:p,position:y,reverseDirection:m,useTranslate3d:b,viewBox:g,wrapperStyle:x},(t=B(B({},this.props),{},{payload:O}),n.isValidElement(c)?n.cloneElement(c,t):"function"==typeof c?n.createElement(c,t):n.createElement(v,t)))}}],L(o.prototype,e),r&&L(o,r),Object.defineProperty(o,"prototype",{writable:!1}),o}(n.PureComponent);$(W,"displayName","Tooltip"),$(W,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!C.x.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}})},88357:function(t,e,r){"use strict";r.d(e,{m:function(){return c}});var n=r(2265),o=r(90794),i=r(43843),a=["children","className"];function u(){return(u=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var c=n.forwardRef(function(t,e){var r=t.children,c=t.className,l=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,a),s=(0,o.Z)("recharts-layer",c);return n.createElement("g",u({className:s},(0,i.L6)(l,!0),{ref:e}),r)})},37434:function(t,e,r){"use strict";r.d(e,{T:function(){return c}});var n=r(2265),o=r(90794),i=r(43843),a=["children","width","height","viewBox","className","style","title","desc"];function u(){return(u=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function c(t){var e=t.children,r=t.width,c=t.height,l=t.viewBox,s=t.className,f=t.style,p=t.title,h=t.desc,d=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,a),y=l||{width:r,height:c,x:0,y:0},v=(0,o.Z)("recharts-surface",s);return n.createElement("svg",u({},(0,i.L6)(d,!0,"svg"),{className:v,width:r,height:c,style:f,viewBox:"".concat(y.x," ").concat(y.y," ").concat(y.width," ").concat(y.height)}),n.createElement("title",null,p),n.createElement("desc",null,h),e)}},32794:function(t,e,r){"use strict";r.d(e,{br:function(){return g},CW:function(){return w},Mw:function(){return k},zn:function(){return E},sp:function(){return x},qD:function(){return A},d2:function(){return P},bH:function(){return O},Ud:function(){return S},Nf:function(){return j}});var n=r(2265),o=r(16389),i=r(37941),a=r.n(i),u=r(61637),c=r.n(u),l=r(23798),s=r.n(l)()(function(t){return{x:t.left,y:t.top,width:t.width,height:t.height}},function(t){return["l",t.left,"t",t.top,"w",t.width,"h",t.height].join("")}),f=r(97281),p=(0,n.createContext)(void 0),h=(0,n.createContext)(void 0),d=(0,n.createContext)(void 0),y=(0,n.createContext)({}),v=(0,n.createContext)(void 0),m=(0,n.createContext)(0),b=(0,n.createContext)(0),g=function(t){var e=t.state,r=e.xAxisMap,o=e.yAxisMap,i=e.offset,a=t.clipPathId,u=t.children,c=t.width,l=t.height,f=s(i);return n.createElement(p.Provider,{value:r},n.createElement(h.Provider,{value:o},n.createElement(y.Provider,{value:i},n.createElement(d.Provider,{value:f},n.createElement(v.Provider,{value:a},n.createElement(m.Provider,{value:l},n.createElement(b.Provider,{value:c},u)))))))},x=function(){return(0,n.useContext)(v)},O=function(t){var e=(0,n.useContext)(p);null!=e||(0,o.Z)(!1);var r=e[t];return null!=r||(0,o.Z)(!1),r},w=function(){var t=(0,n.useContext)(p);return(0,f.Kt)(t)},j=function(){var t=(0,n.useContext)(h);return a()(t,function(t){return c()(t.domain,Number.isFinite)})||(0,f.Kt)(t)},S=function(t){var e=(0,n.useContext)(h);null!=e||(0,o.Z)(!1);var r=e[t];return null!=r||(0,o.Z)(!1),r},P=function(){return(0,n.useContext)(d)},A=function(){return(0,n.useContext)(y)},E=function(){return(0,n.useContext)(b)},k=function(){return(0,n.useContext)(m)}},11697:function(t,e,r){"use strict";r.d(e,{H:function(){return H}});var n=r(2265);function o(){}function i(t,e,r){t._context.bezierCurveTo((2*t._x0+t._x1)/3,(2*t._y0+t._y1)/3,(t._x0+2*t._x1)/3,(t._y0+2*t._y1)/3,(t._x0+4*t._x1+e)/6,(t._y0+4*t._y1+r)/6)}function a(t){this._context=t}function u(t){this._context=t}function c(t){this._context=t}a.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:i(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:i(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},u.prototype={areaStart:o,areaEnd:o,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._x2=t,this._y2=e;break;case 1:this._point=2,this._x3=t,this._y3=e;break;case 2:this._point=3,this._x4=t,this._y4=e,this._context.moveTo((this._x0+4*this._x1+t)/6,(this._y0+4*this._y1+e)/6);break;default:i(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},c.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+t)/6,n=(this._y0+4*this._y1+e)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:i(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}};class l{constructor(t,e){this._context=t,this._x=e}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,e,t,e):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+e)/2,t,this._y0,t,e)}this._x0=t,this._y0=e}}function s(t){this._context=t}function f(t){this._context=t}function p(t){return new f(t)}function h(t,e,r){var n=t._x1-t._x0,o=e-t._x1,i=(t._y1-t._y0)/(n||o<0&&-0),a=(r-t._y1)/(o||n<0&&-0);return((i<0?-1:1)+(a<0?-1:1))*Math.min(Math.abs(i),Math.abs(a),.5*Math.abs((i*o+a*n)/(n+o)))||0}function d(t,e){var r=t._x1-t._x0;return r?(3*(t._y1-t._y0)/r-e)/2:e}function y(t,e,r){var n=t._x0,o=t._y0,i=t._x1,a=t._y1,u=(i-n)/3;t._context.bezierCurveTo(n+u,o+u*e,i-u,a-u*r,i,a)}function v(t){this._context=t}function m(t){this._context=new b(t)}function b(t){this._context=t}function g(t){this._context=t}function x(t){var e,r,n=t.length-1,o=Array(n),i=Array(n),a=Array(n);for(o[0]=0,i[0]=2,a[0]=t[0]+2*t[1],e=1;e<n-1;++e)o[e]=1,i[e]=4,a[e]=4*t[e]+2*t[e+1];for(o[n-1]=2,i[n-1]=7,a[n-1]=8*t[n-1]+t[n],e=1;e<n;++e)r=o[e]/i[e-1],i[e]-=r,a[e]-=r*a[e-1];for(o[n-1]=a[n-1]/i[n-1],e=n-2;e>=0;--e)o[e]=(a[e]-o[e+1])/i[e];for(e=0,i[n-1]=(t[n]+o[n-1])/2;e<n-1;++e)i[e]=2*t[e+1]-o[e+1];return[o,i]}function O(t,e){this._context=t,this._t=e}s.prototype={areaStart:o,areaEnd:o,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(t,e){t=+t,e=+e,this._point?this._context.lineTo(t,e):(this._point=1,this._context.moveTo(t,e))}},f.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._context.lineTo(t,e)}}},v.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:y(this,this._t0,d(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){var r=NaN;if(e=+e,(t=+t)!==this._x1||e!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,y(this,d(this,r=h(this,t,e)),r);break;default:y(this,this._t0,r=h(this,t,e))}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e,this._t0=r}}},(m.prototype=Object.create(v.prototype)).point=function(t,e){v.prototype.point.call(this,e,t)},b.prototype={moveTo:function(t,e){this._context.moveTo(e,t)},closePath:function(){this._context.closePath()},lineTo:function(t,e){this._context.lineTo(e,t)},bezierCurveTo:function(t,e,r,n,o,i){this._context.bezierCurveTo(e,t,n,r,i,o)}},g.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var t=this._x,e=this._y,r=t.length;if(r){if(this._line?this._context.lineTo(t[0],e[0]):this._context.moveTo(t[0],e[0]),2===r)this._context.lineTo(t[1],e[1]);else for(var n=x(t),o=x(e),i=0,a=1;a<r;++i,++a)this._context.bezierCurveTo(n[0][i],o[0][i],n[1][i],o[1][i],t[a],e[a])}(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(t,e){this._x.push(+t),this._y.push(+e)}},O.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,e),this._context.lineTo(t,e);else{var r=this._x*(1-this._t)+t*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,e)}}this._x=t,this._y=e}};var w=r(90418),j=r(47643),S=r(57863);function P(t){return t[0]}function A(t){return t[1]}function E(t,e){var r=(0,j.Z)(!0),n=null,o=p,i=null,a=(0,S.d)(u);function u(u){var c,l,s,f=(u=(0,w.Z)(u)).length,p=!1;for(null==n&&(i=o(s=a())),c=0;c<=f;++c)!(c<f&&r(l=u[c],c,u))===p&&((p=!p)?i.lineStart():i.lineEnd()),p&&i.point(+t(l,c,u),+e(l,c,u));if(s)return i=null,s+""||null}return t="function"==typeof t?t:void 0===t?P:(0,j.Z)(t),e="function"==typeof e?e:void 0===e?A:(0,j.Z)(e),u.x=function(e){return arguments.length?(t="function"==typeof e?e:(0,j.Z)(+e),u):t},u.y=function(t){return arguments.length?(e="function"==typeof t?t:(0,j.Z)(+t),u):e},u.defined=function(t){return arguments.length?(r="function"==typeof t?t:(0,j.Z)(!!t),u):r},u.curve=function(t){return arguments.length?(o=t,null!=n&&(i=o(n)),u):o},u.context=function(t){return arguments.length?(null==t?n=i=null:i=o(n=t),u):n},u}function k(t,e,r){var n=null,o=(0,j.Z)(!0),i=null,a=p,u=null,c=(0,S.d)(l);function l(l){var s,f,p,h,d,y=(l=(0,w.Z)(l)).length,v=!1,m=Array(y),b=Array(y);for(null==i&&(u=a(d=c())),s=0;s<=y;++s){if(!(s<y&&o(h=l[s],s,l))===v){if(v=!v)f=s,u.areaStart(),u.lineStart();else{for(u.lineEnd(),u.lineStart(),p=s-1;p>=f;--p)u.point(m[p],b[p]);u.lineEnd(),u.areaEnd()}}v&&(m[s]=+t(h,s,l),b[s]=+e(h,s,l),u.point(n?+n(h,s,l):m[s],r?+r(h,s,l):b[s]))}if(d)return u=null,d+""||null}function s(){return E().defined(o).curve(a).context(i)}return t="function"==typeof t?t:void 0===t?P:(0,j.Z)(+t),e="function"==typeof e?e:void 0===e?(0,j.Z)(0):(0,j.Z)(+e),r="function"==typeof r?r:void 0===r?A:(0,j.Z)(+r),l.x=function(e){return arguments.length?(t="function"==typeof e?e:(0,j.Z)(+e),n=null,l):t},l.x0=function(e){return arguments.length?(t="function"==typeof e?e:(0,j.Z)(+e),l):t},l.x1=function(t){return arguments.length?(n=null==t?null:"function"==typeof t?t:(0,j.Z)(+t),l):n},l.y=function(t){return arguments.length?(e="function"==typeof t?t:(0,j.Z)(+t),r=null,l):e},l.y0=function(t){return arguments.length?(e="function"==typeof t?t:(0,j.Z)(+t),l):e},l.y1=function(t){return arguments.length?(r=null==t?null:"function"==typeof t?t:(0,j.Z)(+t),l):r},l.lineX0=l.lineY0=function(){return s().x(t).y(e)},l.lineY1=function(){return s().x(t).y(r)},l.lineX1=function(){return s().x(n).y(e)},l.defined=function(t){return arguments.length?(o="function"==typeof t?t:(0,j.Z)(!!t),l):o},l.curve=function(t){return arguments.length?(a=t,null!=i&&(u=a(i)),l):a},l.context=function(t){return arguments.length?(null==t?i=u=null:u=a(i=t),l):i},l}var M=r(21774),_=r.n(M),T=r(39390),C=r.n(T),D=r(90794),I=r(12655),N=r(43843),B=r(97281);function L(t){return(L="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function R(){return(R=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function z(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function U(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?z(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=L(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=L(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==L(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):z(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var $={curveBasisClosed:function(t){return new u(t)},curveBasisOpen:function(t){return new c(t)},curveBasis:function(t){return new a(t)},curveBumpX:function(t){return new l(t,!0)},curveBumpY:function(t){return new l(t,!1)},curveLinearClosed:function(t){return new s(t)},curveLinear:p,curveMonotoneX:function(t){return new v(t)},curveMonotoneY:function(t){return new m(t)},curveNatural:function(t){return new g(t)},curveStep:function(t){return new O(t,.5)},curveStepAfter:function(t){return new O(t,1)},curveStepBefore:function(t){return new O(t,0)}},F=function(t){return t.x===+t.x&&t.y===+t.y},Z=function(t){return t.x},W=function(t){return t.y},q=function(t,e){if(C()(t))return t;var r="curve".concat(_()(t));return("curveMonotone"===r||"curveBump"===r)&&e?$["".concat(r).concat("vertical"===e?"Y":"X")]:$[r]||p},Y=function(t){var e,r=t.type,n=t.points,o=void 0===n?[]:n,i=t.baseLine,a=t.layout,u=t.connectNulls,c=void 0!==u&&u,l=q(void 0===r?"linear":r,a),s=c?o.filter(function(t){return F(t)}):o;if(Array.isArray(i)){var f=c?i.filter(function(t){return F(t)}):i,p=s.map(function(t,e){return U(U({},t),{},{base:f[e]})});return(e="vertical"===a?k().y(W).x1(Z).x0(function(t){return t.base.x}):k().x(Z).y1(W).y0(function(t){return t.base.y})).defined(F).curve(l),e(p)}return(e="vertical"===a&&(0,B.hj)(i)?k().y(W).x1(Z).x0(i):(0,B.hj)(i)?k().x(Z).y1(W).y0(i):E().x(Z).y(W)).defined(F).curve(l),e(s)},H=function(t){var e=t.className,r=t.points,o=t.path,i=t.pathRef;if((!r||!r.length)&&!o)return null;var a=r&&r.length?Y(t):o;return n.createElement("path",R({},(0,N.L6)(t,!1),(0,I.Ym)(t),{className:(0,D.Z)("recharts-curve",e),d:a,ref:i}))}},14304:function(t,e,r){"use strict";r.d(e,{o:function(){return c}});var n=r(2265),o=r(90794),i=r(12655),a=r(43843);function u(){return(u=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var c=function(t){var e=t.cx,r=t.cy,c=t.r,l=t.className,s=(0,o.Z)("recharts-dot",l);return e===+e&&r===+r&&c===+c?n.createElement("circle",u({},(0,a.L6)(t,!1),(0,i.Ym)(t),{className:s,cx:e,cy:r,r:c})):null}},200:function(t,e,r){"use strict";r.d(e,{A:function(){return y},X:function(){return h}});var n=r(2265),o=r(90794),i=r(98917),a=r(43843);function u(t){return(u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function c(){return(c=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function l(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function s(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function f(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?s(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=u(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=u(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==u(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var p=function(t,e,r,n,o){var i,a=Math.min(Math.abs(r)/2,Math.abs(n)/2),u=n>=0?1:-1,c=r>=0?1:-1,l=n>=0&&r>=0||n<0&&r<0?1:0;if(a>0&&o instanceof Array){for(var s=[0,0,0,0],f=0;f<4;f++)s[f]=o[f]>a?a:o[f];i="M".concat(t,",").concat(e+u*s[0]),s[0]>0&&(i+="A ".concat(s[0],",").concat(s[0],",0,0,").concat(l,",").concat(t+c*s[0],",").concat(e)),i+="L ".concat(t+r-c*s[1],",").concat(e),s[1]>0&&(i+="A ".concat(s[1],",").concat(s[1],",0,0,").concat(l,",\n        ").concat(t+r,",").concat(e+u*s[1])),i+="L ".concat(t+r,",").concat(e+n-u*s[2]),s[2]>0&&(i+="A ".concat(s[2],",").concat(s[2],",0,0,").concat(l,",\n        ").concat(t+r-c*s[2],",").concat(e+n)),i+="L ".concat(t+c*s[3],",").concat(e+n),s[3]>0&&(i+="A ".concat(s[3],",").concat(s[3],",0,0,").concat(l,",\n        ").concat(t,",").concat(e+n-u*s[3])),i+="Z"}else if(a>0&&o===+o&&o>0){var p=Math.min(a,o);i="M ".concat(t,",").concat(e+u*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+c*p,",").concat(e,"\n            L ").concat(t+r-c*p,",").concat(e,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+r,",").concat(e+u*p,"\n            L ").concat(t+r,",").concat(e+n-u*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+r-c*p,",").concat(e+n,"\n            L ").concat(t+c*p,",").concat(e+n,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t,",").concat(e+n-u*p," Z")}else i="M ".concat(t,",").concat(e," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return i},h=function(t,e){if(!t||!e)return!1;var r=t.x,n=t.y,o=e.x,i=e.y,a=e.width,u=e.height;return!!(Math.abs(a)>0&&Math.abs(u)>0)&&r>=Math.min(o,o+a)&&r<=Math.max(o,o+a)&&n>=Math.min(i,i+u)&&n<=Math.max(i,i+u)},d={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},y=function(t){var e,r=f(f({},d),t),u=(0,n.useRef)(),s=function(t){if(Array.isArray(t))return t}(e=(0,n.useState)(-1))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(e,2)||function(t,e){if(t){if("string"==typeof t)return l(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return l(t,e)}}(e,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),h=s[0],y=s[1];(0,n.useEffect)(function(){if(u.current&&u.current.getTotalLength)try{var t=u.current.getTotalLength();t&&y(t)}catch(t){}},[]);var v=r.x,m=r.y,b=r.width,g=r.height,x=r.radius,O=r.className,w=r.animationEasing,j=r.animationDuration,S=r.animationBegin,P=r.isAnimationActive,A=r.isUpdateAnimationActive;if(v!==+v||m!==+m||b!==+b||g!==+g||0===b||0===g)return null;var E=(0,o.Z)("recharts-rectangle",O);return A?n.createElement(i.ZP,{canBegin:h>0,from:{width:b,height:g,x:v,y:m},to:{width:b,height:g,x:v,y:m},duration:j,animationEasing:w,isActive:A},function(t){var e=t.width,o=t.height,l=t.x,s=t.y;return n.createElement(i.ZP,{canBegin:h>0,from:"0px ".concat(-1===h?1:h,"px"),to:"".concat(h,"px 0px"),attributeName:"strokeDasharray",begin:S,duration:j,isActive:P,easing:w},n.createElement("path",c({},(0,a.L6)(r,!0),{className:E,d:p(l,s,e,o,x),ref:u})))}):n.createElement("path",c({},(0,a.L6)(r,!0),{className:E,d:p(v,m,b,g,x)}))}},77795:function(t,e,r){"use strict";r.d(e,{L:function(){return v}});var n=r(2265),o=r(90794),i=r(43843),a=r(56120),u=r(97281);function c(t){return(c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function l(){return(l=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function s(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function f(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?s(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=c(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=c(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==c(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var p=function(t){var e=t.cx,r=t.cy,n=t.radius,o=t.angle,i=t.sign,u=t.isExternal,c=t.cornerRadius,l=t.cornerIsExternal,s=c*(u?1:-1)+n,f=Math.asin(c/s)/a.Wk,p=l?o:o+i*f;return{center:(0,a.op)(e,r,s,p),circleTangency:(0,a.op)(e,r,n,p),lineTangency:(0,a.op)(e,r,s*Math.cos(f*a.Wk),l?o-i*f:o),theta:f}},h=function(t){var e,r=t.cx,n=t.cy,o=t.innerRadius,i=t.outerRadius,c=t.startAngle,l=(e=t.endAngle,(0,u.uY)(e-c)*Math.min(Math.abs(e-c),359.999)),s=c+l,f=(0,a.op)(r,n,i,c),p=(0,a.op)(r,n,i,s),h="M ".concat(f.x,",").concat(f.y,"\n    A ").concat(i,",").concat(i,",0,\n    ").concat(+(Math.abs(l)>180),",").concat(+(c>s),",\n    ").concat(p.x,",").concat(p.y,"\n  ");if(o>0){var d=(0,a.op)(r,n,o,c),y=(0,a.op)(r,n,o,s);h+="L ".concat(y.x,",").concat(y.y,"\n            A ").concat(o,",").concat(o,",0,\n            ").concat(+(Math.abs(l)>180),",").concat(+(c<=s),",\n            ").concat(d.x,",").concat(d.y," Z")}else h+="L ".concat(r,",").concat(n," Z");return h},d=function(t){var e=t.cx,r=t.cy,n=t.innerRadius,o=t.outerRadius,i=t.cornerRadius,a=t.forceCornerRadius,c=t.cornerIsExternal,l=t.startAngle,s=t.endAngle,f=(0,u.uY)(s-l),d=p({cx:e,cy:r,radius:o,angle:l,sign:f,cornerRadius:i,cornerIsExternal:c}),y=d.circleTangency,v=d.lineTangency,m=d.theta,b=p({cx:e,cy:r,radius:o,angle:s,sign:-f,cornerRadius:i,cornerIsExternal:c}),g=b.circleTangency,x=b.lineTangency,O=b.theta,w=c?Math.abs(l-s):Math.abs(l-s)-m-O;if(w<0)return a?"M ".concat(v.x,",").concat(v.y,"\n        a").concat(i,",").concat(i,",0,0,1,").concat(2*i,",0\n        a").concat(i,",").concat(i,",0,0,1,").concat(-(2*i),",0\n      "):h({cx:e,cy:r,innerRadius:n,outerRadius:o,startAngle:l,endAngle:s});var j="M ".concat(v.x,",").concat(v.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(f<0),",").concat(y.x,",").concat(y.y,"\n    A").concat(o,",").concat(o,",0,").concat(+(w>180),",").concat(+(f<0),",").concat(g.x,",").concat(g.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(f<0),",").concat(x.x,",").concat(x.y,"\n  ");if(n>0){var S=p({cx:e,cy:r,radius:n,angle:l,sign:f,isExternal:!0,cornerRadius:i,cornerIsExternal:c}),P=S.circleTangency,A=S.lineTangency,E=S.theta,k=p({cx:e,cy:r,radius:n,angle:s,sign:-f,isExternal:!0,cornerRadius:i,cornerIsExternal:c}),M=k.circleTangency,_=k.lineTangency,T=k.theta,C=c?Math.abs(l-s):Math.abs(l-s)-E-T;if(C<0&&0===i)return"".concat(j,"L").concat(e,",").concat(r,"Z");j+="L".concat(_.x,",").concat(_.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(f<0),",").concat(M.x,",").concat(M.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(C>180),",").concat(+(f>0),",").concat(P.x,",").concat(P.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(f<0),",").concat(A.x,",").concat(A.y,"Z")}else j+="L".concat(e,",").concat(r,"Z");return j},y={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},v=function(t){var e,r=f(f({},y),t),a=r.cx,c=r.cy,s=r.innerRadius,p=r.outerRadius,v=r.cornerRadius,m=r.forceCornerRadius,b=r.cornerIsExternal,g=r.startAngle,x=r.endAngle,O=r.className;if(p<s||g===x)return null;var w=(0,o.Z)("recharts-sector",O),j=p-s,S=(0,u.h1)(v,j,0,!0);return e=S>0&&360>Math.abs(g-x)?d({cx:a,cy:c,innerRadius:s,outerRadius:p,cornerRadius:Math.min(S,j/2),forceCornerRadius:m,cornerIsExternal:b,startAngle:g,endAngle:x}):h({cx:a,cy:c,innerRadius:s,outerRadius:p,startAngle:g,endAngle:x}),n.createElement("path",l({},(0,i.L6)(r,!0),{className:w,d:e,role:"img"}))}},34519:function(t,e,r){"use strict";r.d(e,{v:function(){return D}});var n=r(2265),o=r(21774),i=r.n(o);let a=Math.cos,u=Math.sin,c=Math.sqrt,l=Math.PI,s=2*l;var f={draw(t,e){let r=c(e/l);t.moveTo(r,0),t.arc(0,0,r,0,s)}};let p=c(1/3),h=2*p,d=u(l/10)/u(7*l/10),y=u(s/10)*d,v=-a(s/10)*d,m=c(3),b=c(3)/2,g=1/c(12),x=(g/2+1)*3;var O=r(47643),w=r(57863);c(3),c(3);var j=r(90794),S=r(43843);function P(t){return(P="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var A=["type","size","sizeType"];function E(){return(E=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function k(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function M(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?k(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=P(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=P(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==P(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):k(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var _={symbolCircle:f,symbolCross:{draw(t,e){let r=c(e/5)/2;t.moveTo(-3*r,-r),t.lineTo(-r,-r),t.lineTo(-r,-3*r),t.lineTo(r,-3*r),t.lineTo(r,-r),t.lineTo(3*r,-r),t.lineTo(3*r,r),t.lineTo(r,r),t.lineTo(r,3*r),t.lineTo(-r,3*r),t.lineTo(-r,r),t.lineTo(-3*r,r),t.closePath()}},symbolDiamond:{draw(t,e){let r=c(e/h),n=r*p;t.moveTo(0,-r),t.lineTo(n,0),t.lineTo(0,r),t.lineTo(-n,0),t.closePath()}},symbolSquare:{draw(t,e){let r=c(e),n=-r/2;t.rect(n,n,r,r)}},symbolStar:{draw(t,e){let r=c(.8908130915292852*e),n=y*r,o=v*r;t.moveTo(0,-r),t.lineTo(n,o);for(let e=1;e<5;++e){let i=s*e/5,c=a(i),l=u(i);t.lineTo(l*r,-c*r),t.lineTo(c*n-l*o,l*n+c*o)}t.closePath()}},symbolTriangle:{draw(t,e){let r=-c(e/(3*m));t.moveTo(0,2*r),t.lineTo(-m*r,-r),t.lineTo(m*r,-r),t.closePath()}},symbolWye:{draw(t,e){let r=c(e/x),n=r/2,o=r*g,i=r*g+r,a=-n;t.moveTo(n,o),t.lineTo(n,i),t.lineTo(a,i),t.lineTo(-.5*n-b*o,b*n+-.5*o),t.lineTo(-.5*n-b*i,b*n+-.5*i),t.lineTo(-.5*a-b*i,b*a+-.5*i),t.lineTo(-.5*n+b*o,-.5*o-b*n),t.lineTo(-.5*n+b*i,-.5*i-b*n),t.lineTo(-.5*a+b*i,-.5*i-b*a),t.closePath()}}},T=Math.PI/180,C=function(t,e,r){if("area"===e)return t;switch(r){case"cross":return 5*t*t/9;case"diamond":return .5*t*t/Math.sqrt(3);case"square":return t*t;case"star":var n=18*T;return 1.25*t*t*(Math.tan(n)-Math.tan(2*n)*Math.pow(Math.tan(n),2));case"triangle":return Math.sqrt(3)*t*t/4;case"wye":return(21-10*Math.sqrt(3))*t*t/8;default:return Math.PI*t*t/4}},D=function(t){var e,r=t.type,o=void 0===r?"circle":r,a=t.size,u=void 0===a?64:a,c=t.sizeType,l=void 0===c?"area":c,s=M(M({},function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,A)),{},{type:o,size:u,sizeType:l}),p=s.className,h=s.cx,d=s.cy,y=(0,S.L6)(s,!0);return h===+h&&d===+d&&u===+u?n.createElement("path",E({},y,{className:(0,j.Z)("recharts-symbols",p),transform:"translate(".concat(h,", ").concat(d,")"),d:(e=_["symbol".concat(i()(o))]||f,(function(t,e){let r=null,n=(0,w.d)(o);function o(){let o;if(r||(r=o=n()),t.apply(this,arguments).draw(r,+e.apply(this,arguments)),o)return r=null,o+""||null}return t="function"==typeof t?t:(0,O.Z)(t||f),e="function"==typeof e?e:(0,O.Z)(void 0===e?64:+e),o.type=function(e){return arguments.length?(t="function"==typeof e?e:(0,O.Z)(e),o):t},o.size=function(t){return arguments.length?(e="function"==typeof t?t:(0,O.Z)(+t),o):e},o.context=function(t){return arguments.length?(r=null==t?null:t,o):r},o})().type(e).size(C(u,l,o))())})):null};D.registerSymbol=function(t,e){_["symbol".concat(i()(t))]=e}},77688:function(t,e,r){"use strict";r.d(e,{bn:function(){return C},a3:function(){return z},lT:function(){return D},V$:function(){return I},w7:function(){return N}});var n=r(2265),o=r(39390),i=r.n(o),a=r(56),u=r.n(a),c=r(95974),l=r.n(c),s=r(62927),f=r.n(s),p=r(200),h=r(90794),d=r(98917),y=r(43843);function v(t){return(v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function m(){return(m=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function b(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function g(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function x(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?g(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=v(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=v(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==v(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):g(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var O=function(t,e,r,n,o){var i=r-n;return"M ".concat(t,",").concat(e)+"L ".concat(t+r,",").concat(e)+"L ".concat(t+r-i/2,",").concat(e+o)+"L ".concat(t+r-i/2-n,",").concat(e+o)+"L ".concat(t,",").concat(e," Z")},w={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},j=function(t){var e,r=x(x({},w),t),o=(0,n.useRef)(),i=function(t){if(Array.isArray(t))return t}(e=(0,n.useState)(-1))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(e,2)||function(t,e){if(t){if("string"==typeof t)return b(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return b(t,e)}}(e,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),a=i[0],u=i[1];(0,n.useEffect)(function(){if(o.current&&o.current.getTotalLength)try{var t=o.current.getTotalLength();t&&u(t)}catch(t){}},[]);var c=r.x,l=r.y,s=r.upperWidth,f=r.lowerWidth,p=r.height,v=r.className,g=r.animationEasing,j=r.animationDuration,S=r.animationBegin,P=r.isUpdateAnimationActive;if(c!==+c||l!==+l||s!==+s||f!==+f||p!==+p||0===s&&0===f||0===p)return null;var A=(0,h.Z)("recharts-trapezoid",v);return P?n.createElement(d.ZP,{canBegin:a>0,from:{upperWidth:0,lowerWidth:0,height:p,x:c,y:l},to:{upperWidth:s,lowerWidth:f,height:p,x:c,y:l},duration:j,animationEasing:g,isActive:P},function(t){var e=t.upperWidth,i=t.lowerWidth,u=t.height,c=t.x,l=t.y;return n.createElement(d.ZP,{canBegin:a>0,from:"0px ".concat(-1===a?1:a,"px"),to:"".concat(a,"px 0px"),attributeName:"strokeDasharray",begin:S,duration:j,easing:g},n.createElement("path",m({},(0,y.L6)(r,!0),{className:A,d:O(c,l,e,i,u),ref:o})))}):n.createElement("g",null,n.createElement("path",m({},(0,y.L6)(r,!0),{className:A,d:O(c,l,s,f,p)})))},S=r(77795),P=r(88357),A=r(34519),E=["option","shapeType","propTransformer","activeClassName","isActive"];function k(t){return(k="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function M(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function _(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?M(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=k(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=k(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==k(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):M(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function T(t){var e=t.shapeType,r=t.elementProps;switch(e){case"rectangle":return n.createElement(p.A,r);case"trapezoid":return n.createElement(j,r);case"sector":return n.createElement(S.L,r);case"symbols":if("symbols"===e)return n.createElement(A.v,r);break;default:return null}}function C(t){var e,r=t.option,o=t.shapeType,a=t.propTransformer,c=t.activeClassName,s=t.isActive,f=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,E);if((0,n.isValidElement)(r))e=(0,n.cloneElement)(r,_(_({},f),(0,n.isValidElement)(r)?r.props:r));else if(i()(r))e=r(f);else if(u()(r)&&!l()(r)){var p=(void 0===a?function(t,e){return _(_({},e),t)}:a)(r,f);e=n.createElement(T,{shapeType:o,elementProps:p})}else e=n.createElement(T,{shapeType:o,elementProps:f});return s?n.createElement(P.m,{className:void 0===c?"recharts-active-shape":c},e):e}function D(t,e){return null!=e&&"trapezoids"in t.props}function I(t,e){return null!=e&&"sectors"in t.props}function N(t,e){return null!=e&&"points"in t.props}function B(t,e){var r,n,o=t.x===(null==e||null===(r=e.labelViewBox)||void 0===r?void 0:r.x)||t.x===e.x,i=t.y===(null==e||null===(n=e.labelViewBox)||void 0===n?void 0:n.y)||t.y===e.y;return o&&i}function L(t,e){var r=t.endAngle===e.endAngle,n=t.startAngle===e.startAngle;return r&&n}function R(t,e){var r=t.x===e.x,n=t.y===e.y,o=t.z===e.z;return r&&n&&o}function z(t){var e,r,n,o=t.activeTooltipItem,i=t.graphicalItem,a=t.itemData,u=(D(i,o)?e="trapezoids":I(i,o)?e="sectors":N(i,o)&&(e="points"),e),c=D(i,o)?null===(r=o.tooltipPayload)||void 0===r||null===(r=r[0])||void 0===r||null===(r=r.payload)||void 0===r?void 0:r.payload:I(i,o)?null===(n=o.tooltipPayload)||void 0===n||null===(n=n[0])||void 0===n||null===(n=n.payload)||void 0===n?void 0:n.payload:N(i,o)?o.payload:{},l=a.filter(function(t,e){var r=f()(c,t),n=i.props[u].filter(function(t){var e;return(D(i,o)?e=B:I(i,o)?e=L:N(i,o)&&(e=R),e)(t,o)}),a=i.props[u].indexOf(n[n.length-1]);return r&&e===a});return a.indexOf(l[l.length-1])}},39677:function(t,e,r){"use strict";r.d(e,{Ky:function(){return O},O1:function(){return b},_b:function(){return g},t9:function(){return m},xE:function(){return w}});var n=r(15277),o=r.n(n),i=r(61637),a=r.n(i),u=r(41586),c=r(43843),l=r(97281),s=r(31346);function f(t){return(f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function p(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,v(n.key),n)}}function h(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function d(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?h(Object(r),!0).forEach(function(e){y(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):h(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function y(t,e,r){return(e=v(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function v(t){var e=function(t,e){if("object"!=f(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=f(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f(e)?e:e+""}var m=function(t,e,r,n,o){var i=t.width,a=t.height,f=t.layout,p=t.children,h=Object.keys(e),v={left:r.left,leftMirror:r.left,right:i-r.right,rightMirror:i-r.right,top:r.top,topMirror:r.top,bottom:a-r.bottom,bottomMirror:a-r.bottom},m=!!(0,c.sP)(p,s.$);return h.reduce(function(i,a){var c,s,p,h,b,g=e[a],x=g.orientation,O=g.domain,w=g.padding,j=void 0===w?{}:w,S=g.mirror,P=g.reversed,A="".concat(x).concat(S?"Mirror":"");if("number"===g.type&&("gap"===g.padding||"no-gap"===g.padding)){var E=O[1]-O[0],k=1/0,M=g.categoricalDomain.sort(l.fC);if(M.forEach(function(t,e){e>0&&(k=Math.min((t||0)-(M[e-1]||0),k))}),Number.isFinite(k)){var _=k/E,T="vertical"===g.layout?r.height:r.width;if("gap"===g.padding&&(c=_*T/2),"no-gap"===g.padding){var C=(0,l.h1)(t.barCategoryGap,_*T),D=_*T/2;c=D-C-(D-C)/T*C}}}s="xAxis"===n?[r.left+(j.left||0)+(c||0),r.left+r.width-(j.right||0)-(c||0)]:"yAxis"===n?"horizontal"===f?[r.top+r.height-(j.bottom||0),r.top+(j.top||0)]:[r.top+(j.top||0)+(c||0),r.top+r.height-(j.bottom||0)-(c||0)]:g.range,P&&(s=[s[1],s[0]]);var I=(0,u.Hq)(g,o,m),N=I.scale,B=I.realScaleType;N.domain(O).range(s),(0,u.zF)(N);var L=(0,u.g$)(N,d(d({},g),{},{realScaleType:B}));"xAxis"===n?(b="top"===x&&!S||"bottom"===x&&S,p=r.left,h=v[A]-b*g.height):"yAxis"===n&&(b="left"===x&&!S||"right"===x&&S,p=v[A]-b*g.width,h=r.top);var R=d(d(d({},g),L),{},{realScaleType:B,x:p,y:h,scale:N,width:"xAxis"===n?r.width:g.width,height:"yAxis"===n?r.height:g.height});return R.bandSize=(0,u.zT)(R,L),g.hide||"xAxis"!==n?g.hide||(v[A]+=(b?-1:1)*R.width):v[A]+=(b?-1:1)*R.height,d(d({},i),{},y({},a,R))},{})},b=function(t,e){var r=t.x,n=t.y,o=e.x,i=e.y;return{x:Math.min(r,o),y:Math.min(n,i),width:Math.abs(o-r),height:Math.abs(i-n)}},g=function(t){return b({x:t.x1,y:t.y1},{x:t.x2,y:t.y2})},x=function(){var t,e;function r(t){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),this.scale=t}return t=[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e.bandAware,n=e.position;if(void 0!==t){if(n)switch(n){case"start":default:return this.scale(t);case"middle":var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+o;case"end":var i=this.bandwidth?this.bandwidth():0;return this.scale(t)+i}if(r){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+a}return this.scale(t)}}},{key:"isInRange",value:function(t){var e=this.range(),r=e[0],n=e[e.length-1];return r<=n?t>=r&&t<=n:t>=n&&t<=r}}],e=[{key:"create",value:function(t){return new r(t)}}],t&&p(r.prototype,t),e&&p(r,e),Object.defineProperty(r,"prototype",{writable:!1}),r}();y(x,"EPS",1e-4);var O=function(t){var e=Object.keys(t).reduce(function(e,r){return d(d({},e),{},y({},r,x.create(t[r])))},{});return d(d({},e),{},{apply:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.bandAware,i=r.position;return o()(t,function(t,r){return e[r].apply(t,{bandAware:n,position:i})})},isInRange:function(t){return a()(t,function(t,r){return e[r].isInRange(t)})}})},w=function(t){var e=t.width,r=t.height,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,o=(n%180+180)%180*Math.PI/180,i=Math.atan(r/e);return Math.abs(o>i&&o<Math.PI-i?r/Math.sin(o):e/Math.cos(o))}},41586:function(t,e,r){"use strict";r.d(e,{By:function(){return n8},VO:function(){return n6},zF:function(){return oc},DO:function(){return oa},Bu:function(){return ol},zT:function(){return oj},qz:function(){return n7},pt:function(){return n5},Yj:function(){return om},Fy:function(){return ov},Hv:function(){return oy},Rf:function(){return on},gF:function(){return n3},s6:function(){return oe},EB:function(){return og},fk:function(){return n4},wh:function(){return oh},O3:function(){return ob},uY:function(){return oo},g$:function(){return od},Qo:function(){return oP},F$:function(){return n2},NA:function(){return or},ko:function(){return oS},ZI:function(){return ot},Hq:function(){return ou},LG:function(){return ow},Vv:function(){return os}});var n,o,i,a,u,c,l,s={};r.r(s),r.d(s,{scaleBand:function(){return f.Z},scaleDiverging:function(){return function t(){var e=tN(r2()(tv));return e.copy=function(){return rQ(e,t())},tj.O.apply(e,arguments)}},scaleDivergingLog:function(){return function t(){var e=tW(r2()).domain([.1,1,10]);return e.copy=function(){return rQ(e,t()).base(e.base())},tj.O.apply(e,arguments)}},scaleDivergingPow:function(){return r3},scaleDivergingSqrt:function(){return r6},scaleDivergingSymlog:function(){return function t(){var e=tH(r2());return e.copy=function(){return rQ(e,t()).constant(e.constant())},tj.O.apply(e,arguments)}},scaleIdentity:function(){return function t(e){var r;function n(t){return null==t||isNaN(t=+t)?r:t}return n.invert=n,n.domain=n.range=function(t){return arguments.length?(e=Array.from(t,td),n):e.slice()},n.unknown=function(t){return arguments.length?(r=t,n):r},n.copy=function(){return t(e).unknown(r)},e=arguments.length?Array.from(e,td):[0,1],tN(n)}},scaleImplicit:function(){return tX.O},scaleLinear:function(){return tB},scaleLog:function(){return function t(){let e=tW(tO()).domain([1,10]);return e.copy=()=>tx(e,t()).base(e.base()),tj.o.apply(e,arguments),e}},scaleOrdinal:function(){return tX.Z},scalePoint:function(){return f.x},scalePow:function(){return tQ},scaleQuantile:function(){return function t(){var e,r=[],n=[],o=[];function i(){var t=0,e=Math.max(1,n.length);for(o=Array(e-1);++t<e;)o[t-1]=function(t,e,r=j){if(!(!(n=t.length)||isNaN(e=+e))){if(e<=0||n<2)return+r(t[0],0,t);if(e>=1)return+r(t[n-1],n-1,t);var n,o=(n-1)*e,i=Math.floor(o),a=+r(t[i],i,t);return a+(+r(t[i+1],i+1,t)-a)*(o-i)}}(r,t/e);return a}function a(t){return null==t||isNaN(t=+t)?e:n[P(o,t)]}return a.invertExtent=function(t){var e=n.indexOf(t);return e<0?[NaN,NaN]:[e>0?o[e-1]:r[0],e<o.length?o[e]:r[r.length-1]]},a.domain=function(t){if(!arguments.length)return r.slice();for(let e of(r=[],t))null==e||isNaN(e=+e)||r.push(e);return r.sort(g),i()},a.range=function(t){return arguments.length?(n=Array.from(t),i()):n.slice()},a.unknown=function(t){return arguments.length?(e=t,a):e},a.quantiles=function(){return o.slice()},a.copy=function(){return t().domain(r).range(n).unknown(e)},tj.o.apply(a,arguments)}},scaleQuantize:function(){return function t(){var e,r=0,n=1,o=1,i=[.5],a=[0,1];function u(t){return null!=t&&t<=t?a[P(i,t,0,o)]:e}function c(){var t=-1;for(i=Array(o);++t<o;)i[t]=((t+1)*n-(t-o)*r)/(o+1);return u}return u.domain=function(t){return arguments.length?([r,n]=t,r=+r,n=+n,c()):[r,n]},u.range=function(t){return arguments.length?(o=(a=Array.from(t)).length-1,c()):a.slice()},u.invertExtent=function(t){var e=a.indexOf(t);return e<0?[NaN,NaN]:e<1?[r,i[0]]:e>=o?[i[o-1],n]:[i[e-1],i[e]]},u.unknown=function(t){return arguments.length&&(e=t),u},u.thresholds=function(){return i.slice()},u.copy=function(){return t().domain([r,n]).range(a).unknown(e)},tj.o.apply(tN(u),arguments)}},scaleRadial:function(){return function t(){var e,r=tw(),n=[0,1],o=!1;function i(t){var n,i=Math.sign(n=r(t))*Math.sqrt(Math.abs(n));return isNaN(i)?e:o?Math.round(i):i}return i.invert=function(t){return r.invert(t1(t))},i.domain=function(t){return arguments.length?(r.domain(t),i):r.domain()},i.range=function(t){return arguments.length?(r.range((n=Array.from(t,td)).map(t1)),i):n.slice()},i.rangeRound=function(t){return i.range(t).round(!0)},i.round=function(t){return arguments.length?(o=!!t,i):o},i.clamp=function(t){return arguments.length?(r.clamp(t),i):r.clamp()},i.unknown=function(t){return arguments.length?(e=t,i):e},i.copy=function(){return t(r.domain(),n).round(o).clamp(r.clamp()).unknown(e)},tj.o.apply(i,arguments),tN(i)}},scaleSequential:function(){return function t(){var e=tN(rJ()(tv));return e.copy=function(){return rQ(e,t())},tj.O.apply(e,arguments)}},scaleSequentialLog:function(){return function t(){var e=tW(rJ()).domain([1,10]);return e.copy=function(){return rQ(e,t()).base(e.base())},tj.O.apply(e,arguments)}},scaleSequentialPow:function(){return r0},scaleSequentialQuantile:function(){return function t(){var e=[],r=tv;function n(t){if(null!=t&&!isNaN(t=+t))return r((P(e,t,1)-1)/(e.length-1))}return n.domain=function(t){if(!arguments.length)return e.slice();for(let r of(e=[],t))null==r||isNaN(r=+r)||e.push(r);return e.sort(g),n},n.interpolator=function(t){return arguments.length?(r=t,n):r},n.range=function(){return e.map((t,n)=>r(n/(e.length-1)))},n.quantiles=function(t){return Array.from({length:t+1},(r,n)=>(function(t,e,r){if(!(!(n=(t=Float64Array.from(function*(t,e){if(void 0===e)for(let e of t)null!=e&&(e=+e)>=e&&(yield e);else{let r=-1;for(let n of t)null!=(n=e(n,++r,t))&&(n=+n)>=n&&(yield n)}}(t,void 0))).length)||isNaN(e=+e))){if(e<=0||n<2)return t3(t);if(e>=1)return t2(t);var n,o=(n-1)*e,i=Math.floor(o),a=t2((function t(e,r,n=0,o=1/0,i){if(r=Math.floor(r),n=Math.floor(Math.max(0,n)),o=Math.floor(Math.min(e.length-1,o)),!(n<=r&&r<=o))return e;for(i=void 0===i?t6:function(t=g){if(t===g)return t6;if("function"!=typeof t)throw TypeError("compare is not a function");return(e,r)=>{let n=t(e,r);return n||0===n?n:(0===t(r,r))-(0===t(e,e))}}(i);o>n;){if(o-n>600){let a=o-n+1,u=r-n+1,c=Math.log(a),l=.5*Math.exp(2*c/3),s=.5*Math.sqrt(c*l*(a-l)/a)*(u-a/2<0?-1:1),f=Math.max(n,Math.floor(r-u*l/a+s)),p=Math.min(o,Math.floor(r+(a-u)*l/a+s));t(e,r,f,p,i)}let a=e[r],u=n,c=o;for(t4(e,n,r),i(e[o],a)>0&&t4(e,n,o);u<c;){for(t4(e,u,c),++u,--c;0>i(e[u],a);)++u;for(;i(e[c],a)>0;)--c}0===i(e[n],a)?t4(e,n,c):t4(e,++c,o),c<=r&&(n=c+1),r<=c&&(o=c-1)}return e})(t,i).subarray(0,i+1));return a+(t3(t.subarray(i+1))-a)*(o-i)}})(e,n/t))},n.copy=function(){return t(r).domain(e)},tj.O.apply(n,arguments)}},scaleSequentialSqrt:function(){return r1},scaleSequentialSymlog:function(){return function t(){var e=tH(rJ());return e.copy=function(){return rQ(e,t()).constant(e.constant())},tj.O.apply(e,arguments)}},scaleSqrt:function(){return t0},scaleSymlog:function(){return function t(){var e=tH(tO());return e.copy=function(){return tx(e,t()).constant(e.constant())},tj.o.apply(e,arguments)}},scaleThreshold:function(){return function t(){var e,r=[.5],n=[0,1],o=1;function i(t){return null!=t&&t<=t?n[P(r,t,0,o)]:e}return i.domain=function(t){return arguments.length?(o=Math.min((r=Array.from(t)).length,n.length-1),i):r.slice()},i.range=function(t){return arguments.length?(n=Array.from(t),o=Math.min(r.length,n.length-1),i):n.slice()},i.invertExtent=function(t){var e=n.indexOf(t);return[r[e-1],r[e]]},i.unknown=function(t){return arguments.length?(e=t,i):e},i.copy=function(){return t().domain(r).range(n).unknown(e)},tj.o.apply(i,arguments)}},scaleTime:function(){return rG},scaleUtc:function(){return rK},tickFormat:function(){return tI}});var f=r(83896);let p=Math.sqrt(50),h=Math.sqrt(10),d=Math.sqrt(2);function y(t,e,r){let n,o,i;let a=(e-t)/Math.max(0,r),u=Math.floor(Math.log10(a)),c=a/Math.pow(10,u),l=c>=p?10:c>=h?5:c>=d?2:1;return(u<0?(n=Math.round(t*(i=Math.pow(10,-u)/l)),o=Math.round(e*i),n/i<t&&++n,o/i>e&&--o,i=-i):(n=Math.round(t/(i=Math.pow(10,u)*l)),o=Math.round(e/i),n*i<t&&++n,o*i>e&&--o),o<n&&.5<=r&&r<2)?y(t,e,2*r):[n,o,i]}function v(t,e,r){if(e=+e,t=+t,!((r=+r)>0))return[];if(t===e)return[t];let n=e<t,[o,i,a]=n?y(e,t,r):y(t,e,r);if(!(i>=o))return[];let u=i-o+1,c=Array(u);if(n){if(a<0)for(let t=0;t<u;++t)c[t]=-((i-t)/a);else for(let t=0;t<u;++t)c[t]=(i-t)*a}else if(a<0)for(let t=0;t<u;++t)c[t]=-((o+t)/a);else for(let t=0;t<u;++t)c[t]=(o+t)*a;return c}function m(t,e,r){return y(t=+t,e=+e,r=+r)[2]}function b(t,e,r){e=+e,t=+t,r=+r;let n=e<t,o=n?m(e,t,r):m(t,e,r);return(n?-1:1)*(o<0?-(1/o):o)}function g(t,e){return null==t||null==e?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function x(t,e){return null==t||null==e?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function O(t){let e,r,n;function o(t,n,o=0,i=t.length){if(o<i){if(0!==e(n,n))return i;do{let e=o+i>>>1;0>r(t[e],n)?o=e+1:i=e}while(o<i)}return o}return 2!==t.length?(e=g,r=(e,r)=>g(t(e),r),n=(e,r)=>t(e)-r):(e=t===g||t===x?t:w,r=t,n=t),{left:o,center:function(t,e,r=0,i=t.length){let a=o(t,e,r,i-1);return a>r&&n(t[a-1],e)>-n(t[a],e)?a-1:a},right:function(t,n,o=0,i=t.length){if(o<i){if(0!==e(n,n))return i;do{let e=o+i>>>1;0>=r(t[e],n)?o=e+1:i=e}while(o<i)}return o}}}function w(){return 0}function j(t){return null===t?NaN:+t}let S=O(g),P=S.right;function A(t,e,r){t.prototype=e.prototype=r,r.constructor=t}function E(t,e){var r=Object.create(t.prototype);for(var n in e)r[n]=e[n];return r}function k(){}S.left,O(j).center;var M="\\s*([+-]?\\d+)\\s*",_="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",T="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",C=/^#([0-9a-f]{3,8})$/,D=RegExp(`^rgb\\(${M},${M},${M}\\)$`),I=RegExp(`^rgb\\(${T},${T},${T}\\)$`),N=RegExp(`^rgba\\(${M},${M},${M},${_}\\)$`),B=RegExp(`^rgba\\(${T},${T},${T},${_}\\)$`),L=RegExp(`^hsl\\(${_},${T},${T}\\)$`),R=RegExp(`^hsla\\(${_},${T},${T},${_}\\)$`),z={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function U(){return this.rgb().formatHex()}function $(){return this.rgb().formatRgb()}function F(t){var e,r;return t=(t+"").trim().toLowerCase(),(e=C.exec(t))?(r=e[1].length,e=parseInt(e[1],16),6===r?Z(e):3===r?new Y(e>>8&15|e>>4&240,e>>4&15|240&e,(15&e)<<4|15&e,1):8===r?W(e>>24&255,e>>16&255,e>>8&255,(255&e)/255):4===r?W(e>>12&15|e>>8&240,e>>8&15|e>>4&240,e>>4&15|240&e,((15&e)<<4|15&e)/255):null):(e=D.exec(t))?new Y(e[1],e[2],e[3],1):(e=I.exec(t))?new Y(255*e[1]/100,255*e[2]/100,255*e[3]/100,1):(e=N.exec(t))?W(e[1],e[2],e[3],e[4]):(e=B.exec(t))?W(255*e[1]/100,255*e[2]/100,255*e[3]/100,e[4]):(e=L.exec(t))?J(e[1],e[2]/100,e[3]/100,1):(e=R.exec(t))?J(e[1],e[2]/100,e[3]/100,e[4]):z.hasOwnProperty(t)?Z(z[t]):"transparent"===t?new Y(NaN,NaN,NaN,0):null}function Z(t){return new Y(t>>16&255,t>>8&255,255&t,1)}function W(t,e,r,n){return n<=0&&(t=e=r=NaN),new Y(t,e,r,n)}function q(t,e,r,n){var o;return 1==arguments.length?((o=t)instanceof k||(o=F(o)),o)?new Y((o=o.rgb()).r,o.g,o.b,o.opacity):new Y:new Y(t,e,r,null==n?1:n)}function Y(t,e,r,n){this.r=+t,this.g=+e,this.b=+r,this.opacity=+n}function H(){return`#${K(this.r)}${K(this.g)}${K(this.b)}`}function X(){let t=V(this.opacity);return`${1===t?"rgb(":"rgba("}${G(this.r)}, ${G(this.g)}, ${G(this.b)}${1===t?")":`, ${t})`}`}function V(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function G(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function K(t){return((t=G(t))<16?"0":"")+t.toString(16)}function J(t,e,r,n){return n<=0?t=e=r=NaN:r<=0||r>=1?t=e=NaN:e<=0&&(t=NaN),new tt(t,e,r,n)}function Q(t){if(t instanceof tt)return new tt(t.h,t.s,t.l,t.opacity);if(t instanceof k||(t=F(t)),!t)return new tt;if(t instanceof tt)return t;var e=(t=t.rgb()).r/255,r=t.g/255,n=t.b/255,o=Math.min(e,r,n),i=Math.max(e,r,n),a=NaN,u=i-o,c=(i+o)/2;return u?(a=e===i?(r-n)/u+(r<n)*6:r===i?(n-e)/u+2:(e-r)/u+4,u/=c<.5?i+o:2-i-o,a*=60):u=c>0&&c<1?0:a,new tt(a,u,c,t.opacity)}function tt(t,e,r,n){this.h=+t,this.s=+e,this.l=+r,this.opacity=+n}function te(t){return(t=(t||0)%360)<0?t+360:t}function tr(t){return Math.max(0,Math.min(1,t||0))}function tn(t,e,r){return(t<60?e+(r-e)*t/60:t<180?r:t<240?e+(r-e)*(240-t)/60:e)*255}function to(t,e,r,n,o){var i=t*t,a=i*t;return((1-3*t+3*i-a)*e+(4-6*i+3*a)*r+(1+3*t+3*i-3*a)*n+a*o)/6}A(k,F,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:U,formatHex:U,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return Q(this).formatHsl()},formatRgb:$,toString:$}),A(Y,q,E(k,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new Y(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new Y(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new Y(G(this.r),G(this.g),G(this.b),V(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:H,formatHex:H,formatHex8:function(){return`#${K(this.r)}${K(this.g)}${K(this.b)}${K((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:X,toString:X})),A(tt,function(t,e,r,n){return 1==arguments.length?Q(t):new tt(t,e,r,null==n?1:n)},E(k,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new tt(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new tt(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+(this.h<0)*360,e=isNaN(t)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*e,o=2*r-n;return new Y(tn(t>=240?t-240:t+120,o,n),tn(t,o,n),tn(t<120?t+240:t-120,o,n),this.opacity)},clamp(){return new tt(te(this.h),tr(this.s),tr(this.l),V(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let t=V(this.opacity);return`${1===t?"hsl(":"hsla("}${te(this.h)}, ${100*tr(this.s)}%, ${100*tr(this.l)}%${1===t?")":`, ${t})`}`}}));var ti=t=>()=>t;function ta(t,e){var r=e-t;return r?function(e){return t+e*r}:ti(isNaN(t)?e:t)}var tu=function t(e){var r,n=1==(r=+(r=e))?ta:function(t,e){var n,o,i;return e-t?(n=t,o=e,n=Math.pow(n,i=r),o=Math.pow(o,i)-n,i=1/i,function(t){return Math.pow(n+t*o,i)}):ti(isNaN(t)?e:t)};function o(t,e){var r=n((t=q(t)).r,(e=q(e)).r),o=n(t.g,e.g),i=n(t.b,e.b),a=ta(t.opacity,e.opacity);return function(e){return t.r=r(e),t.g=o(e),t.b=i(e),t.opacity=a(e),t+""}}return o.gamma=t,o}(1);function tc(t){return function(e){var r,n,o=e.length,i=Array(o),a=Array(o),u=Array(o);for(r=0;r<o;++r)n=q(e[r]),i[r]=n.r||0,a[r]=n.g||0,u[r]=n.b||0;return i=t(i),a=t(a),u=t(u),n.opacity=1,function(t){return n.r=i(t),n.g=a(t),n.b=u(t),n+""}}}function tl(t,e){return t=+t,e=+e,function(r){return t*(1-r)+e*r}}tc(function(t){var e=t.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,e-1):Math.floor(r*e),o=t[n],i=t[n+1],a=n>0?t[n-1]:2*o-i,u=n<e-1?t[n+2]:2*i-o;return to((r-n/e)*e,a,o,i,u)}}),tc(function(t){var e=t.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*e),o=t[(n+e-1)%e],i=t[n%e],a=t[(n+1)%e],u=t[(n+2)%e];return to((r-n/e)*e,o,i,a,u)}});var ts=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,tf=RegExp(ts.source,"g");function tp(t,e){var r,n,o=typeof e;return null==e||"boolean"===o?ti(e):("number"===o?tl:"string"===o?(n=F(e))?(e=n,tu):function(t,e){var r,n,o,i,a,u=ts.lastIndex=tf.lastIndex=0,c=-1,l=[],s=[];for(t+="",e+="";(o=ts.exec(t))&&(i=tf.exec(e));)(a=i.index)>u&&(a=e.slice(u,a),l[c]?l[c]+=a:l[++c]=a),(o=o[0])===(i=i[0])?l[c]?l[c]+=i:l[++c]=i:(l[++c]=null,s.push({i:c,x:tl(o,i)})),u=tf.lastIndex;return u<e.length&&(a=e.slice(u),l[c]?l[c]+=a:l[++c]=a),l.length<2?s[0]?(r=s[0].x,function(t){return r(t)+""}):(n=e,function(){return n}):(e=s.length,function(t){for(var r,n=0;n<e;++n)l[(r=s[n]).i]=r.x(t);return l.join("")})}:e instanceof F?tu:e instanceof Date?function(t,e){var r=new Date;return t=+t,e=+e,function(n){return r.setTime(t*(1-n)+e*n),r}}:(r=e,!ArrayBuffer.isView(r)||r instanceof DataView)?Array.isArray(e)?function(t,e){var r,n=e?e.length:0,o=t?Math.min(n,t.length):0,i=Array(o),a=Array(n);for(r=0;r<o;++r)i[r]=tp(t[r],e[r]);for(;r<n;++r)a[r]=e[r];return function(t){for(r=0;r<o;++r)a[r]=i[r](t);return a}}:"function"!=typeof e.valueOf&&"function"!=typeof e.toString||isNaN(e)?function(t,e){var r,n={},o={};for(r in(null===t||"object"!=typeof t)&&(t={}),(null===e||"object"!=typeof e)&&(e={}),e)r in t?n[r]=tp(t[r],e[r]):o[r]=e[r];return function(t){for(r in n)o[r]=n[r](t);return o}}:tl:function(t,e){e||(e=[]);var r,n=t?Math.min(e.length,t.length):0,o=e.slice();return function(i){for(r=0;r<n;++r)o[r]=t[r]*(1-i)+e[r]*i;return o}})(t,e)}function th(t,e){return t=+t,e=+e,function(r){return Math.round(t*(1-r)+e*r)}}function td(t){return+t}var ty=[0,1];function tv(t){return t}function tm(t,e){var r;return(e-=t=+t)?function(r){return(r-t)/e}:(r=isNaN(e)?NaN:.5,function(){return r})}function tb(t,e,r){var n=t[0],o=t[1],i=e[0],a=e[1];return o<n?(n=tm(o,n),i=r(a,i)):(n=tm(n,o),i=r(i,a)),function(t){return i(n(t))}}function tg(t,e,r){var n=Math.min(t.length,e.length)-1,o=Array(n),i=Array(n),a=-1;for(t[n]<t[0]&&(t=t.slice().reverse(),e=e.slice().reverse());++a<n;)o[a]=tm(t[a],t[a+1]),i[a]=r(e[a],e[a+1]);return function(e){var r=P(t,e,1,n)-1;return i[r](o[r](e))}}function tx(t,e){return e.domain(t.domain()).range(t.range()).interpolate(t.interpolate()).clamp(t.clamp()).unknown(t.unknown())}function tO(){var t,e,r,n,o,i,a=ty,u=ty,c=tp,l=tv;function s(){var t,e,r,c=Math.min(a.length,u.length);return l!==tv&&(t=a[0],e=a[c-1],t>e&&(r=t,t=e,e=r),l=function(r){return Math.max(t,Math.min(e,r))}),n=c>2?tg:tb,o=i=null,f}function f(e){return null==e||isNaN(e=+e)?r:(o||(o=n(a.map(t),u,c)))(t(l(e)))}return f.invert=function(r){return l(e((i||(i=n(u,a.map(t),tl)))(r)))},f.domain=function(t){return arguments.length?(a=Array.from(t,td),s()):a.slice()},f.range=function(t){return arguments.length?(u=Array.from(t),s()):u.slice()},f.rangeRound=function(t){return u=Array.from(t),c=th,s()},f.clamp=function(t){return arguments.length?(l=!!t||tv,s()):l!==tv},f.interpolate=function(t){return arguments.length?(c=t,s()):c},f.unknown=function(t){return arguments.length?(r=t,f):r},function(r,n){return t=r,e=n,s()}}function tw(){return tO()(tv,tv)}var tj=r(29468),tS=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function tP(t){var e;if(!(e=tS.exec(t)))throw Error("invalid format: "+t);return new tA({fill:e[1],align:e[2],sign:e[3],symbol:e[4],zero:e[5],width:e[6],comma:e[7],precision:e[8]&&e[8].slice(1),trim:e[9],type:e[10]})}function tA(t){this.fill=void 0===t.fill?" ":t.fill+"",this.align=void 0===t.align?">":t.align+"",this.sign=void 0===t.sign?"-":t.sign+"",this.symbol=void 0===t.symbol?"":t.symbol+"",this.zero=!!t.zero,this.width=void 0===t.width?void 0:+t.width,this.comma=!!t.comma,this.precision=void 0===t.precision?void 0:+t.precision,this.trim=!!t.trim,this.type=void 0===t.type?"":t.type+""}function tE(t,e){if((r=(t=e?t.toExponential(e-1):t.toExponential()).indexOf("e"))<0)return null;var r,n=t.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+t.slice(r+1)]}function tk(t){return(t=tE(Math.abs(t)))?t[1]:NaN}function tM(t,e){var r=tE(t,e);if(!r)return t+"";var n=r[0],o=r[1];return o<0?"0."+Array(-o).join("0")+n:n.length>o+1?n.slice(0,o+1)+"."+n.slice(o+1):n+Array(o-n.length+2).join("0")}tP.prototype=tA.prototype,tA.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};var t_={"%":(t,e)=>(100*t).toFixed(e),b:t=>Math.round(t).toString(2),c:t=>t+"",d:function(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)},e:(t,e)=>t.toExponential(e),f:(t,e)=>t.toFixed(e),g:(t,e)=>t.toPrecision(e),o:t=>Math.round(t).toString(8),p:(t,e)=>tM(100*t,e),r:tM,s:function(t,e){var r=tE(t,e);if(!r)return t+"";var o=r[0],i=r[1],a=i-(n=3*Math.max(-8,Math.min(8,Math.floor(i/3))))+1,u=o.length;return a===u?o:a>u?o+Array(a-u+1).join("0"):a>0?o.slice(0,a)+"."+o.slice(a):"0."+Array(1-a).join("0")+tE(t,Math.max(0,e+a-1))[0]},X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function tT(t){return t}var tC=Array.prototype.map,tD=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function tI(t,e,r,n){var o,u,c=b(t,e,r);switch((n=tP(null==n?",f":n)).type){case"s":var l=Math.max(Math.abs(t),Math.abs(e));return null!=n.precision||isNaN(u=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(tk(l)/3)))-tk(Math.abs(c))))||(n.precision=u),a(n,l);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(u=Math.max(0,tk(Math.abs(Math.max(Math.abs(t),Math.abs(e)))-(o=Math.abs(o=c)))-tk(o))+1)||(n.precision=u-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(u=Math.max(0,-tk(Math.abs(c))))||(n.precision=u-("%"===n.type)*2)}return i(n)}function tN(t){var e=t.domain;return t.ticks=function(t){var r=e();return v(r[0],r[r.length-1],null==t?10:t)},t.tickFormat=function(t,r){var n=e();return tI(n[0],n[n.length-1],null==t?10:t,r)},t.nice=function(r){null==r&&(r=10);var n,o,i=e(),a=0,u=i.length-1,c=i[a],l=i[u],s=10;for(l<c&&(o=c,c=l,l=o,o=a,a=u,u=o);s-- >0;){if((o=m(c,l,r))===n)return i[a]=c,i[u]=l,e(i);if(o>0)c=Math.floor(c/o)*o,l=Math.ceil(l/o)*o;else if(o<0)c=Math.ceil(c*o)/o,l=Math.floor(l*o)/o;else break;n=o}return t},t}function tB(){var t=tw();return t.copy=function(){return tx(t,tB())},tj.o.apply(t,arguments),tN(t)}function tL(t,e){t=t.slice();var r,n=0,o=t.length-1,i=t[n],a=t[o];return a<i&&(r=n,n=o,o=r,r=i,i=a,a=r),t[n]=e.floor(i),t[o]=e.ceil(a),t}function tR(t){return Math.log(t)}function tz(t){return Math.exp(t)}function tU(t){return-Math.log(-t)}function t$(t){return-Math.exp(-t)}function tF(t){return isFinite(t)?+("1e"+t):t<0?0:t}function tZ(t){return(e,r)=>-t(-e,r)}function tW(t){let e,r;let n=t(tR,tz),o=n.domain,a=10;function u(){var i,u;return e=(i=a)===Math.E?Math.log:10===i&&Math.log10||2===i&&Math.log2||(i=Math.log(i),t=>Math.log(t)/i),r=10===(u=a)?tF:u===Math.E?Math.exp:t=>Math.pow(u,t),o()[0]<0?(e=tZ(e),r=tZ(r),t(tU,t$)):t(tR,tz),n}return n.base=function(t){return arguments.length?(a=+t,u()):a},n.domain=function(t){return arguments.length?(o(t),u()):o()},n.ticks=t=>{let n,i;let u=o(),c=u[0],l=u[u.length-1],s=l<c;s&&([c,l]=[l,c]);let f=e(c),p=e(l),h=null==t?10:+t,d=[];if(!(a%1)&&p-f<h){if(f=Math.floor(f),p=Math.ceil(p),c>0){for(;f<=p;++f)for(n=1;n<a;++n)if(!((i=f<0?n/r(-f):n*r(f))<c)){if(i>l)break;d.push(i)}}else for(;f<=p;++f)for(n=a-1;n>=1;--n)if(!((i=f>0?n/r(-f):n*r(f))<c)){if(i>l)break;d.push(i)}2*d.length<h&&(d=v(c,l,h))}else d=v(f,p,Math.min(p-f,h)).map(r);return s?d.reverse():d},n.tickFormat=(t,o)=>{if(null==t&&(t=10),null==o&&(o=10===a?"s":","),"function"!=typeof o&&(a%1||null!=(o=tP(o)).precision||(o.trim=!0),o=i(o)),t===1/0)return o;let u=Math.max(1,a*t/n.ticks().length);return t=>{let n=t/r(Math.round(e(t)));return n*a<a-.5&&(n*=a),n<=u?o(t):""}},n.nice=()=>o(tL(o(),{floor:t=>r(Math.floor(e(t))),ceil:t=>r(Math.ceil(e(t)))})),n}function tq(t){return function(e){return Math.sign(e)*Math.log1p(Math.abs(e/t))}}function tY(t){return function(e){return Math.sign(e)*Math.expm1(Math.abs(e))*t}}function tH(t){var e=1,r=t(tq(1),tY(e));return r.constant=function(r){return arguments.length?t(tq(e=+r),tY(e)):e},tN(r)}i=(o=function(t){var e,r,o,i=void 0===t.grouping||void 0===t.thousands?tT:(e=tC.call(t.grouping,Number),r=t.thousands+"",function(t,n){for(var o=t.length,i=[],a=0,u=e[0],c=0;o>0&&u>0&&(c+u+1>n&&(u=Math.max(1,n-c)),i.push(t.substring(o-=u,o+u)),!((c+=u+1)>n));)u=e[a=(a+1)%e.length];return i.reverse().join(r)}),a=void 0===t.currency?"":t.currency[0]+"",u=void 0===t.currency?"":t.currency[1]+"",c=void 0===t.decimal?".":t.decimal+"",l=void 0===t.numerals?tT:(o=tC.call(t.numerals,String),function(t){return t.replace(/[0-9]/g,function(t){return o[+t]})}),s=void 0===t.percent?"%":t.percent+"",f=void 0===t.minus?"−":t.minus+"",p=void 0===t.nan?"NaN":t.nan+"";function h(t){var e=(t=tP(t)).fill,r=t.align,o=t.sign,h=t.symbol,d=t.zero,y=t.width,v=t.comma,m=t.precision,b=t.trim,g=t.type;"n"===g?(v=!0,g="g"):t_[g]||(void 0===m&&(m=12),b=!0,g="g"),(d||"0"===e&&"="===r)&&(d=!0,e="0",r="=");var x="$"===h?a:"#"===h&&/[boxX]/.test(g)?"0"+g.toLowerCase():"",O="$"===h?u:/[%p]/.test(g)?s:"",w=t_[g],j=/[defgprs%]/.test(g);function S(t){var a,u,s,h=x,S=O;if("c"===g)S=w(t)+S,t="";else{var P=(t=+t)<0||1/t<0;if(t=isNaN(t)?p:w(Math.abs(t),m),b&&(t=function(t){t:for(var e,r=t.length,n=1,o=-1;n<r;++n)switch(t[n]){case".":o=e=n;break;case"0":0===o&&(o=n),e=n;break;default:if(!+t[n])break t;o>0&&(o=0)}return o>0?t.slice(0,o)+t.slice(e+1):t}(t)),P&&0==+t&&"+"!==o&&(P=!1),h=(P?"("===o?o:f:"-"===o||"("===o?"":o)+h,S=("s"===g?tD[8+n/3]:"")+S+(P&&"("===o?")":""),j){for(a=-1,u=t.length;++a<u;)if(48>(s=t.charCodeAt(a))||s>57){S=(46===s?c+t.slice(a+1):t.slice(a))+S,t=t.slice(0,a);break}}}v&&!d&&(t=i(t,1/0));var A=h.length+t.length+S.length,E=A<y?Array(y-A+1).join(e):"";switch(v&&d&&(t=i(E+t,E.length?y-S.length:1/0),E=""),r){case"<":t=h+t+S+E;break;case"=":t=h+E+t+S;break;case"^":t=E.slice(0,A=E.length>>1)+h+t+S+E.slice(A);break;default:t=E+h+t+S}return l(t)}return m=void 0===m?6:/[gprs]/.test(g)?Math.max(1,Math.min(21,m)):Math.max(0,Math.min(20,m)),S.toString=function(){return t+""},S}return{format:h,formatPrefix:function(t,e){var r=h(((t=tP(t)).type="f",t)),n=3*Math.max(-8,Math.min(8,Math.floor(tk(e)/3))),o=Math.pow(10,-n),i=tD[8+n/3];return function(t){return r(o*t)+i}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,a=o.formatPrefix;var tX=r(13507);function tV(t){return function(e){return e<0?-Math.pow(-e,t):Math.pow(e,t)}}function tG(t){return t<0?-Math.sqrt(-t):Math.sqrt(t)}function tK(t){return t<0?-t*t:t*t}function tJ(t){var e=t(tv,tv),r=1;return e.exponent=function(e){return arguments.length?1==(r=+e)?t(tv,tv):.5===r?t(tG,tK):t(tV(r),tV(1/r)):r},tN(e)}function tQ(){var t=tJ(tO());return t.copy=function(){return tx(t,tQ()).exponent(t.exponent())},tj.o.apply(t,arguments),t}function t0(){return tQ.apply(null,arguments).exponent(.5)}function t1(t){return Math.sign(t)*t*t}function t2(t,e){let r;if(void 0===e)for(let e of t)null!=e&&(r<e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let o of t)null!=(o=e(o,++n,t))&&(r<o||void 0===r&&o>=o)&&(r=o)}return r}function t3(t,e){let r;if(void 0===e)for(let e of t)null!=e&&(r>e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let o of t)null!=(o=e(o,++n,t))&&(r>o||void 0===r&&o>=o)&&(r=o)}return r}function t6(t,e){return(null==t||!(t>=t))-(null==e||!(e>=e))||(t<e?-1:t>e?1:0)}function t4(t,e,r){let n=t[e];t[e]=t[r],t[r]=n}let t5=864e5,t7=7*t5,t8=30*t5,t9=365*t5,et=new Date,ee=new Date;function er(t,e,r,n){function o(e){return t(e=0==arguments.length?new Date:new Date(+e)),e}return o.floor=e=>(t(e=new Date(+e)),e),o.ceil=r=>(t(r=new Date(r-1)),e(r,1),t(r),r),o.round=t=>{let e=o(t),r=o.ceil(t);return t-e<r-t?e:r},o.offset=(t,r)=>(e(t=new Date(+t),null==r?1:Math.floor(r)),t),o.range=(r,n,i)=>{let a;let u=[];if(r=o.ceil(r),i=null==i?1:Math.floor(i),!(r<n)||!(i>0))return u;do u.push(a=new Date(+r)),e(r,i),t(r);while(a<r&&r<n);return u},o.filter=r=>er(e=>{if(e>=e)for(;t(e),!r(e);)e.setTime(e-1)},(t,n)=>{if(t>=t){if(n<0)for(;++n<=0;)for(;e(t,-1),!r(t););else for(;--n>=0;)for(;e(t,1),!r(t););}}),r&&(o.count=(e,n)=>(et.setTime(+e),ee.setTime(+n),t(et),t(ee),Math.floor(r(et,ee))),o.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?o.filter(n?e=>n(e)%t==0:e=>o.count(0,e)%t==0):o:null),o}let en=er(()=>{},(t,e)=>{t.setTime(+t+e)},(t,e)=>e-t);en.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?er(e=>{e.setTime(Math.floor(e/t)*t)},(e,r)=>{e.setTime(+e+r*t)},(e,r)=>(r-e)/t):en:null,en.range;let eo=er(t=>{t.setTime(t-t.getMilliseconds())},(t,e)=>{t.setTime(+t+1e3*e)},(t,e)=>(e-t)/1e3,t=>t.getUTCSeconds());eo.range;let ei=er(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds())},(t,e)=>{t.setTime(+t+6e4*e)},(t,e)=>(e-t)/6e4,t=>t.getMinutes());ei.range;let ea=er(t=>{t.setUTCSeconds(0,0)},(t,e)=>{t.setTime(+t+6e4*e)},(t,e)=>(e-t)/6e4,t=>t.getUTCMinutes());ea.range;let eu=er(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds()-6e4*t.getMinutes())},(t,e)=>{t.setTime(+t+36e5*e)},(t,e)=>(e-t)/36e5,t=>t.getHours());eu.range;let ec=er(t=>{t.setUTCMinutes(0,0,0)},(t,e)=>{t.setTime(+t+36e5*e)},(t,e)=>(e-t)/36e5,t=>t.getUTCHours());ec.range;let el=er(t=>t.setHours(0,0,0,0),(t,e)=>t.setDate(t.getDate()+e),(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/t5,t=>t.getDate()-1);el.range;let es=er(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/t5,t=>t.getUTCDate()-1);es.range;let ef=er(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/t5,t=>Math.floor(t/t5));function ep(t){return er(e=>{e.setDate(e.getDate()-(e.getDay()+7-t)%7),e.setHours(0,0,0,0)},(t,e)=>{t.setDate(t.getDate()+7*e)},(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/t7)}ef.range;let eh=ep(0),ed=ep(1),ey=ep(2),ev=ep(3),em=ep(4),eb=ep(5),eg=ep(6);function ex(t){return er(e=>{e.setUTCDate(e.getUTCDate()-(e.getUTCDay()+7-t)%7),e.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+7*e)},(t,e)=>(e-t)/t7)}eh.range,ed.range,ey.range,ev.range,em.range,eb.range,eg.range;let eO=ex(0),ew=ex(1),ej=ex(2),eS=ex(3),eP=ex(4),eA=ex(5),eE=ex(6);eO.range,ew.range,ej.range,eS.range,eP.range,eA.range,eE.range;let ek=er(t=>{t.setDate(1),t.setHours(0,0,0,0)},(t,e)=>{t.setMonth(t.getMonth()+e)},(t,e)=>e.getMonth()-t.getMonth()+(e.getFullYear()-t.getFullYear())*12,t=>t.getMonth());ek.range;let eM=er(t=>{t.setUTCDate(1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCMonth(t.getUTCMonth()+e)},(t,e)=>e.getUTCMonth()-t.getUTCMonth()+(e.getUTCFullYear()-t.getUTCFullYear())*12,t=>t.getUTCMonth());eM.range;let e_=er(t=>{t.setMonth(0,1),t.setHours(0,0,0,0)},(t,e)=>{t.setFullYear(t.getFullYear()+e)},(t,e)=>e.getFullYear()-t.getFullYear(),t=>t.getFullYear());e_.every=t=>isFinite(t=Math.floor(t))&&t>0?er(e=>{e.setFullYear(Math.floor(e.getFullYear()/t)*t),e.setMonth(0,1),e.setHours(0,0,0,0)},(e,r)=>{e.setFullYear(e.getFullYear()+r*t)}):null,e_.range;let eT=er(t=>{t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCFullYear(t.getUTCFullYear()+e)},(t,e)=>e.getUTCFullYear()-t.getUTCFullYear(),t=>t.getUTCFullYear());function eC(t,e,r,n,o,i){let a=[[eo,1,1e3],[eo,5,5e3],[eo,15,15e3],[eo,30,3e4],[i,1,6e4],[i,5,3e5],[i,15,9e5],[i,30,18e5],[o,1,36e5],[o,3,108e5],[o,6,216e5],[o,12,432e5],[n,1,t5],[n,2,2*t5],[r,1,t7],[e,1,t8],[e,3,3*t8],[t,1,t9]];function u(e,r,n){let o=Math.abs(r-e)/n,i=O(([,,t])=>t).right(a,o);if(i===a.length)return t.every(b(e/t9,r/t9,n));if(0===i)return en.every(Math.max(b(e,r,n),1));let[u,c]=a[o/a[i-1][2]<a[i][2]/o?i-1:i];return u.every(c)}return[function(t,e,r){let n=e<t;n&&([t,e]=[e,t]);let o=r&&"function"==typeof r.range?r:u(t,e,r),i=o?o.range(t,+e+1):[];return n?i.reverse():i},u]}eT.every=t=>isFinite(t=Math.floor(t))&&t>0?er(e=>{e.setUTCFullYear(Math.floor(e.getUTCFullYear()/t)*t),e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,r)=>{e.setUTCFullYear(e.getUTCFullYear()+r*t)}):null,eT.range;let[eD,eI]=eC(eT,eM,eO,ef,ec,ea),[eN,eB]=eC(e_,ek,eh,el,eu,ei);function eL(t){if(0<=t.y&&t.y<100){var e=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);return e.setFullYear(t.y),e}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function eR(t){if(0<=t.y&&t.y<100){var e=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));return e.setUTCFullYear(t.y),e}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function ez(t,e,r){return{y:t,m:e,d:r,H:0,M:0,S:0,L:0}}var eU={"-":"",_:" ",0:"0"},e$=/^\s*\d+/,eF=/^%/,eZ=/[\\^$*+?|[\]().{}]/g;function eW(t,e,r){var n=t<0?"-":"",o=(n?-t:t)+"",i=o.length;return n+(i<r?Array(r-i+1).join(e)+o:o)}function eq(t){return t.replace(eZ,"\\$&")}function eY(t){return RegExp("^(?:"+t.map(eq).join("|")+")","i")}function eH(t){return new Map(t.map((t,e)=>[t.toLowerCase(),e]))}function eX(t,e,r){var n=e$.exec(e.slice(r,r+1));return n?(t.w=+n[0],r+n[0].length):-1}function eV(t,e,r){var n=e$.exec(e.slice(r,r+1));return n?(t.u=+n[0],r+n[0].length):-1}function eG(t,e,r){var n=e$.exec(e.slice(r,r+2));return n?(t.U=+n[0],r+n[0].length):-1}function eK(t,e,r){var n=e$.exec(e.slice(r,r+2));return n?(t.V=+n[0],r+n[0].length):-1}function eJ(t,e,r){var n=e$.exec(e.slice(r,r+2));return n?(t.W=+n[0],r+n[0].length):-1}function eQ(t,e,r){var n=e$.exec(e.slice(r,r+4));return n?(t.y=+n[0],r+n[0].length):-1}function e0(t,e,r){var n=e$.exec(e.slice(r,r+2));return n?(t.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function e1(t,e,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(e.slice(r,r+6));return n?(t.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function e2(t,e,r){var n=e$.exec(e.slice(r,r+1));return n?(t.q=3*n[0]-3,r+n[0].length):-1}function e3(t,e,r){var n=e$.exec(e.slice(r,r+2));return n?(t.m=n[0]-1,r+n[0].length):-1}function e6(t,e,r){var n=e$.exec(e.slice(r,r+2));return n?(t.d=+n[0],r+n[0].length):-1}function e4(t,e,r){var n=e$.exec(e.slice(r,r+3));return n?(t.m=0,t.d=+n[0],r+n[0].length):-1}function e5(t,e,r){var n=e$.exec(e.slice(r,r+2));return n?(t.H=+n[0],r+n[0].length):-1}function e7(t,e,r){var n=e$.exec(e.slice(r,r+2));return n?(t.M=+n[0],r+n[0].length):-1}function e8(t,e,r){var n=e$.exec(e.slice(r,r+2));return n?(t.S=+n[0],r+n[0].length):-1}function e9(t,e,r){var n=e$.exec(e.slice(r,r+3));return n?(t.L=+n[0],r+n[0].length):-1}function rt(t,e,r){var n=e$.exec(e.slice(r,r+6));return n?(t.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function re(t,e,r){var n=eF.exec(e.slice(r,r+1));return n?r+n[0].length:-1}function rr(t,e,r){var n=e$.exec(e.slice(r));return n?(t.Q=+n[0],r+n[0].length):-1}function rn(t,e,r){var n=e$.exec(e.slice(r));return n?(t.s=+n[0],r+n[0].length):-1}function ro(t,e){return eW(t.getDate(),e,2)}function ri(t,e){return eW(t.getHours(),e,2)}function ra(t,e){return eW(t.getHours()%12||12,e,2)}function ru(t,e){return eW(1+el.count(e_(t),t),e,3)}function rc(t,e){return eW(t.getMilliseconds(),e,3)}function rl(t,e){return rc(t,e)+"000"}function rs(t,e){return eW(t.getMonth()+1,e,2)}function rf(t,e){return eW(t.getMinutes(),e,2)}function rp(t,e){return eW(t.getSeconds(),e,2)}function rh(t){var e=t.getDay();return 0===e?7:e}function rd(t,e){return eW(eh.count(e_(t)-1,t),e,2)}function ry(t){var e=t.getDay();return e>=4||0===e?em(t):em.ceil(t)}function rv(t,e){return t=ry(t),eW(em.count(e_(t),t)+(4===e_(t).getDay()),e,2)}function rm(t){return t.getDay()}function rb(t,e){return eW(ed.count(e_(t)-1,t),e,2)}function rg(t,e){return eW(t.getFullYear()%100,e,2)}function rx(t,e){return eW((t=ry(t)).getFullYear()%100,e,2)}function rO(t,e){return eW(t.getFullYear()%1e4,e,4)}function rw(t,e){var r=t.getDay();return eW((t=r>=4||0===r?em(t):em.ceil(t)).getFullYear()%1e4,e,4)}function rj(t){var e=t.getTimezoneOffset();return(e>0?"-":(e*=-1,"+"))+eW(e/60|0,"0",2)+eW(e%60,"0",2)}function rS(t,e){return eW(t.getUTCDate(),e,2)}function rP(t,e){return eW(t.getUTCHours(),e,2)}function rA(t,e){return eW(t.getUTCHours()%12||12,e,2)}function rE(t,e){return eW(1+es.count(eT(t),t),e,3)}function rk(t,e){return eW(t.getUTCMilliseconds(),e,3)}function rM(t,e){return rk(t,e)+"000"}function r_(t,e){return eW(t.getUTCMonth()+1,e,2)}function rT(t,e){return eW(t.getUTCMinutes(),e,2)}function rC(t,e){return eW(t.getUTCSeconds(),e,2)}function rD(t){var e=t.getUTCDay();return 0===e?7:e}function rI(t,e){return eW(eO.count(eT(t)-1,t),e,2)}function rN(t){var e=t.getUTCDay();return e>=4||0===e?eP(t):eP.ceil(t)}function rB(t,e){return t=rN(t),eW(eP.count(eT(t),t)+(4===eT(t).getUTCDay()),e,2)}function rL(t){return t.getUTCDay()}function rR(t,e){return eW(ew.count(eT(t)-1,t),e,2)}function rz(t,e){return eW(t.getUTCFullYear()%100,e,2)}function rU(t,e){return eW((t=rN(t)).getUTCFullYear()%100,e,2)}function r$(t,e){return eW(t.getUTCFullYear()%1e4,e,4)}function rF(t,e){var r=t.getUTCDay();return eW((t=r>=4||0===r?eP(t):eP.ceil(t)).getUTCFullYear()%1e4,e,4)}function rZ(){return"+0000"}function rW(){return"%"}function rq(t){return+t}function rY(t){return Math.floor(+t/1e3)}function rH(t){return new Date(t)}function rX(t){return t instanceof Date?+t:+new Date(+t)}function rV(t,e,r,n,o,i,a,u,c,l){var s=tw(),f=s.invert,p=s.domain,h=l(".%L"),d=l(":%S"),y=l("%I:%M"),v=l("%I %p"),m=l("%a %d"),b=l("%b %d"),g=l("%B"),x=l("%Y");function O(t){return(c(t)<t?h:u(t)<t?d:a(t)<t?y:i(t)<t?v:n(t)<t?o(t)<t?m:b:r(t)<t?g:x)(t)}return s.invert=function(t){return new Date(f(t))},s.domain=function(t){return arguments.length?p(Array.from(t,rX)):p().map(rH)},s.ticks=function(e){var r=p();return t(r[0],r[r.length-1],null==e?10:e)},s.tickFormat=function(t,e){return null==e?O:l(e)},s.nice=function(t){var r=p();return t&&"function"==typeof t.range||(t=e(r[0],r[r.length-1],null==t?10:t)),t?p(tL(r,t)):s},s.copy=function(){return tx(s,rV(t,e,r,n,o,i,a,u,c,l))},s}function rG(){return tj.o.apply(rV(eN,eB,e_,ek,eh,el,eu,ei,eo,c).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function rK(){return tj.o.apply(rV(eD,eI,eT,eM,eO,es,ec,ea,eo,l).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function rJ(){var t,e,r,n,o,i=0,a=1,u=tv,c=!1;function l(e){return null==e||isNaN(e=+e)?o:u(0===r?.5:(e=(n(e)-t)*r,c?Math.max(0,Math.min(1,e)):e))}function s(t){return function(e){var r,n;return arguments.length?([r,n]=e,u=t(r,n),l):[u(0),u(1)]}}return l.domain=function(o){return arguments.length?([i,a]=o,t=n(i=+i),e=n(a=+a),r=t===e?0:1/(e-t),l):[i,a]},l.clamp=function(t){return arguments.length?(c=!!t,l):c},l.interpolator=function(t){return arguments.length?(u=t,l):u},l.range=s(tp),l.rangeRound=s(th),l.unknown=function(t){return arguments.length?(o=t,l):o},function(o){return n=o,t=o(i),e=o(a),r=t===e?0:1/(e-t),l}}function rQ(t,e){return e.domain(t.domain()).interpolator(t.interpolator()).clamp(t.clamp()).unknown(t.unknown())}function r0(){var t=tJ(rJ());return t.copy=function(){return rQ(t,r0()).exponent(t.exponent())},tj.O.apply(t,arguments)}function r1(){return r0.apply(null,arguments).exponent(.5)}function r2(){var t,e,r,n,o,i,a,u=0,c=.5,l=1,s=1,f=tv,p=!1;function h(t){return isNaN(t=+t)?a:(t=.5+((t=+i(t))-e)*(s*t<s*e?n:o),f(p?Math.max(0,Math.min(1,t)):t))}function d(t){return function(e){var r,n,o;return arguments.length?([r,n,o]=e,f=function(t,e){void 0===e&&(e=t,t=tp);for(var r=0,n=e.length-1,o=e[0],i=Array(n<0?0:n);r<n;)i[r]=t(o,o=e[++r]);return function(t){var e=Math.max(0,Math.min(n-1,Math.floor(t*=n)));return i[e](t-e)}}(t,[r,n,o]),h):[f(0),f(.5),f(1)]}}return h.domain=function(a){return arguments.length?([u,c,l]=a,t=i(u=+u),e=i(c=+c),r=i(l=+l),n=t===e?0:.5/(e-t),o=e===r?0:.5/(r-e),s=e<t?-1:1,h):[u,c,l]},h.clamp=function(t){return arguments.length?(p=!!t,h):p},h.interpolator=function(t){return arguments.length?(f=t,h):f},h.range=d(tp),h.rangeRound=d(th),h.unknown=function(t){return arguments.length?(a=t,h):a},function(a){return i=a,t=a(u),e=a(c),r=a(l),n=t===e?0:.5/(e-t),o=e===r?0:.5/(r-e),s=e<t?-1:1,h}}function r3(){var t=tJ(r2());return t.copy=function(){return rQ(t,r3()).exponent(t.exponent())},tj.O.apply(t,arguments)}function r6(){return r3.apply(null,arguments).exponent(.5)}function r4(t,e){if((o=t.length)>1)for(var r,n,o,i=1,a=t[e[0]],u=a.length;i<o;++i)for(n=a,a=t[e[i]],r=0;r<u;++r)a[r][1]+=a[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}c=(u=function(t){var e=t.dateTime,r=t.date,n=t.time,o=t.periods,i=t.days,a=t.shortDays,u=t.months,c=t.shortMonths,l=eY(o),s=eH(o),f=eY(i),p=eH(i),h=eY(a),d=eH(a),y=eY(u),v=eH(u),m=eY(c),b=eH(c),g={a:function(t){return a[t.getDay()]},A:function(t){return i[t.getDay()]},b:function(t){return c[t.getMonth()]},B:function(t){return u[t.getMonth()]},c:null,d:ro,e:ro,f:rl,g:rx,G:rw,H:ri,I:ra,j:ru,L:rc,m:rs,M:rf,p:function(t){return o[+(t.getHours()>=12)]},q:function(t){return 1+~~(t.getMonth()/3)},Q:rq,s:rY,S:rp,u:rh,U:rd,V:rv,w:rm,W:rb,x:null,X:null,y:rg,Y:rO,Z:rj,"%":rW},x={a:function(t){return a[t.getUTCDay()]},A:function(t){return i[t.getUTCDay()]},b:function(t){return c[t.getUTCMonth()]},B:function(t){return u[t.getUTCMonth()]},c:null,d:rS,e:rS,f:rM,g:rU,G:rF,H:rP,I:rA,j:rE,L:rk,m:r_,M:rT,p:function(t){return o[+(t.getUTCHours()>=12)]},q:function(t){return 1+~~(t.getUTCMonth()/3)},Q:rq,s:rY,S:rC,u:rD,U:rI,V:rB,w:rL,W:rR,x:null,X:null,y:rz,Y:r$,Z:rZ,"%":rW},O={a:function(t,e,r){var n=h.exec(e.slice(r));return n?(t.w=d.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(t,e,r){var n=f.exec(e.slice(r));return n?(t.w=p.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(t,e,r){var n=m.exec(e.slice(r));return n?(t.m=b.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(t,e,r){var n=y.exec(e.slice(r));return n?(t.m=v.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(t,r,n){return S(t,e,r,n)},d:e6,e:e6,f:rt,g:e0,G:eQ,H:e5,I:e5,j:e4,L:e9,m:e3,M:e7,p:function(t,e,r){var n=l.exec(e.slice(r));return n?(t.p=s.get(n[0].toLowerCase()),r+n[0].length):-1},q:e2,Q:rr,s:rn,S:e8,u:eV,U:eG,V:eK,w:eX,W:eJ,x:function(t,e,n){return S(t,r,e,n)},X:function(t,e,r){return S(t,n,e,r)},y:e0,Y:eQ,Z:e1,"%":re};function w(t,e){return function(r){var n,o,i,a=[],u=-1,c=0,l=t.length;for(r instanceof Date||(r=new Date(+r));++u<l;)37===t.charCodeAt(u)&&(a.push(t.slice(c,u)),null!=(o=eU[n=t.charAt(++u)])?n=t.charAt(++u):o="e"===n?" ":"0",(i=e[n])&&(n=i(r,o)),a.push(n),c=u+1);return a.push(t.slice(c,u)),a.join("")}}function j(t,e){return function(r){var n,o,i=ez(1900,void 0,1);if(S(i,t,r+="",0)!=r.length)return null;if("Q"in i)return new Date(i.Q);if("s"in i)return new Date(1e3*i.s+("L"in i?i.L:0));if(!e||"Z"in i||(i.Z=0),"p"in i&&(i.H=i.H%12+12*i.p),void 0===i.m&&(i.m="q"in i?i.q:0),"V"in i){if(i.V<1||i.V>53)return null;"w"in i||(i.w=1),"Z"in i?(n=(o=(n=eR(ez(i.y,0,1))).getUTCDay())>4||0===o?ew.ceil(n):ew(n),n=es.offset(n,(i.V-1)*7),i.y=n.getUTCFullYear(),i.m=n.getUTCMonth(),i.d=n.getUTCDate()+(i.w+6)%7):(n=(o=(n=eL(ez(i.y,0,1))).getDay())>4||0===o?ed.ceil(n):ed(n),n=el.offset(n,(i.V-1)*7),i.y=n.getFullYear(),i.m=n.getMonth(),i.d=n.getDate()+(i.w+6)%7)}else("W"in i||"U"in i)&&("w"in i||(i.w="u"in i?i.u%7:"W"in i?1:0),o="Z"in i?eR(ez(i.y,0,1)).getUTCDay():eL(ez(i.y,0,1)).getDay(),i.m=0,i.d="W"in i?(i.w+6)%7+7*i.W-(o+5)%7:i.w+7*i.U-(o+6)%7);return"Z"in i?(i.H+=i.Z/100|0,i.M+=i.Z%100,eR(i)):eL(i)}}function S(t,e,r,n){for(var o,i,a=0,u=e.length,c=r.length;a<u;){if(n>=c)return -1;if(37===(o=e.charCodeAt(a++))){if(!(i=O[(o=e.charAt(a++))in eU?e.charAt(a++):o])||(n=i(t,r,n))<0)return -1}else if(o!=r.charCodeAt(n++))return -1}return n}return g.x=w(r,g),g.X=w(n,g),g.c=w(e,g),x.x=w(r,x),x.X=w(n,x),x.c=w(e,x),{format:function(t){var e=w(t+="",g);return e.toString=function(){return t},e},parse:function(t){var e=j(t+="",!1);return e.toString=function(){return t},e},utcFormat:function(t){var e=w(t+="",x);return e.toString=function(){return t},e},utcParse:function(t){var e=j(t+="",!0);return e.toString=function(){return t},e}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,u.parse,l=u.utcFormat,u.utcParse;var r5=r(90418),r7=r(47643);function r8(t){for(var e=t.length,r=Array(e);--e>=0;)r[e]=e;return r}function r9(t,e){return t[e]}function nt(t){let e=[];return e.key=t,e}var ne=r(52136),nr=r.n(ne),nn=r(83321),no=r.n(nn),ni=r(66952),na=r.n(ni),nu=r(39390),nc=r.n(nu),nl=r(77605),ns=r.n(nl),nf=r(1995),np=r.n(nf),nh=r(26286),nd=r.n(nh),ny=r(26398),nv=r.n(ny),nm=r(21774),nb=r.n(nm),ng=r(62927),nx=r.n(ng),nO=r(64677),nw=r.n(nO),nj=r(4748),nS=r.n(nj);function nP(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var nA=function(t){return t},nE={},nk=function(t){return t===nE},nM=function(t){return function e(){return 0==arguments.length||1==arguments.length&&nk(arguments.length<=0?void 0:arguments[0])?e:t.apply(void 0,arguments)}},n_=function(t){return function t(e,r){return 1===e?r:nM(function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];var a=o.filter(function(t){return t!==nE}).length;return a>=e?r.apply(void 0,o):t(e-a,nM(function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];var i=o.map(function(t){return nk(t)?e.shift():t});return r.apply(void 0,((function(t){if(Array.isArray(t))return nP(t)})(i)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(i)||function(t,e){if(t){if("string"==typeof t)return nP(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nP(t,e)}}(i)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()).concat(e))}))})}(t.length,t)},nT=function(t,e){for(var r=[],n=t;n<e;++n)r[n-t]=n;return r},nC=n_(function(t,e){return Array.isArray(e)?e.map(t):Object.keys(e).map(function(t){return e[t]}).map(t)}),nD=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];if(!e.length)return nA;var n=e.reverse(),o=n[0],i=n.slice(1);return function(){return i.reduce(function(t,e){return e(t)},o.apply(void 0,arguments))}},nI=function(t){return Array.isArray(t)?t.reverse():t.split("").reverse.join("")},nN=function(t){var e=null,r=null;return function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];return e&&o.every(function(t,r){return t===e[r]})?r:(e=o,r=t.apply(void 0,o))}},nB={rangeStep:function(t,e,r){for(var n=new(nS())(t),o=0,i=[];n.lt(e)&&o<1e5;)i.push(n.toNumber()),n=n.add(r),o++;return i},getDigitCount:function(t){return 0===t?1:Math.floor(new(nS())(t).abs().log(10).toNumber())+1},interpolateNumber:n_(function(t,e,r){var n=+t;return n+r*(+e-n)}),uninterpolateNumber:n_(function(t,e,r){var n=e-+t;return(r-t)/(n=n||1/0)}),uninterpolateTruncation:n_(function(t,e,r){var n=e-+t;return Math.max(0,Math.min(1,(r-t)/(n=n||1/0)))})};function nL(t){return function(t){if(Array.isArray(t))return nU(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||nz(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nR(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t)){var r=[],n=!0,o=!1,i=void 0;try{for(var a,u=t[Symbol.iterator]();!(n=(a=u.next()).done)&&(r.push(a.value),!e||r.length!==e);n=!0);}catch(t){o=!0,i=t}finally{try{n||null==u.return||u.return()}finally{if(o)throw i}}return r}}(t,e)||nz(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nz(t,e){if(t){if("string"==typeof t)return nU(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nU(t,e)}}function nU(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function n$(t){var e=nR(t,2),r=e[0],n=e[1],o=r,i=n;return r>n&&(o=n,i=r),[o,i]}function nF(t,e,r){if(t.lte(0))return new(nS())(0);var n=nB.getDigitCount(t.toNumber()),o=new(nS())(10).pow(n),i=t.div(o),a=1!==n?.05:.1,u=new(nS())(Math.ceil(i.div(a).toNumber())).add(r).mul(a).mul(o);return e?u:new(nS())(Math.ceil(u))}function nZ(t,e,r){var n=1,o=new(nS())(t);if(!o.isint()&&r){var i=Math.abs(t);i<1?(n=new(nS())(10).pow(nB.getDigitCount(t)-1),o=new(nS())(Math.floor(o.div(n).toNumber())).mul(n)):i>1&&(o=new(nS())(Math.floor(t)))}else 0===t?o=new(nS())(Math.floor((e-1)/2)):r||(o=new(nS())(Math.floor(t)));var a=Math.floor((e-1)/2);return nD(nC(function(t){return o.add(new(nS())(t-a).mul(n)).toNumber()}),nT)(0,e)}var nW=nN(function(t){var e=nR(t,2),r=e[0],n=e[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(o,2),u=nR(n$([r,n]),2),c=u[0],l=u[1];if(c===-1/0||l===1/0){var s=l===1/0?[c].concat(nL(nT(0,o-1).map(function(){return 1/0}))):[].concat(nL(nT(0,o-1).map(function(){return-1/0})),[l]);return r>n?nI(s):s}if(c===l)return nZ(c,o,i);var f=function t(e,r,n,o){var i,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((r-e)/(n-1)))return{step:new(nS())(0),tickMin:new(nS())(0),tickMax:new(nS())(0)};var u=nF(new(nS())(r).sub(e).div(n-1),o,a),c=Math.ceil((i=e<=0&&r>=0?new(nS())(0):(i=new(nS())(e).add(r).div(2)).sub(new(nS())(i).mod(u))).sub(e).div(u).toNumber()),l=Math.ceil(new(nS())(r).sub(i).div(u).toNumber()),s=c+l+1;return s>n?t(e,r,n,o,a+1):(s<n&&(l=r>0?l+(n-s):l,c=r>0?c:c+(n-s)),{step:u,tickMin:i.sub(new(nS())(c).mul(u)),tickMax:i.add(new(nS())(l).mul(u))})}(c,l,a,i),p=f.step,h=f.tickMin,d=f.tickMax,y=nB.rangeStep(h,d.add(new(nS())(.1).mul(p)),p);return r>n?nI(y):y});nN(function(t){var e=nR(t,2),r=e[0],n=e[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(o,2),u=nR(n$([r,n]),2),c=u[0],l=u[1];if(c===-1/0||l===1/0)return[r,n];if(c===l)return nZ(c,o,i);var s=nF(new(nS())(l).sub(c).div(a-1),i,0),f=nD(nC(function(t){return new(nS())(c).add(new(nS())(t).mul(s)).toNumber()}),nT)(0,a).filter(function(t){return t>=c&&t<=l});return r>n?nI(f):f});var nq=nN(function(t,e){var r=nR(t,2),n=r[0],o=r[1],i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=nR(n$([n,o]),2),u=a[0],c=a[1];if(u===-1/0||c===1/0)return[n,o];if(u===c)return[u];var l=nF(new(nS())(c).sub(u).div(Math.max(e,2)-1),i,0),s=[].concat(nL(nB.rangeStep(new(nS())(u),new(nS())(c).sub(new(nS())(.99).mul(l)),l)),[c]);return n>o?nI(s):s}),nY=r(42494),nH=r(97281),nX=r(43843),nV=r(9776);function nG(t){return(nG="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function nK(t){return function(t){if(Array.isArray(t))return nJ(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return nJ(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nJ(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nJ(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function nQ(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function n0(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?nQ(Object(r),!0).forEach(function(e){n1(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):nQ(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function n1(t,e,r){var n;return(n=function(t,e){if("object"!=nG(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=nG(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==nG(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function n2(t,e,r){return na()(t)||na()(e)?r:(0,nH.P2)(e)?np()(t,e,r):nc()(e)?e(t):r}function n3(t,e,r,n){var o=nd()(t,function(t){return n2(t,e)});if("number"===r){var i=o.filter(function(t){return(0,nH.hj)(t)||parseFloat(t)});return i.length?[no()(i),nr()(i)]:[1/0,-1/0]}return(n?o.filter(function(t){return!na()(t)}):o).map(function(t){return(0,nH.P2)(t)||t instanceof Date?t:""})}var n6=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2?arguments[2]:void 0,o=arguments.length>3?arguments[3]:void 0,i=-1,a=null!==(e=null==r?void 0:r.length)&&void 0!==e?e:0;if(a<=1)return 0;if(o&&"angleAxis"===o.axisType&&1e-6>=Math.abs(Math.abs(o.range[1]-o.range[0])-360))for(var u=o.range,c=0;c<a;c++){var l=c>0?n[c-1].coordinate:n[a-1].coordinate,s=n[c].coordinate,f=c>=a-1?n[0].coordinate:n[c+1].coordinate,p=void 0;if((0,nH.uY)(s-l)!==(0,nH.uY)(f-s)){var h=[];if((0,nH.uY)(f-s)===(0,nH.uY)(u[1]-u[0])){p=f;var d=s+u[1]-u[0];h[0]=Math.min(d,(d+l)/2),h[1]=Math.max(d,(d+l)/2)}else{p=l;var y=f+u[1]-u[0];h[0]=Math.min(s,(y+s)/2),h[1]=Math.max(s,(y+s)/2)}var v=[Math.min(s,(p+s)/2),Math.max(s,(p+s)/2)];if(t>v[0]&&t<=v[1]||t>=h[0]&&t<=h[1]){i=n[c].index;break}}else{var m=Math.min(l,f),b=Math.max(l,f);if(t>(m+s)/2&&t<=(b+s)/2){i=n[c].index;break}}}else for(var g=0;g<a;g++)if(0===g&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g>0&&g<a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g===a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2){i=r[g].index;break}return i},n4=function(t){var e,r,n=t.type.displayName,o=null!==(e=t.type)&&void 0!==e&&e.defaultProps?n0(n0({},t.type.defaultProps),t.props):t.props,i=o.stroke,a=o.fill;switch(n){case"Line":r=i;break;case"Area":case"Radar":r=i&&"none"!==i?i:a;break;default:r=a}return r},n5=function(t){var e=t.barSize,r=t.totalSize,n=t.stackGroups,o=void 0===n?{}:n;if(!o)return{};for(var i={},a=Object.keys(o),u=0,c=a.length;u<c;u++)for(var l=o[a[u]].stackGroups,s=Object.keys(l),f=0,p=s.length;f<p;f++){var h=l[s[f]],d=h.items,y=h.cateAxisId,v=d.filter(function(t){return(0,nX.Gf)(t.type).indexOf("Bar")>=0});if(v&&v.length){var m=v[0].type.defaultProps,b=void 0!==m?n0(n0({},m),v[0].props):v[0].props,g=b.barSize,x=b[y];i[x]||(i[x]=[]);var O=na()(g)?e:g;i[x].push({item:v[0],stackList:v.slice(1),barSize:na()(O)?void 0:(0,nH.h1)(O,r,0)})}}return i},n7=function(t){var e,r=t.barGap,n=t.barCategoryGap,o=t.bandSize,i=t.sizeList,a=void 0===i?[]:i,u=t.maxBarSize,c=a.length;if(c<1)return null;var l=(0,nH.h1)(r,o,0,!0),s=[];if(a[0].barSize===+a[0].barSize){var f=!1,p=o/c,h=a.reduce(function(t,e){return t+e.barSize||0},0);(h+=(c-1)*l)>=o&&(h-=(c-1)*l,l=0),h>=o&&p>0&&(f=!0,p*=.9,h=c*p);var d={offset:((o-h)/2>>0)-l,size:0};e=a.reduce(function(t,e){var r={item:e.item,position:{offset:d.offset+d.size+l,size:f?p:e.barSize}},n=[].concat(nK(t),[r]);return d=n[n.length-1].position,e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){n.push({item:t,position:d})}),n},s)}else{var y=(0,nH.h1)(n,o,0,!0);o-2*y-(c-1)*l<=0&&(l=0);var v=(o-2*y-(c-1)*l)/c;v>1&&(v>>=0);var m=u===+u?Math.min(v,u):v;e=a.reduce(function(t,e,r){var n=[].concat(nK(t),[{item:e.item,position:{offset:y+(v+l)*r+(v-m)/2,size:m}}]);return e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){n.push({item:t,position:n[n.length-1].position})}),n},s)}return e},n8=function(t,e,r,n){var o=r.children,i=r.width,a=r.margin,u=i-(a.left||0)-(a.right||0),c=(0,nV.z)({children:o,legendWidth:u});if(c){var l=n||{},s=l.width,f=l.height,p=c.align,h=c.verticalAlign,d=c.layout;if(("vertical"===d||"horizontal"===d&&"middle"===h)&&"center"!==p&&(0,nH.hj)(t[p]))return n0(n0({},t),{},n1({},p,t[p]+(s||0)));if(("horizontal"===d||"vertical"===d&&"center"===p)&&"middle"!==h&&(0,nH.hj)(t[h]))return n0(n0({},t),{},n1({},h,t[h]+(f||0)))}return t},n9=function(t,e,r,n,o){var i=e.props.children,a=(0,nX.NN)(i,nY.W).filter(function(t){var e;return e=t.props.direction,!!na()(o)||("horizontal"===n?"yAxis"===o:"vertical"===n||"x"===e?"xAxis"===o:"y"!==e||"yAxis"===o)});if(a&&a.length){var u=a.map(function(t){return t.props.dataKey});return t.reduce(function(t,e){var n=n2(e,r);if(na()(n))return t;var o=Array.isArray(n)?[no()(n),nr()(n)]:[n,n],i=u.reduce(function(t,r){var n=n2(e,r,0),i=o[0]-Math.abs(Array.isArray(n)?n[0]:n),a=o[1]+Math.abs(Array.isArray(n)?n[1]:n);return[Math.min(i,t[0]),Math.max(a,t[1])]},[1/0,-1/0]);return[Math.min(i[0],t[0]),Math.max(i[1],t[1])]},[1/0,-1/0])}return null},ot=function(t,e,r,n,o){var i=e.map(function(e){return n9(t,e,r,o,n)}).filter(function(t){return!na()(t)});return i&&i.length?i.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]):null},oe=function(t,e,r,n,o){var i=e.map(function(e){var i=e.props.dataKey;return"number"===r&&i&&n9(t,e,i,n)||n3(t,i,r,o)});if("number"===r)return i.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]);var a={};return i.reduce(function(t,e){for(var r=0,n=e.length;r<n;r++)a[e[r]]||(a[e[r]]=!0,t.push(e[r]));return t},[])},or=function(t,e){return"horizontal"===t&&"xAxis"===e||"vertical"===t&&"yAxis"===e||"centric"===t&&"angleAxis"===e||"radial"===t&&"radiusAxis"===e},on=function(t,e,r,n){if(n)return t.map(function(t){return t.coordinate});var o,i,a=t.map(function(t){return t.coordinate===e&&(o=!0),t.coordinate===r&&(i=!0),t.coordinate});return o||a.push(e),i||a.push(r),a},oo=function(t,e,r){if(!t)return null;var n=t.scale,o=t.duplicateDomain,i=t.type,a=t.range,u="scaleBand"===t.realScaleType?n.bandwidth()/2:2,c=(e||r)&&"category"===i&&n.bandwidth?n.bandwidth()/u:0;return(c="angleAxis"===t.axisType&&(null==a?void 0:a.length)>=2?2*(0,nH.uY)(a[0]-a[1])*c:c,e&&(t.ticks||t.niceTicks))?(t.ticks||t.niceTicks).map(function(t){return{coordinate:n(o?o.indexOf(t):t)+c,value:t,offset:c}}).filter(function(t){return!nv()(t.coordinate)}):t.isCategorical&&t.categoricalDomain?t.categoricalDomain.map(function(t,e){return{coordinate:n(t)+c,value:t,index:e,offset:c}}):n.ticks&&!r?n.ticks(t.tickCount).map(function(t){return{coordinate:n(t)+c,value:t,offset:c}}):n.domain().map(function(t,e){return{coordinate:n(t)+c,value:o?o[t]:t,index:e,offset:c}})},oi=new WeakMap,oa=function(t,e){if("function"!=typeof e)return t;oi.has(t)||oi.set(t,new WeakMap);var r=oi.get(t);if(r.has(e))return r.get(e);var n=function(){t.apply(void 0,arguments),e.apply(void 0,arguments)};return r.set(e,n),n},ou=function(t,e,r){var n=t.scale,o=t.type,i=t.layout,a=t.axisType;if("auto"===n)return"radial"===i&&"radiusAxis"===a?{scale:f.Z(),realScaleType:"band"}:"radial"===i&&"angleAxis"===a?{scale:tB(),realScaleType:"linear"}:"category"===o&&e&&(e.indexOf("LineChart")>=0||e.indexOf("AreaChart")>=0||e.indexOf("ComposedChart")>=0&&!r)?{scale:f.x(),realScaleType:"point"}:"category"===o?{scale:f.Z(),realScaleType:"band"}:{scale:tB(),realScaleType:"linear"};if(ns()(n)){var u="scale".concat(nb()(n));return{scale:(s[u]||f.x)(),realScaleType:s[u]?u:"point"}}return nc()(n)?{scale:n}:{scale:f.x(),realScaleType:"point"}},oc=function(t){var e=t.domain();if(e&&!(e.length<=2)){var r=e.length,n=t.range(),o=Math.min(n[0],n[1])-1e-4,i=Math.max(n[0],n[1])+1e-4,a=t(e[0]),u=t(e[r-1]);(a<o||a>i||u<o||u>i)&&t.domain([e[0],e[r-1]])}},ol=function(t,e){if(!t)return null;for(var r=0,n=t.length;r<n;r++)if(t[r].item===e)return t[r].position;return null},os=function(t,e){if(!e||2!==e.length||!(0,nH.hj)(e[0])||!(0,nH.hj)(e[1]))return t;var r=Math.min(e[0],e[1]),n=Math.max(e[0],e[1]),o=[t[0],t[1]];return(!(0,nH.hj)(t[0])||t[0]<r)&&(o[0]=r),(!(0,nH.hj)(t[1])||t[1]>n)&&(o[1]=n),o[0]>n&&(o[0]=n),o[1]<r&&(o[1]=r),o},of={sign:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var o=0,i=0,a=0;a<e;++a){var u=nv()(t[a][r][1])?t[a][r][0]:t[a][r][1];u>=0?(t[a][r][0]=o,t[a][r][1]=o+u,o=t[a][r][1]):(t[a][r][0]=i,t[a][r][1]=i+u,i=t[a][r][1])}},expand:function(t,e){if((n=t.length)>0){for(var r,n,o,i=0,a=t[0].length;i<a;++i){for(o=r=0;r<n;++r)o+=t[r][i][1]||0;if(o)for(r=0;r<n;++r)t[r][i][1]/=o}r4(t,e)}},none:r4,silhouette:function(t,e){if((r=t.length)>0){for(var r,n=0,o=t[e[0]],i=o.length;n<i;++n){for(var a=0,u=0;a<r;++a)u+=t[a][n][1]||0;o[n][1]+=o[n][0]=-u/2}r4(t,e)}},wiggle:function(t,e){if((o=t.length)>0&&(n=(r=t[e[0]]).length)>0){for(var r,n,o,i=0,a=1;a<n;++a){for(var u=0,c=0,l=0;u<o;++u){for(var s=t[e[u]],f=s[a][1]||0,p=(f-(s[a-1][1]||0))/2,h=0;h<u;++h){var d=t[e[h]];p+=(d[a][1]||0)-(d[a-1][1]||0)}c+=f,l+=p*f}r[a-1][1]+=r[a-1][0]=i,c&&(i-=l/c)}r[a-1][1]+=r[a-1][0]=i,r4(t,e)}},positive:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var o=0,i=0;i<e;++i){var a=nv()(t[i][r][1])?t[i][r][0]:t[i][r][1];a>=0?(t[i][r][0]=o,t[i][r][1]=o+a,o=t[i][r][1]):(t[i][r][0]=0,t[i][r][1]=0)}}},op=function(t,e,r){var n=e.map(function(t){return t.props.dataKey}),o=of[r];return(function(){var t=(0,r7.Z)([]),e=r8,r=r4,n=r9;function o(o){var i,a,u=Array.from(t.apply(this,arguments),nt),c=u.length,l=-1;for(let t of o)for(i=0,++l;i<c;++i)(u[i][l]=[0,+n(t,u[i].key,l,o)]).data=t;for(i=0,a=(0,r5.Z)(e(u));i<c;++i)u[a[i]].index=i;return r(u,a),u}return o.keys=function(e){return arguments.length?(t="function"==typeof e?e:(0,r7.Z)(Array.from(e)),o):t},o.value=function(t){return arguments.length?(n="function"==typeof t?t:(0,r7.Z)(+t),o):n},o.order=function(t){return arguments.length?(e=null==t?r8:"function"==typeof t?t:(0,r7.Z)(Array.from(t)),o):e},o.offset=function(t){return arguments.length?(r=null==t?r4:t,o):r},o})().keys(n).value(function(t,e){return+n2(t,e,0)}).order(r8).offset(o)(t)},oh=function(t,e,r,n,o,i){if(!t)return null;var a=(i?e.reverse():e).reduce(function(t,e){var o,i=null!==(o=e.type)&&void 0!==o&&o.defaultProps?n0(n0({},e.type.defaultProps),e.props):e.props,a=i.stackId;if(i.hide)return t;var u=i[r],c=t[u]||{hasStack:!1,stackGroups:{}};if((0,nH.P2)(a)){var l=c.stackGroups[a]||{numericAxisId:r,cateAxisId:n,items:[]};l.items.push(e),c.hasStack=!0,c.stackGroups[a]=l}else c.stackGroups[(0,nH.EL)("_stackId_")]={numericAxisId:r,cateAxisId:n,items:[e]};return n0(n0({},t),{},n1({},u,c))},{});return Object.keys(a).reduce(function(e,i){var u=a[i];return u.hasStack&&(u.stackGroups=Object.keys(u.stackGroups).reduce(function(e,i){var a=u.stackGroups[i];return n0(n0({},e),{},n1({},i,{numericAxisId:r,cateAxisId:n,items:a.items,stackedData:op(t,a.items,o)}))},{})),n0(n0({},e),{},n1({},i,u))},{})},od=function(t,e){var r=e.realScaleType,n=e.type,o=e.tickCount,i=e.originalDomain,a=e.allowDecimals,u=r||e.scale;if("auto"!==u&&"linear"!==u)return null;if(o&&"number"===n&&i&&("auto"===i[0]||"auto"===i[1])){var c=t.domain();if(!c.length)return null;var l=nW(c,o,a);return t.domain([no()(l),nr()(l)]),{niceTicks:l}}return o&&"number"===n?{niceTicks:nq(t.domain(),o,a)}:null};function oy(t){var e=t.axis,r=t.ticks,n=t.bandSize,o=t.entry,i=t.index,a=t.dataKey;if("category"===e.type){if(!e.allowDuplicatedCategory&&e.dataKey&&!na()(o[e.dataKey])){var u=(0,nH.Ap)(r,"value",o[e.dataKey]);if(u)return u.coordinate+n/2}return r[i]?r[i].coordinate+n/2:null}var c=n2(o,na()(a)?e.dataKey:a);return na()(c)?null:e.scale(c)}var ov=function(t){var e=t.axis,r=t.ticks,n=t.offset,o=t.bandSize,i=t.entry,a=t.index;if("category"===e.type)return r[a]?r[a].coordinate+n:null;var u=n2(i,e.dataKey,e.domain[a]);return na()(u)?null:e.scale(u)-o/2+n},om=function(t){var e=t.numericAxis,r=e.scale.domain();if("number"===e.type){var n=Math.min(r[0],r[1]),o=Math.max(r[0],r[1]);return n<=0&&o>=0?0:o<0?o:n}return r[0]},ob=function(t,e){var r,n=(null!==(r=t.type)&&void 0!==r&&r.defaultProps?n0(n0({},t.type.defaultProps),t.props):t.props).stackId;if((0,nH.P2)(n)){var o=e[n];if(o){var i=o.items.indexOf(t);return i>=0?o.stackedData[i]:null}}return null},og=function(t,e,r){return Object.keys(t).reduce(function(n,o){var i=t[o].stackedData.reduce(function(t,n){var o=n.slice(e,r+1).reduce(function(t,e){return[no()(e.concat([t[0]]).filter(nH.hj)),nr()(e.concat([t[1]]).filter(nH.hj))]},[1/0,-1/0]);return[Math.min(t[0],o[0]),Math.max(t[1],o[1])]},[1/0,-1/0]);return[Math.min(i[0],n[0]),Math.max(i[1],n[1])]},[1/0,-1/0]).map(function(t){return t===1/0||t===-1/0?0:t})},ox=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,oO=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,ow=function(t,e,r){if(nc()(t))return t(e,r);if(!Array.isArray(t))return e;var n=[];if((0,nH.hj)(t[0]))n[0]=r?t[0]:Math.min(t[0],e[0]);else if(ox.test(t[0])){var o=+ox.exec(t[0])[1];n[0]=e[0]-o}else nc()(t[0])?n[0]=t[0](e[0]):n[0]=e[0];if((0,nH.hj)(t[1]))n[1]=r?t[1]:Math.max(t[1],e[1]);else if(oO.test(t[1])){var i=+oO.exec(t[1])[1];n[1]=e[1]+i}else nc()(t[1])?n[1]=t[1](e[1]):n[1]=e[1];return n},oj=function(t,e,r){if(t&&t.scale&&t.scale.bandwidth){var n=t.scale.bandwidth();if(!r||n>0)return n}if(t&&e&&e.length>=2){for(var o=nw()(e,function(t){return t.coordinate}),i=1/0,a=1,u=o.length;a<u;a++){var c=o[a],l=o[a-1];i=Math.min((c.coordinate||0)-(l.coordinate||0),i)}return i===1/0?0:i}return r?void 0:0},oS=function(t,e,r){return!t||!t.length||nx()(t,np()(r,"type.defaultProps.domain"))?e:t},oP=function(t,e){var r=t.type.defaultProps?n0(n0({},t.type.defaultProps),t.props):t.props,n=r.dataKey,o=r.name,i=r.unit,a=r.formatter,u=r.tooltipType,c=r.chartType,l=r.hide;return n0(n0({},(0,nX.L6)(t,!1)),{},{dataKey:n,unit:i,formatter:a,name:o||n,color:n4(t),value:n2(e,n),type:u,payload:e,chartType:c,hide:l})}},54768:function(t,e,r){"use strict";r.d(e,{os:function(){return f},xE:function(){return s}});var n=r(3841);function o(t){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function i(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function a(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?i(Object(r),!0).forEach(function(e){var n,i;n=e,i=r[e],(n=function(t){var e=function(t,e){if("object"!=o(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=o(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==o(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var u={widthCache:{},cacheCount:0},c={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},l="recharts_measurement_span",s=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==t||n.x.isSsr)return{width:0,height:0};var o=(Object.keys(e=a({},r)).forEach(function(t){e[t]||delete e[t]}),e),i=JSON.stringify({text:t,copyStyle:o});if(u.widthCache[i])return u.widthCache[i];try{var s=document.getElementById(l);s||((s=document.createElement("span")).setAttribute("id",l),s.setAttribute("aria-hidden","true"),document.body.appendChild(s));var f=a(a({},c),o);Object.assign(s.style,f),s.textContent="".concat(t);var p=s.getBoundingClientRect(),h={width:p.width,height:p.height};return u.widthCache[i]=h,++u.cacheCount>2e3&&(u.cacheCount=0,u.widthCache={}),h}catch(t){return{width:0,height:0}}},f=function(t){return{top:t.top+window.scrollY-document.documentElement.clientTop,left:t.left+window.scrollX-document.documentElement.clientLeft}}},97281:function(t,e,r){"use strict";r.d(e,{Ap:function(){return O},EL:function(){return v},Kt:function(){return b},P2:function(){return d},bv:function(){return g},fC:function(){return w},h1:function(){return m},hU:function(){return p},hj:function(){return h},k4:function(){return x},uY:function(){return f}});var n=r(77605),o=r.n(n),i=r(26398),a=r.n(i),u=r(1995),c=r.n(u),l=r(44968),s=r.n(l),f=function(t){return 0===t?0:t>0?1:-1},p=function(t){return o()(t)&&t.indexOf("%")===t.length-1},h=function(t){return s()(t)&&!a()(t)},d=function(t){return h(t)||o()(t)},y=0,v=function(t){var e=++y;return"".concat(t||"").concat(e)},m=function(t,e){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!h(t)&&!o()(t))return n;if(p(t)){var u=t.indexOf("%");r=e*parseFloat(t.slice(0,u))/100}else r=+t;return a()(r)&&(r=n),i&&r>e&&(r=e),r},b=function(t){if(!t)return null;var e=Object.keys(t);return e&&e.length?t[e[0]]:null},g=function(t){if(!Array.isArray(t))return!1;for(var e=t.length,r={},n=0;n<e;n++){if(r[t[n]])return!0;r[t[n]]=!0}return!1},x=function(t,e){return h(t)&&h(e)?function(r){return t+r*(e-t)}:function(){return e}};function O(t,e,r){return t&&t.length?t.find(function(t){return t&&("function"==typeof e?e(t):c()(t,e))===r}):null}var w=function(t,e){return h(t)&&h(e)?t-e:o()(t)&&o()(e)?t.localeCompare(e):t instanceof Date&&e instanceof Date?t.getTime()-e.getTime():String(t).localeCompare(String(e))}},3841:function(t,e,r){"use strict";r.d(e,{x:function(){return n}});var n={isSsr:!("undefined"!=typeof window&&window.document&&window.document.createElement&&window.setTimeout),get:function(t){return n[t]},set:function(t,e){if("string"==typeof t)n[t]=e;else{var r=Object.keys(t);r&&r.length&&r.forEach(function(e){n[e]=t[e]})}}}},47205:function(t,e,r){"use strict";r.d(e,{Z:function(){return n}});var n=function(t,e){for(var r=arguments.length,n=Array(r>2?r-2:0),o=2;o<r;o++)n[o-2]=arguments[o]}},56120:function(t,e,r){"use strict";r.d(e,{$4:function(){return m},$S:function(){return j},Wk:function(){return y},op:function(){return v},t9:function(){return b},z3:function(){return w}});var n=r(66952),o=r.n(n),i=r(2265),a=r(39390),u=r.n(a),c=r(97281),l=r(41586);function s(t){return(s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function f(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function p(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?f(Object(r),!0).forEach(function(e){h(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function h(t,e,r){var n;return(n=function(t,e){if("object"!=s(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=s(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==s(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function d(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var y=Math.PI/180,v=function(t,e,r,n){return{x:t+Math.cos(-y*n)*r,y:e+Math.sin(-y*n)*r}},m=function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0};return Math.min(Math.abs(t-(r.left||0)-(r.right||0)),Math.abs(e-(r.top||0)-(r.bottom||0)))/2},b=function(t,e,r,n,i){var a=t.width,u=t.height,s=t.startAngle,f=t.endAngle,y=(0,c.h1)(t.cx,a,a/2),v=(0,c.h1)(t.cy,u,u/2),b=m(a,u,r),g=(0,c.h1)(t.innerRadius,b,0),x=(0,c.h1)(t.outerRadius,b,.8*b);return Object.keys(e).reduce(function(t,r){var a,u=e[r],c=u.domain,m=u.reversed;if(o()(u.range))"angleAxis"===n?a=[s,f]:"radiusAxis"===n&&(a=[g,x]),m&&(a=[a[1],a[0]]);else{var b,O=function(t){if(Array.isArray(t))return t}(b=a=u.range)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(b,2)||function(t,e){if(t){if("string"==typeof t)return d(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return d(t,e)}}(b,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();s=O[0],f=O[1]}var w=(0,l.Hq)(u,i),j=w.realScaleType,S=w.scale;S.domain(c).range(a),(0,l.zF)(S);var P=(0,l.g$)(S,p(p({},u),{},{realScaleType:j})),A=p(p(p({},u),P),{},{range:a,radius:x,realScaleType:j,scale:S,cx:y,cy:v,innerRadius:g,outerRadius:x,startAngle:s,endAngle:f});return p(p({},t),{},h({},r,A))},{})},g=function(t,e){var r=t.x,n=t.y;return Math.sqrt(Math.pow(r-e.x,2)+Math.pow(n-e.y,2))},x=function(t,e){var r=t.x,n=t.y,o=e.cx,i=e.cy,a=g({x:r,y:n},{x:o,y:i});if(a<=0)return{radius:a};var u=Math.acos((r-o)/a);return n>i&&(u=2*Math.PI-u),{radius:a,angle:180*u/Math.PI,angleInRadian:u}},O=function(t){var e=t.startAngle,r=t.endAngle,n=Math.min(Math.floor(e/360),Math.floor(r/360));return{startAngle:e-360*n,endAngle:r-360*n}},w=function(t,e){var r,n=x({x:t.x,y:t.y},e),o=n.radius,i=n.angle,a=e.innerRadius,u=e.outerRadius;if(o<a||o>u)return!1;if(0===o)return!0;var c=O(e),l=c.startAngle,s=c.endAngle,f=i;if(l<=s){for(;f>s;)f-=360;for(;f<l;)f+=360;r=f>=l&&f<=s}else{for(;f>l;)f-=360;for(;f<s;)f+=360;r=f>=s&&f<=l}return r?p(p({},e),{},{radius:o,angle:f+360*Math.min(Math.floor(e.startAngle/360),Math.floor(e.endAngle/360))}):null},j=function(t){return(0,i.isValidElement)(t)||u()(t)||"boolean"==typeof t?"":t.className}},43843:function(t,e,r){"use strict";r.d(e,{$R:function(){return R},Bh:function(){return L},Gf:function(){return j},L6:function(){return D},NN:function(){return E},TT:function(){return M},eu:function(){return B},jf:function(){return T},rL:function(){return I},sP:function(){return k}});var n=r(1995),o=r.n(n),i=r(66952),a=r.n(i),u=r(77605),c=r.n(u),l=r(39390),s=r.n(l),f=r(41548),p=r.n(f),h=r(2265),d=r(9176),y=r(97281),v=r(1374),m=r(12655),b=["children"],g=["children"];function x(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function O(t){return(O="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var w={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},j=function(t){return"string"==typeof t?t:t?t.displayName||t.name||"Component":""},S=null,P=null,A=function t(e){if(e===S&&Array.isArray(P))return P;var r=[];return h.Children.forEach(e,function(e){a()(e)||((0,d.isFragment)(e)?r=r.concat(t(e.props.children)):r.push(e))}),P=r,S=e,r};function E(t,e){var r=[],n=[];return n=Array.isArray(e)?e.map(function(t){return j(t)}):[j(e)],A(t).forEach(function(t){var e=o()(t,"type.displayName")||o()(t,"type.name");-1!==n.indexOf(e)&&r.push(t)}),r}function k(t,e){var r=E(t,e);return r&&r[0]}var M=function(t){if(!t||!t.props)return!1;var e=t.props,r=e.width,n=e.height;return!!(0,y.hj)(r)&&!(r<=0)&&!!(0,y.hj)(n)&&!(n<=0)},_=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],T=function(t){return t&&"object"===O(t)&&"clipDot"in t},C=function(t,e,r,n){var o,i=null!==(o=null===m.ry||void 0===m.ry?void 0:m.ry[n])&&void 0!==o?o:[];return e.startsWith("data-")||!s()(t)&&(n&&i.includes(e)||m.Yh.includes(e))||r&&m.nv.includes(e)},D=function(t,e,r){if(!t||"function"==typeof t||"boolean"==typeof t)return null;var n=t;if((0,h.isValidElement)(t)&&(n=t.props),!p()(n))return null;var o={};return Object.keys(n).forEach(function(t){var i;C(null===(i=n)||void 0===i?void 0:i[t],t,e,r)&&(o[t]=n[t])}),o},I=function t(e,r){if(e===r)return!0;var n=h.Children.count(e);if(n!==h.Children.count(r))return!1;if(0===n)return!0;if(1===n)return N(Array.isArray(e)?e[0]:e,Array.isArray(r)?r[0]:r);for(var o=0;o<n;o++){var i=e[o],a=r[o];if(Array.isArray(i)||Array.isArray(a)){if(!t(i,a))return!1}else if(!N(i,a))return!1}return!0},N=function(t,e){if(a()(t)&&a()(e))return!0;if(!a()(t)&&!a()(e)){var r=t.props||{},n=r.children,o=x(r,b),i=e.props||{},u=i.children,c=x(i,g);if(n&&u)return(0,v.w)(o,c)&&I(n,u);if(!n&&!u)return(0,v.w)(o,c)}return!1},B=function(t,e){var r=[],n={};return A(t).forEach(function(t,o){if(t&&t.type&&c()(t.type)&&_.indexOf(t.type)>=0)r.push(t);else if(t){var i=j(t.type),a=e[i]||{},u=a.handler,l=a.once;if(u&&(!l||!n[i])){var s=u(t,i,o);r.push(s),n[i]=!0}}}),r},L=function(t){var e=t&&t.type;return e&&w[e]?w[e]:null},R=function(t,e){return A(e).indexOf(t)}},1374:function(t,e,r){"use strict";function n(t,e){for(var r in t)if(({}).hasOwnProperty.call(t,r)&&(!({}).hasOwnProperty.call(e,r)||t[r]!==e[r]))return!1;for(var n in e)if(({}).hasOwnProperty.call(e,n)&&!({}).hasOwnProperty.call(t,n))return!1;return!0}r.d(e,{w:function(){return n}})},9776:function(t,e,r){"use strict";r.d(e,{z:function(){return l}});var n=r(79857),o=r(41586),i=r(43843);function a(t){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function u(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function c(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?u(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=a(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=a(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==a(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):u(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var l=function(t){var e,r=t.children,a=t.formattedGraphicalItems,u=t.legendWidth,l=t.legendContent,s=(0,i.sP)(r,n.D);if(!s)return null;var f=n.D.defaultProps,p=void 0!==f?c(c({},f),s.props):{};return e=s.props&&s.props.payload?s.props&&s.props.payload:"children"===l?(a||[]).reduce(function(t,e){var r=e.item,n=e.props,o=n.sectors||n.data||[];return t.concat(o.map(function(t){return{type:s.props.iconType||r.props.legendType,value:t.name,color:t.fill,payload:t}}))},[]):(a||[]).map(function(t){var e=t.item,r=e.type.defaultProps,n=void 0!==r?c(c({},r),e.props):{},i=n.dataKey,a=n.name,u=n.legendType;return{inactive:n.hide,dataKey:i,type:p.iconType||u||"square",color:(0,o.fk)(e),value:a||i,payload:n}}),c(c(c({},p),n.D.getWithHeight(s,u)),{},{payload:e,item:s})}},50200:function(t,e,r){"use strict";r.d(e,{z:function(){return u}});var n=r(36179),o=r.n(n),i=r(39390),a=r.n(i);function u(t,e,r){return!0===e?o()(t,r):a()(e)?o()(t,e):t}},12655:function(t,e,r){"use strict";r.d(e,{Yh:function(){return u},Ym:function(){return f},bw:function(){return p},nv:function(){return s},ry:function(){return l}});var n=r(2265),o=r(41548),i=r.n(o);function a(t){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var u=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],c=["points","pathLength"],l={svg:["viewBox","children"],polygon:c,polyline:c},s=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],f=function(t,e){if(!t||"function"==typeof t||"boolean"==typeof t)return null;var r=t;if((0,n.isValidElement)(t)&&(r=t.props),!i()(r))return null;var o={};return Object.keys(r).forEach(function(t){s.includes(t)&&(o[t]=e||function(e){return r[t](r,e)})}),o},p=function(t,e,r){if(!i()(t)||"object"!==a(t))return null;var n=null;return Object.keys(t).forEach(function(o){var i=t[o];s.includes(o)&&"function"==typeof i&&(n||(n={}),n[o]=function(t){return i(e,r,t),null})}),n}},4748:function(t,e,r){var n;/*! decimal.js-light v2.5.1 https://github.com/MikeMcl/decimal.js-light/LICENCE */!function(o){"use strict";var i,a={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},u=!0,c="[DecimalError] ",l=c+"Invalid argument: ",s=c+"Exponent out of range: ",f=Math.floor,p=Math.pow,h=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,d=f(1286742750677284.5),y={};function v(t,e){var r,n,o,i,a,c,l,s,f=t.constructor,p=f.precision;if(!t.s||!e.s)return e.s||(e=new f(t)),u?A(e,p):e;if(l=t.d,s=e.d,a=t.e,o=e.e,l=l.slice(),i=a-o){for(i<0?(n=l,i=-i,c=s.length):(n=s,o=a,c=l.length),i>(c=(a=Math.ceil(p/7))>c?a+1:c+1)&&(i=c,n.length=1),n.reverse();i--;)n.push(0);n.reverse()}for((c=l.length)-(i=s.length)<0&&(i=c,n=s,s=l,l=n),r=0;i;)r=(l[--i]=l[i]+s[i]+r)/1e7|0,l[i]%=1e7;for(r&&(l.unshift(r),++o),c=l.length;0==l[--c];)l.pop();return e.d=l,e.e=o,u?A(e,p):e}function m(t,e,r){if(t!==~~t||t<e||t>r)throw Error(l+t)}function b(t){var e,r,n,o=t.length-1,i="",a=t[0];if(o>0){for(i+=a,e=1;e<o;e++)(r=7-(n=t[e]+"").length)&&(i+=j(r)),i+=n;(r=7-(n=(a=t[e])+"").length)&&(i+=j(r))}else if(0===a)return"0";for(;a%10==0;)a/=10;return i+a}y.absoluteValue=y.abs=function(){var t=new this.constructor(this);return t.s&&(t.s=1),t},y.comparedTo=y.cmp=function(t){var e,r,n,o;if(t=new this.constructor(t),this.s!==t.s)return this.s||-t.s;if(this.e!==t.e)return this.e>t.e^this.s<0?1:-1;for(e=0,r=(n=this.d.length)<(o=t.d.length)?n:o;e<r;++e)if(this.d[e]!==t.d[e])return this.d[e]>t.d[e]^this.s<0?1:-1;return n===o?0:n>o^this.s<0?1:-1},y.decimalPlaces=y.dp=function(){var t=this.d.length-1,e=(t-this.e)*7;if(t=this.d[t])for(;t%10==0;t/=10)e--;return e<0?0:e},y.dividedBy=y.div=function(t){return g(this,new this.constructor(t))},y.dividedToIntegerBy=y.idiv=function(t){var e=this.constructor;return A(g(this,new e(t),0,1),e.precision)},y.equals=y.eq=function(t){return!this.cmp(t)},y.exponent=function(){return O(this)},y.greaterThan=y.gt=function(t){return this.cmp(t)>0},y.greaterThanOrEqualTo=y.gte=function(t){return this.cmp(t)>=0},y.isInteger=y.isint=function(){return this.e>this.d.length-2},y.isNegative=y.isneg=function(){return this.s<0},y.isPositive=y.ispos=function(){return this.s>0},y.isZero=function(){return 0===this.s},y.lessThan=y.lt=function(t){return 0>this.cmp(t)},y.lessThanOrEqualTo=y.lte=function(t){return 1>this.cmp(t)},y.logarithm=y.log=function(t){var e,r=this.constructor,n=r.precision,o=n+5;if(void 0===t)t=new r(10);else if((t=new r(t)).s<1||t.eq(i))throw Error(c+"NaN");if(this.s<1)throw Error(c+(this.s?"NaN":"-Infinity"));return this.eq(i)?new r(0):(u=!1,e=g(S(this,o),S(t,o),o),u=!0,A(e,n))},y.minus=y.sub=function(t){return t=new this.constructor(t),this.s==t.s?E(this,t):v(this,(t.s=-t.s,t))},y.modulo=y.mod=function(t){var e,r=this.constructor,n=r.precision;if(!(t=new r(t)).s)throw Error(c+"NaN");return this.s?(u=!1,e=g(this,t,0,1).times(t),u=!0,this.minus(e)):A(new r(this),n)},y.naturalExponential=y.exp=function(){return x(this)},y.naturalLogarithm=y.ln=function(){return S(this)},y.negated=y.neg=function(){var t=new this.constructor(this);return t.s=-t.s||0,t},y.plus=y.add=function(t){return t=new this.constructor(t),this.s==t.s?v(this,t):E(this,(t.s=-t.s,t))},y.precision=y.sd=function(t){var e,r,n;if(void 0!==t&&!!t!==t&&1!==t&&0!==t)throw Error(l+t);if(e=O(this)+1,r=7*(n=this.d.length-1)+1,n=this.d[n]){for(;n%10==0;n/=10)r--;for(n=this.d[0];n>=10;n/=10)r++}return t&&e>r?e:r},y.squareRoot=y.sqrt=function(){var t,e,r,n,o,i,a,l=this.constructor;if(this.s<1){if(!this.s)return new l(0);throw Error(c+"NaN")}for(t=O(this),u=!1,0==(o=Math.sqrt(+this))||o==1/0?(((e=b(this.d)).length+t)%2==0&&(e+="0"),o=Math.sqrt(e),t=f((t+1)/2)-(t<0||t%2),n=new l(e=o==1/0?"5e"+t:(e=o.toExponential()).slice(0,e.indexOf("e")+1)+t)):n=new l(o.toString()),o=a=(r=l.precision)+3;;)if(n=(i=n).plus(g(this,i,a+2)).times(.5),b(i.d).slice(0,a)===(e=b(n.d)).slice(0,a)){if(e=e.slice(a-3,a+1),o==a&&"4999"==e){if(A(i,r+1,0),i.times(i).eq(this)){n=i;break}}else if("9999"!=e)break;a+=4}return u=!0,A(n,r)},y.times=y.mul=function(t){var e,r,n,o,i,a,c,l,s,f=this.constructor,p=this.d,h=(t=new f(t)).d;if(!this.s||!t.s)return new f(0);for(t.s*=this.s,r=this.e+t.e,(l=p.length)<(s=h.length)&&(i=p,p=h,h=i,a=l,l=s,s=a),i=[],n=a=l+s;n--;)i.push(0);for(n=s;--n>=0;){for(e=0,o=l+n;o>n;)c=i[o]+h[n]*p[o-n-1]+e,i[o--]=c%1e7|0,e=c/1e7|0;i[o]=(i[o]+e)%1e7|0}for(;!i[--a];)i.pop();return e?++r:i.shift(),t.d=i,t.e=r,u?A(t,f.precision):t},y.toDecimalPlaces=y.todp=function(t,e){var r=this,n=r.constructor;return(r=new n(r),void 0===t)?r:(m(t,0,1e9),void 0===e?e=n.rounding:m(e,0,8),A(r,t+O(r)+1,e))},y.toExponential=function(t,e){var r,n=this,o=n.constructor;return void 0===t?r=k(n,!0):(m(t,0,1e9),void 0===e?e=o.rounding:m(e,0,8),r=k(n=A(new o(n),t+1,e),!0,t+1)),r},y.toFixed=function(t,e){var r,n,o=this.constructor;return void 0===t?k(this):(m(t,0,1e9),void 0===e?e=o.rounding:m(e,0,8),r=k((n=A(new o(this),t+O(this)+1,e)).abs(),!1,t+O(n)+1),this.isneg()&&!this.isZero()?"-"+r:r)},y.toInteger=y.toint=function(){var t=this.constructor;return A(new t(this),O(this)+1,t.rounding)},y.toNumber=function(){return+this},y.toPower=y.pow=function(t){var e,r,n,o,a,l,s=this,p=s.constructor,h=+(t=new p(t));if(!t.s)return new p(i);if(!(s=new p(s)).s){if(t.s<1)throw Error(c+"Infinity");return s}if(s.eq(i))return s;if(n=p.precision,t.eq(i))return A(s,n);if(l=(e=t.e)>=(r=t.d.length-1),a=s.s,l){if((r=h<0?-h:h)<=9007199254740991){for(o=new p(i),e=Math.ceil(n/7+4),u=!1;r%2&&M((o=o.times(s)).d,e),0!==(r=f(r/2));)M((s=s.times(s)).d,e);return u=!0,t.s<0?new p(i).div(o):A(o,n)}}else if(a<0)throw Error(c+"NaN");return a=a<0&&1&t.d[Math.max(e,r)]?-1:1,s.s=1,u=!1,o=t.times(S(s,n+12)),u=!0,(o=x(o)).s=a,o},y.toPrecision=function(t,e){var r,n,o=this,i=o.constructor;return void 0===t?(r=O(o),n=k(o,r<=i.toExpNeg||r>=i.toExpPos)):(m(t,1,1e9),void 0===e?e=i.rounding:m(e,0,8),r=O(o=A(new i(o),t,e)),n=k(o,t<=r||r<=i.toExpNeg,t)),n},y.toSignificantDigits=y.tosd=function(t,e){var r=this.constructor;return void 0===t?(t=r.precision,e=r.rounding):(m(t,1,1e9),void 0===e?e=r.rounding:m(e,0,8)),A(new r(this),t,e)},y.toString=y.valueOf=y.val=y.toJSON=function(){var t=O(this),e=this.constructor;return k(this,t<=e.toExpNeg||t>=e.toExpPos)};var g=function(){function t(t,e){var r,n=0,o=t.length;for(t=t.slice();o--;)r=t[o]*e+n,t[o]=r%1e7|0,n=r/1e7|0;return n&&t.unshift(n),t}function e(t,e,r,n){var o,i;if(r!=n)i=r>n?1:-1;else for(o=i=0;o<r;o++)if(t[o]!=e[o]){i=t[o]>e[o]?1:-1;break}return i}function r(t,e,r){for(var n=0;r--;)t[r]-=n,n=t[r]<e[r]?1:0,t[r]=1e7*n+t[r]-e[r];for(;!t[0]&&t.length>1;)t.shift()}return function(n,o,i,a){var u,l,s,f,p,h,d,y,v,m,b,g,x,w,j,S,P,E,k=n.constructor,M=n.s==o.s?1:-1,_=n.d,T=o.d;if(!n.s)return new k(n);if(!o.s)throw Error(c+"Division by zero");for(s=0,l=n.e-o.e,P=T.length,j=_.length,y=(d=new k(M)).d=[];T[s]==(_[s]||0);)++s;if(T[s]>(_[s]||0)&&--l,(g=null==i?i=k.precision:a?i+(O(n)-O(o))+1:i)<0)return new k(0);if(g=g/7+2|0,s=0,1==P)for(f=0,T=T[0],g++;(s<j||f)&&g--;s++)x=1e7*f+(_[s]||0),y[s]=x/T|0,f=x%T|0;else{for((f=1e7/(T[0]+1)|0)>1&&(T=t(T,f),_=t(_,f),P=T.length,j=_.length),w=P,m=(v=_.slice(0,P)).length;m<P;)v[m++]=0;(E=T.slice()).unshift(0),S=T[0],T[1]>=1e7/2&&++S;do f=0,(u=e(T,v,P,m))<0?(b=v[0],P!=m&&(b=1e7*b+(v[1]||0)),(f=b/S|0)>1?(f>=1e7&&(f=1e7-1),h=(p=t(T,f)).length,m=v.length,1==(u=e(p,v,h,m))&&(f--,r(p,P<h?E:T,h))):(0==f&&(u=f=1),p=T.slice()),(h=p.length)<m&&p.unshift(0),r(v,p,m),-1==u&&(m=v.length,(u=e(T,v,P,m))<1&&(f++,r(v,P<m?E:T,m))),m=v.length):0===u&&(f++,v=[0]),y[s++]=f,u&&v[0]?v[m++]=_[w]||0:(v=[_[w]],m=1);while((w++<j||void 0!==v[0])&&g--)}return y[0]||y.shift(),d.e=l,A(d,a?i+O(d)+1:i)}}();function x(t,e){var r,n,o,a,c,l=0,f=0,h=t.constructor,d=h.precision;if(O(t)>16)throw Error(s+O(t));if(!t.s)return new h(i);for(null==e?(u=!1,c=d):c=e,a=new h(.03125);t.abs().gte(.1);)t=t.times(a),f+=5;for(c+=Math.log(p(2,f))/Math.LN10*2+5|0,r=n=o=new h(i),h.precision=c;;){if(n=A(n.times(t),c),r=r.times(++l),b((a=o.plus(g(n,r,c))).d).slice(0,c)===b(o.d).slice(0,c)){for(;f--;)o=A(o.times(o),c);return h.precision=d,null==e?(u=!0,A(o,d)):o}o=a}}function O(t){for(var e=7*t.e,r=t.d[0];r>=10;r/=10)e++;return e}function w(t,e,r){if(e>t.LN10.sd())throw u=!0,r&&(t.precision=r),Error(c+"LN10 precision limit exceeded");return A(new t(t.LN10),e)}function j(t){for(var e="";t--;)e+="0";return e}function S(t,e){var r,n,o,a,l,s,f,p,h,d=1,y=t,v=y.d,m=y.constructor,x=m.precision;if(y.s<1)throw Error(c+(y.s?"NaN":"-Infinity"));if(y.eq(i))return new m(0);if(null==e?(u=!1,p=x):p=e,y.eq(10))return null==e&&(u=!0),w(m,p);if(p+=10,m.precision=p,n=(r=b(v)).charAt(0),!(15e14>Math.abs(a=O(y))))return f=w(m,p+2,x).times(a+""),y=S(new m(n+"."+r.slice(1)),p-10).plus(f),m.precision=x,null==e?(u=!0,A(y,x)):y;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=b((y=y.times(t)).d)).charAt(0),d++;for(a=O(y),n>1?(y=new m("0."+r),a++):y=new m(n+"."+r.slice(1)),s=l=y=g(y.minus(i),y.plus(i),p),h=A(y.times(y),p),o=3;;){if(l=A(l.times(h),p),b((f=s.plus(g(l,new m(o),p))).d).slice(0,p)===b(s.d).slice(0,p))return s=s.times(2),0!==a&&(s=s.plus(w(m,p+2,x).times(a+""))),s=g(s,new m(d),p),m.precision=x,null==e?(u=!0,A(s,x)):s;s=f,o+=2}}function P(t,e){var r,n,o;for((r=e.indexOf("."))>-1&&(e=e.replace(".","")),(n=e.search(/e/i))>0?(r<0&&(r=n),r+=+e.slice(n+1),e=e.substring(0,n)):r<0&&(r=e.length),n=0;48===e.charCodeAt(n);)++n;for(o=e.length;48===e.charCodeAt(o-1);)--o;if(e=e.slice(n,o)){if(o-=n,r=r-n-1,t.e=f(r/7),t.d=[],n=(r+1)%7,r<0&&(n+=7),n<o){for(n&&t.d.push(+e.slice(0,n)),o-=7;n<o;)t.d.push(+e.slice(n,n+=7));n=7-(e=e.slice(n)).length}else n-=o;for(;n--;)e+="0";if(t.d.push(+e),u&&(t.e>d||t.e<-d))throw Error(s+r)}else t.s=0,t.e=0,t.d=[0];return t}function A(t,e,r){var n,o,i,a,c,l,h,y,v=t.d;for(a=1,i=v[0];i>=10;i/=10)a++;if((n=e-a)<0)n+=7,o=e,h=v[y=0];else{if((y=Math.ceil((n+1)/7))>=(i=v.length))return t;for(a=1,h=i=v[y];i>=10;i/=10)a++;n%=7,o=n-7+a}if(void 0!==r&&(c=h/(i=p(10,a-o-1))%10|0,l=e<0||void 0!==v[y+1]||h%i,l=r<4?(c||l)&&(0==r||r==(t.s<0?3:2)):c>5||5==c&&(4==r||l||6==r&&(n>0?o>0?h/p(10,a-o):0:v[y-1])%10&1||r==(t.s<0?8:7))),e<1||!v[0])return l?(i=O(t),v.length=1,e=e-i-1,v[0]=p(10,(7-e%7)%7),t.e=f(-e/7)||0):(v.length=1,v[0]=t.e=t.s=0),t;if(0==n?(v.length=y,i=1,y--):(v.length=y+1,i=p(10,7-n),v[y]=o>0?(h/p(10,a-o)%p(10,o)|0)*i:0),l)for(;;){if(0==y){1e7==(v[0]+=i)&&(v[0]=1,++t.e);break}if(v[y]+=i,1e7!=v[y])break;v[y--]=0,i=1}for(n=v.length;0===v[--n];)v.pop();if(u&&(t.e>d||t.e<-d))throw Error(s+O(t));return t}function E(t,e){var r,n,o,i,a,c,l,s,f,p,h=t.constructor,d=h.precision;if(!t.s||!e.s)return e.s?e.s=-e.s:e=new h(t),u?A(e,d):e;if(l=t.d,p=e.d,n=e.e,s=t.e,l=l.slice(),a=s-n){for((f=a<0)?(r=l,a=-a,c=p.length):(r=p,n=s,c=l.length),a>(o=Math.max(Math.ceil(d/7),c)+2)&&(a=o,r.length=1),r.reverse(),o=a;o--;)r.push(0);r.reverse()}else{for((f=(o=l.length)<(c=p.length))&&(c=o),o=0;o<c;o++)if(l[o]!=p[o]){f=l[o]<p[o];break}a=0}for(f&&(r=l,l=p,p=r,e.s=-e.s),c=l.length,o=p.length-c;o>0;--o)l[c++]=0;for(o=p.length;o>a;){if(l[--o]<p[o]){for(i=o;i&&0===l[--i];)l[i]=1e7-1;--l[i],l[o]+=1e7}l[o]-=p[o]}for(;0===l[--c];)l.pop();for(;0===l[0];l.shift())--n;return l[0]?(e.d=l,e.e=n,u?A(e,d):e):new h(0)}function k(t,e,r){var n,o=O(t),i=b(t.d),a=i.length;return e?(r&&(n=r-a)>0?i=i.charAt(0)+"."+i.slice(1)+j(n):a>1&&(i=i.charAt(0)+"."+i.slice(1)),i=i+(o<0?"e":"e+")+o):o<0?(i="0."+j(-o-1)+i,r&&(n=r-a)>0&&(i+=j(n))):o>=a?(i+=j(o+1-a),r&&(n=r-o-1)>0&&(i=i+"."+j(n))):((n=o+1)<a&&(i=i.slice(0,n)+"."+i.slice(n)),r&&(n=r-a)>0&&(o+1===a&&(i+="."),i+=j(n))),t.s<0?"-"+i:i}function M(t,e){if(t.length>e)return t.length=e,!0}function _(t){if(!t||"object"!=typeof t)throw Error(c+"Object expected");var e,r,n,o=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(e=0;e<o.length;e+=3)if(void 0!==(n=t[r=o[e]])){if(f(n)===n&&n>=o[e+1]&&n<=o[e+2])this[r]=n;else throw Error(l+r+": "+n)}if(void 0!==(n=t[r="LN10"])){if(n==Math.LN10)this[r]=new this(n);else throw Error(l+r+": "+n)}return this}(a=function t(e){var r,n,o;function i(t){if(!(this instanceof i))return new i(t);if(this.constructor=i,t instanceof i){this.s=t.s,this.e=t.e,this.d=(t=t.d)?t.slice():t;return}if("number"==typeof t){if(0*t!=0)throw Error(l+t);if(t>0)this.s=1;else if(t<0)t=-t,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(t===~~t&&t<1e7){this.e=0,this.d=[t];return}return P(this,t.toString())}if("string"!=typeof t)throw Error(l+t);if(45===t.charCodeAt(0)?(t=t.slice(1),this.s=-1):this.s=1,h.test(t))P(this,t);else throw Error(l+t)}if(i.prototype=y,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=t,i.config=i.set=_,void 0===e&&(e={}),e)for(r=0,o=["precision","rounding","toExpNeg","toExpPos","LN10"];r<o.length;)e.hasOwnProperty(n=o[r++])||(e[n]=this[n]);return i.config(e),i}(a)).default=a.Decimal=a,i=new a(1),void 0!==(n=(function(){return a}).call(e,r,e,t))&&(t.exports=n)}(0)},12771:function(t){"use strict";var e=Object.prototype.hasOwnProperty,r="~";function n(){}function o(t,e,r){this.fn=t,this.context=e,this.once=r||!1}function i(t,e,n,i,a){if("function"!=typeof n)throw TypeError("The listener must be a function");var u=new o(n,i||t,a),c=r?r+e:e;return t._events[c]?t._events[c].fn?t._events[c]=[t._events[c],u]:t._events[c].push(u):(t._events[c]=u,t._eventsCount++),t}function a(t,e){0==--t._eventsCount?t._events=new n:delete t._events[e]}function u(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1)),u.prototype.eventNames=function(){var t,n,o=[];if(0===this._eventsCount)return o;for(n in t=this._events)e.call(t,n)&&o.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?o.concat(Object.getOwnPropertySymbols(t)):o},u.prototype.listeners=function(t){var e=r?r+t:t,n=this._events[e];if(!n)return[];if(n.fn)return[n.fn];for(var o=0,i=n.length,a=Array(i);o<i;o++)a[o]=n[o].fn;return a},u.prototype.listenerCount=function(t){var e=r?r+t:t,n=this._events[e];return n?n.fn?1:n.length:0},u.prototype.emit=function(t,e,n,o,i,a){var u=r?r+t:t;if(!this._events[u])return!1;var c,l,s=this._events[u],f=arguments.length;if(s.fn){switch(s.once&&this.removeListener(t,s.fn,void 0,!0),f){case 1:return s.fn.call(s.context),!0;case 2:return s.fn.call(s.context,e),!0;case 3:return s.fn.call(s.context,e,n),!0;case 4:return s.fn.call(s.context,e,n,o),!0;case 5:return s.fn.call(s.context,e,n,o,i),!0;case 6:return s.fn.call(s.context,e,n,o,i,a),!0}for(l=1,c=Array(f-1);l<f;l++)c[l-1]=arguments[l];s.fn.apply(s.context,c)}else{var p,h=s.length;for(l=0;l<h;l++)switch(s[l].once&&this.removeListener(t,s[l].fn,void 0,!0),f){case 1:s[l].fn.call(s[l].context);break;case 2:s[l].fn.call(s[l].context,e);break;case 3:s[l].fn.call(s[l].context,e,n);break;case 4:s[l].fn.call(s[l].context,e,n,o);break;default:if(!c)for(p=1,c=Array(f-1);p<f;p++)c[p-1]=arguments[p];s[l].fn.apply(s[l].context,c)}}return!0},u.prototype.on=function(t,e,r){return i(this,t,e,r,!1)},u.prototype.once=function(t,e,r){return i(this,t,e,r,!0)},u.prototype.removeListener=function(t,e,n,o){var i=r?r+t:t;if(!this._events[i])return this;if(!e)return a(this,i),this;var u=this._events[i];if(u.fn)u.fn!==e||o&&!u.once||n&&u.context!==n||a(this,i);else{for(var c=0,l=[],s=u.length;c<s;c++)(u[c].fn!==e||o&&!u[c].once||n&&u[c].context!==n)&&l.push(u[c]);l.length?this._events[i]=1===l.length?l[0]:l:a(this,i)}return this},u.prototype.removeAllListeners=function(t){var e;return t?(e=r?r+t:t,this._events[e]&&a(this,e)):(this._events=new n,this._eventsCount=0),this},u.prototype.off=u.prototype.removeListener,u.prototype.addListener=u.prototype.on,u.prefixed=r,u.EventEmitter=u,t.exports=u},85656:function(t,e,r){var DataView=r(54070)(r(46838),"DataView");t.exports=DataView},43135:function(t,e,r){var n=r(70068),o=r(58464),i=r(83122),a=r(66078),u=r(31789);function c(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}c.prototype.clear=n,c.prototype.delete=o,c.prototype.get=i,c.prototype.has=a,c.prototype.set=u,t.exports=c},28887:function(t,e,r){var n=r(30783),o=r(21571),i=r(21920),a=r(98024),u=r(8285);function c(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}c.prototype.clear=n,c.prototype.delete=o,c.prototype.get=i,c.prototype.has=a,c.prototype.set=u,t.exports=c},43077:function(t,e,r){var Map=r(54070)(r(46838),"Map");t.exports=Map},56147:function(t,e,r){var n=r(88109),o=r(94709),i=r(99927),a=r(43571),u=r(94477);function c(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}c.prototype.clear=n,c.prototype.delete=o,c.prototype.get=i,c.prototype.has=a,c.prototype.set=u,t.exports=c},24524:function(t,e,r){var Promise=r(54070)(r(46838),"Promise");t.exports=Promise},14140:function(t,e,r){var Set=r(54070)(r(46838),"Set");t.exports=Set},33653:function(t,e,r){var n=r(56147),o=r(53454),i=r(62648);function a(t){var e=-1,r=null==t?0:t.length;for(this.__data__=new n;++e<r;)this.add(t[e])}a.prototype.add=a.prototype.push=o,a.prototype.has=i,t.exports=a},87178:function(t,e,r){var n=r(28887),o=r(86060),i=r(34676),a=r(22418),u=r(57581),c=r(83388);function l(t){var e=this.__data__=new n(t);this.size=e.size}l.prototype.clear=o,l.prototype.delete=i,l.prototype.get=a,l.prototype.has=u,l.prototype.set=c,t.exports=l},37515:function(t,e,r){var Symbol=r(46838).Symbol;t.exports=Symbol},1879:function(t,e,r){var Uint8Array=r(46838).Uint8Array;t.exports=Uint8Array},27591:function(t,e,r){var WeakMap=r(54070)(r(46838),"WeakMap");t.exports=WeakMap},10572:function(t){t.exports=function(t,e,r){switch(r.length){case 0:return t.call(e);case 1:return t.call(e,r[0]);case 2:return t.call(e,r[0],r[1]);case 3:return t.call(e,r[0],r[1],r[2])}return t.apply(e,r)}},82485:function(t){t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(!e(t[r],r,t))return!1;return!0}},77985:function(t){t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length,o=0,i=[];++r<n;){var a=t[r];e(a,r,t)&&(i[o++]=a)}return i}},81088:function(t,e,r){var n=r(98540);t.exports=function(t,e){return!!(null==t?0:t.length)&&n(t,e,0)>-1}},85295:function(t){t.exports=function(t,e,r){for(var n=-1,o=null==t?0:t.length;++n<o;)if(r(e,t[n]))return!0;return!1}},45897:function(t,e,r){var n=r(95523),o=r(84502),i=r(54703),a=r(58411),u=r(21497),c=r(96559),l=Object.prototype.hasOwnProperty;t.exports=function(t,e){var r=i(t),s=!r&&o(t),f=!r&&!s&&a(t),p=!r&&!s&&!f&&c(t),h=r||s||f||p,d=h?n(t.length,String):[],y=d.length;for(var v in t)(e||l.call(t,v))&&!(h&&("length"==v||f&&("offset"==v||"parent"==v)||p&&("buffer"==v||"byteLength"==v||"byteOffset"==v)||u(v,y)))&&d.push(v);return d}},45667:function(t){t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length,o=Array(n);++r<n;)o[r]=e(t[r],r,t);return o}},62849:function(t){t.exports=function(t,e){for(var r=-1,n=e.length,o=t.length;++r<n;)t[o+r]=e[r];return t}},1027:function(t){t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(e(t[r],r,t))return!0;return!1}},61085:function(t){t.exports=function(t){return t.split("")}},22312:function(t,e,r){var n=r(41233);t.exports=function(t,e){for(var r=t.length;r--;)if(n(t[r][0],e))return r;return -1}},43185:function(t,e,r){var n=r(12808);t.exports=function(t,e,r){"__proto__"==e&&n?n(t,e,{configurable:!0,enumerable:!0,value:r,writable:!0}):t[e]=r}},47392:function(t,e,r){var n=r(11496),o=r(17166)(n);t.exports=o},55323:function(t,e,r){var n=r(47392);t.exports=function(t,e){var r=!0;return n(t,function(t,n,o){return r=!!e(t,n,o)}),r}},26917:function(t,e,r){var n=r(36883);t.exports=function(t,e,r){for(var o=-1,i=t.length;++o<i;){var a=t[o],u=e(a);if(null!=u&&(void 0===c?u==u&&!n(u):r(u,c)))var c=u,l=a}return l}},42159:function(t){t.exports=function(t,e,r,n){for(var o=t.length,i=r+(n?1:-1);n?i--:++i<o;)if(e(t[i],i,t))return i;return -1}},20231:function(t,e,r){var n=r(62849),o=r(81946);t.exports=function t(e,r,i,a,u){var c=-1,l=e.length;for(i||(i=o),u||(u=[]);++c<l;){var s=e[c];r>0&&i(s)?r>1?t(s,r-1,i,a,u):n(u,s):a||(u[u.length]=s)}return u}},4263:function(t,e,r){var n=r(14820)();t.exports=n},11496:function(t,e,r){var n=r(4263),o=r(22901);t.exports=function(t,e){return t&&n(t,e,o)}},26402:function(t,e,r){var n=r(39053),o=r(67156);t.exports=function(t,e){e=n(e,t);for(var r=0,i=e.length;null!=t&&r<i;)t=t[o(e[r++])];return r&&r==i?t:void 0}},26904:function(t,e,r){var n=r(62849),o=r(54703);t.exports=function(t,e,r){var i=e(t);return o(t)?i:n(i,r(t))}},76176:function(t,e,r){var Symbol=r(37515),n=r(82992),o=r(87541),i=Symbol?Symbol.toStringTag:void 0;t.exports=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":i&&i in Object(t)?n(t):o(t)}},32338:function(t){t.exports=function(t,e){return t>e}},96217:function(t){t.exports=function(t,e){return null!=t&&e in Object(t)}},98540:function(t,e,r){var n=r(42159),o=r(19025),i=r(86976);t.exports=function(t,e,r){return e==e?i(t,e,r):n(t,o,r)}},38913:function(t,e,r){var n=r(76176),o=r(28010);t.exports=function(t){return o(t)&&"[object Arguments]"==n(t)}},49215:function(t,e,r){var n=r(94436),o=r(28010);t.exports=function t(e,r,i,a,u){return e===r||(null!=e&&null!=r&&(o(e)||o(r))?n(e,r,i,a,t,u):e!=e&&r!=r)}},94436:function(t,e,r){var n=r(87178),o=r(6644),i=r(81698),a=r(98020),u=r(49659),c=r(54703),l=r(58411),s=r(96559),f="[object Arguments]",p="[object Array]",h="[object Object]",d=Object.prototype.hasOwnProperty;t.exports=function(t,e,r,y,v,m){var b=c(t),g=c(e),x=b?p:u(t),O=g?p:u(e);x=x==f?h:x,O=O==f?h:O;var w=x==h,j=O==h,S=x==O;if(S&&l(t)){if(!l(e))return!1;b=!0,w=!1}if(S&&!w)return m||(m=new n),b||s(t)?o(t,e,r,y,v,m):i(t,e,x,r,y,v,m);if(!(1&r)){var P=w&&d.call(t,"__wrapped__"),A=j&&d.call(e,"__wrapped__");if(P||A){var E=P?t.value():t,k=A?e.value():e;return m||(m=new n),v(E,k,r,y,m)}}return!!S&&(m||(m=new n),a(t,e,r,y,v,m))}},53937:function(t,e,r){var n=r(87178),o=r(49215);t.exports=function(t,e,r,i){var a=r.length,u=a,c=!i;if(null==t)return!u;for(t=Object(t);a--;){var l=r[a];if(c&&l[2]?l[1]!==t[l[0]]:!(l[0]in t))return!1}for(;++a<u;){var s=(l=r[a])[0],f=t[s],p=l[1];if(c&&l[2]){if(void 0===f&&!(s in t))return!1}else{var h=new n;if(i)var d=i(f,p,s,t,e,h);if(!(void 0===d?o(p,f,3,i,h):d))return!1}}return!0}},19025:function(t){t.exports=function(t){return t!=t}},73707:function(t,e,r){var n=r(39390),o=r(86840),i=r(41548),a=r(2022),u=/^\[object .+?Constructor\]$/,c=Object.prototype,l=Function.prototype.toString,s=c.hasOwnProperty,f=RegExp("^"+l.call(s).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");t.exports=function(t){return!(!i(t)||o(t))&&(n(t)?f:u).test(a(t))}},77465:function(t,e,r){var n=r(76176),o=r(33712),i=r(28010),a={};a["[object Float32Array]"]=a["[object Float64Array]"]=a["[object Int8Array]"]=a["[object Int16Array]"]=a["[object Int32Array]"]=a["[object Uint8Array]"]=a["[object Uint8ClampedArray]"]=a["[object Uint16Array]"]=a["[object Uint32Array]"]=!0,a["[object Arguments]"]=a["[object Array]"]=a["[object ArrayBuffer]"]=a["[object Boolean]"]=a["[object DataView]"]=a["[object Date]"]=a["[object Error]"]=a["[object Function]"]=a["[object Map]"]=a["[object Number]"]=a["[object Object]"]=a["[object RegExp]"]=a["[object Set]"]=a["[object String]"]=a["[object WeakMap]"]=!1,t.exports=function(t){return i(t)&&o(t.length)&&!!a[n(t)]}},89308:function(t,e,r){var n=r(18291),o=r(70657),i=r(39276),a=r(54703),u=r(53785);t.exports=function(t){return"function"==typeof t?t:null==t?i:"object"==typeof t?a(t)?o(t[0],t[1]):n(t):u(t)}},28090:function(t,e,r){var n=r(3598),o=r(94808),i=Object.prototype.hasOwnProperty;t.exports=function(t){if(!n(t))return o(t);var e=[];for(var r in Object(t))i.call(t,r)&&"constructor"!=r&&e.push(r);return e}},42148:function(t){t.exports=function(t,e){return t<e}},97490:function(t,e,r){var n=r(47392),o=r(97703);t.exports=function(t,e){var r=-1,i=o(t)?Array(t.length):[];return n(t,function(t,n,o){i[++r]=e(t,n,o)}),i}},18291:function(t,e,r){var n=r(53937),o=r(79233),i=r(31968);t.exports=function(t){var e=o(t);return 1==e.length&&e[0][2]?i(e[0][0],e[0][1]):function(r){return r===t||n(r,t,e)}}},70657:function(t,e,r){var n=r(49215),o=r(1995),i=r(9279),a=r(39793),u=r(14550),c=r(31968),l=r(67156);t.exports=function(t,e){return a(t)&&u(e)?c(l(t),e):function(r){var a=o(r,t);return void 0===a&&a===e?i(r,t):n(e,a,3)}}},60455:function(t,e,r){var n=r(45667),o=r(26402),i=r(89308),a=r(97490),u=r(4779),c=r(74238),l=r(91816),s=r(39276),f=r(54703);t.exports=function(t,e,r){e=e.length?n(e,function(t){return f(t)?function(e){return o(e,1===t.length?t[0]:t)}:t}):[s];var p=-1;return e=n(e,c(i)),u(a(t,function(t,r,o){return{criteria:n(e,function(e){return e(t)}),index:++p,value:t}}),function(t,e){return l(t,e,r)})}},53730:function(t){t.exports=function(t){return function(e){return null==e?void 0:e[t]}}},82457:function(t,e,r){var n=r(26402);t.exports=function(t){return function(e){return n(e,t)}}},82223:function(t){var e=Math.ceil,r=Math.max;t.exports=function(t,n,o,i){for(var a=-1,u=r(e((n-t)/(o||1)),0),c=Array(u);u--;)c[i?u:++a]=t,t+=o;return c}},19952:function(t,e,r){var n=r(39276),o=r(84776),i=r(96684);t.exports=function(t,e){return i(o(t,e,n),t+"")}},40438:function(t,e,r){var n=r(82961),o=r(12808),i=r(39276),a=o?function(t,e){return o(t,"toString",{configurable:!0,enumerable:!1,value:n(e),writable:!0})}:i;t.exports=a},80426:function(t){t.exports=function(t,e,r){var n=-1,o=t.length;e<0&&(e=-e>o?0:o+e),(r=r>o?o:r)<0&&(r+=o),o=e>r?0:r-e>>>0,e>>>=0;for(var i=Array(o);++n<o;)i[n]=t[n+e];return i}},19040:function(t,e,r){var n=r(47392);t.exports=function(t,e){var r;return n(t,function(t,n,o){return!(r=e(t,n,o))}),!!r}},4779:function(t){t.exports=function(t,e){var r=t.length;for(t.sort(e);r--;)t[r]=t[r].value;return t}},95523:function(t){t.exports=function(t,e){for(var r=-1,n=Array(t);++r<t;)n[r]=e(r);return n}},94325:function(t,e,r){var Symbol=r(37515),n=r(45667),o=r(54703),i=r(36883),a=1/0,u=Symbol?Symbol.prototype:void 0,c=u?u.toString:void 0;t.exports=function t(e){if("string"==typeof e)return e;if(o(e))return n(e,t)+"";if(i(e))return c?c.call(e):"";var r=e+"";return"0"==r&&1/e==-a?"-0":r}},35669:function(t,e,r){var n=r(98239),o=/^\s+/;t.exports=function(t){return t?t.slice(0,n(t)+1).replace(o,""):t}},74238:function(t){t.exports=function(t){return function(e){return t(e)}}},96222:function(t,e,r){var n=r(33653),o=r(81088),i=r(85295),a=r(8304),u=r(30014),c=r(55012);t.exports=function(t,e,r){var l=-1,s=o,f=t.length,p=!0,h=[],d=h;if(r)p=!1,s=i;else if(f>=200){var y=e?null:u(t);if(y)return c(y);p=!1,s=a,d=new n}else d=e?[]:h;e:for(;++l<f;){var v=t[l],m=e?e(v):v;if(v=r||0!==v?v:0,p&&m==m){for(var b=d.length;b--;)if(d[b]===m)continue e;e&&d.push(m),h.push(v)}else s(d,m,r)||(d!==h&&d.push(m),h.push(v))}return h}},8304:function(t){t.exports=function(t,e){return t.has(e)}},39053:function(t,e,r){var n=r(54703),o=r(39793),i=r(13968),a=r(56798);t.exports=function(t,e){return n(t)?t:o(t,e)?[t]:i(a(t))}},7153:function(t,e,r){var n=r(80426);t.exports=function(t,e,r){var o=t.length;return r=void 0===r?o:r,!e&&r>=o?t:n(t,e,r)}},27125:function(t,e,r){var n=r(36883);t.exports=function(t,e){if(t!==e){var r=void 0!==t,o=null===t,i=t==t,a=n(t),u=void 0!==e,c=null===e,l=e==e,s=n(e);if(!c&&!s&&!a&&t>e||a&&u&&l&&!c&&!s||o&&u&&l||!r&&l||!i)return 1;if(!o&&!a&&!s&&t<e||s&&r&&i&&!o&&!a||c&&r&&i||!u&&i||!l)return -1}return 0}},91816:function(t,e,r){var n=r(27125);t.exports=function(t,e,r){for(var o=-1,i=t.criteria,a=e.criteria,u=i.length,c=r.length;++o<u;){var l=n(i[o],a[o]);if(l){if(o>=c)return l;return l*("desc"==r[o]?-1:1)}}return t.index-e.index}},52582:function(t,e,r){var n=r(46838)["__core-js_shared__"];t.exports=n},17166:function(t,e,r){var n=r(97703);t.exports=function(t,e){return function(r,o){if(null==r)return r;if(!n(r))return t(r,o);for(var i=r.length,a=e?i:-1,u=Object(r);(e?a--:++a<i)&&!1!==o(u[a],a,u););return r}}},14820:function(t){t.exports=function(t){return function(e,r,n){for(var o=-1,i=Object(e),a=n(e),u=a.length;u--;){var c=a[t?u:++o];if(!1===r(i[c],c,i))break}return e}}},68424:function(t,e,r){var n=r(7153),o=r(78480),i=r(50866),a=r(56798);t.exports=function(t){return function(e){var r=o(e=a(e))?i(e):void 0,u=r?r[0]:e.charAt(0),c=r?n(r,1).join(""):e.slice(1);return u[t]()+c}}},62319:function(t,e,r){var n=r(89308),o=r(97703),i=r(22901);t.exports=function(t){return function(e,r,a){var u=Object(e);if(!o(e)){var c=n(r,3);e=i(e),r=function(t){return c(u[t],t,u)}}var l=t(e,r,a);return l>-1?u[c?e[l]:l]:void 0}}},15967:function(t,e,r){var n=r(82223),o=r(12841),i=r(69569);t.exports=function(t){return function(e,r,a){return a&&"number"!=typeof a&&o(e,r,a)&&(r=a=void 0),e=i(e),void 0===r?(r=e,e=0):r=i(r),a=void 0===a?e<r?1:-1:i(a),n(e,r,a,t)}}},30014:function(t,e,r){var Set=r(14140),n=r(66239),o=r(55012),i=Set&&1/o(new Set([,-0]))[1]==1/0?function(t){return new Set(t)}:n;t.exports=i},12808:function(t,e,r){var n=r(54070),o=function(){try{var t=n(Object,"defineProperty");return t({},"",{}),t}catch(t){}}();t.exports=o},6644:function(t,e,r){var n=r(33653),o=r(1027),i=r(8304);t.exports=function(t,e,r,a,u,c){var l=1&r,s=t.length,f=e.length;if(s!=f&&!(l&&f>s))return!1;var p=c.get(t),h=c.get(e);if(p&&h)return p==e&&h==t;var d=-1,y=!0,v=2&r?new n:void 0;for(c.set(t,e),c.set(e,t);++d<s;){var m=t[d],b=e[d];if(a)var g=l?a(b,m,d,e,t,c):a(m,b,d,t,e,c);if(void 0!==g){if(g)continue;y=!1;break}if(v){if(!o(e,function(t,e){if(!i(v,e)&&(m===t||u(m,t,r,a,c)))return v.push(e)})){y=!1;break}}else if(!(m===b||u(m,b,r,a,c))){y=!1;break}}return c.delete(t),c.delete(e),y}},81698:function(t,e,r){var Symbol=r(37515),Uint8Array=r(1879),n=r(41233),o=r(6644),i=r(66198),a=r(55012),u=Symbol?Symbol.prototype:void 0,c=u?u.valueOf:void 0;t.exports=function(t,e,r,u,l,s,f){switch(r){case"[object DataView]":if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)break;t=t.buffer,e=e.buffer;case"[object ArrayBuffer]":if(t.byteLength!=e.byteLength||!s(new Uint8Array(t),new Uint8Array(e)))break;return!0;case"[object Boolean]":case"[object Date]":case"[object Number]":return n(+t,+e);case"[object Error]":return t.name==e.name&&t.message==e.message;case"[object RegExp]":case"[object String]":return t==e+"";case"[object Map]":var p=i;case"[object Set]":var h=1&u;if(p||(p=a),t.size!=e.size&&!h)break;var d=f.get(t);if(d)return d==e;u|=2,f.set(t,e);var y=o(p(t),p(e),u,l,s,f);return f.delete(t),y;case"[object Symbol]":if(c)return c.call(t)==c.call(e)}return!1}},98020:function(t,e,r){var n=r(87404),o=Object.prototype.hasOwnProperty;t.exports=function(t,e,r,i,a,u){var c=1&r,l=n(t),s=l.length;if(s!=n(e).length&&!c)return!1;for(var f=s;f--;){var p=l[f];if(!(c?p in e:o.call(e,p)))return!1}var h=u.get(t),d=u.get(e);if(h&&d)return h==e&&d==t;var y=!0;u.set(t,e),u.set(e,t);for(var v=c;++f<s;){var m=t[p=l[f]],b=e[p];if(i)var g=c?i(b,m,p,e,t,u):i(m,b,p,t,e,u);if(!(void 0===g?m===b||a(m,b,r,i,u):g)){y=!1;break}v||(v="constructor"==p)}if(y&&!v){var x=t.constructor,O=e.constructor;x!=O&&"constructor"in t&&"constructor"in e&&!("function"==typeof x&&x instanceof x&&"function"==typeof O&&O instanceof O)&&(y=!1)}return u.delete(t),u.delete(e),y}},91155:function(t,e,r){var n="object"==typeof r.g&&r.g&&r.g.Object===Object&&r.g;t.exports=n},87404:function(t,e,r){var n=r(26904),o=r(62348),i=r(22901);t.exports=function(t){return n(t,i,o)}},53587:function(t,e,r){var n=r(2998);t.exports=function(t,e){var r=t.__data__;return n(e)?r["string"==typeof e?"string":"hash"]:r.map}},79233:function(t,e,r){var n=r(14550),o=r(22901);t.exports=function(t){for(var e=o(t),r=e.length;r--;){var i=e[r],a=t[i];e[r]=[i,a,n(a)]}return e}},54070:function(t,e,r){var n=r(73707),o=r(42731);t.exports=function(t,e){var r=o(t,e);return n(r)?r:void 0}},74106:function(t,e,r){var n=r(49468)(Object.getPrototypeOf,Object);t.exports=n},82992:function(t,e,r){var Symbol=r(37515),n=Object.prototype,o=n.hasOwnProperty,i=n.toString,a=Symbol?Symbol.toStringTag:void 0;t.exports=function(t){var e=o.call(t,a),r=t[a];try{t[a]=void 0;var n=!0}catch(t){}var u=i.call(t);return n&&(e?t[a]=r:delete t[a]),u}},62348:function(t,e,r){var n=r(77985),o=r(43349),i=Object.prototype.propertyIsEnumerable,a=Object.getOwnPropertySymbols,u=a?function(t){return null==t?[]:n(a(t=Object(t)),function(e){return i.call(t,e)})}:o;t.exports=u},49659:function(t,e,r){var DataView=r(85656),Map=r(43077),Promise=r(24524),Set=r(14140),WeakMap=r(27591),n=r(76176),o=r(2022),i="[object Map]",a="[object Promise]",u="[object Set]",c="[object WeakMap]",l="[object DataView]",s=o(DataView),f=o(Map),p=o(Promise),h=o(Set),d=o(WeakMap),y=n;(DataView&&y(new DataView(new ArrayBuffer(1)))!=l||Map&&y(new Map)!=i||Promise&&y(Promise.resolve())!=a||Set&&y(new Set)!=u||WeakMap&&y(new WeakMap)!=c)&&(y=function(t){var e=n(t),r="[object Object]"==e?t.constructor:void 0,y=r?o(r):"";if(y)switch(y){case s:return l;case f:return i;case p:return a;case h:return u;case d:return c}return e}),t.exports=y},42731:function(t){t.exports=function(t,e){return null==t?void 0:t[e]}},4669:function(t,e,r){var n=r(39053),o=r(84502),i=r(54703),a=r(21497),u=r(33712),c=r(67156);t.exports=function(t,e,r){e=n(e,t);for(var l=-1,s=e.length,f=!1;++l<s;){var p=c(e[l]);if(!(f=null!=t&&r(t,p)))break;t=t[p]}return f||++l!=s?f:!!(s=null==t?0:t.length)&&u(s)&&a(p,s)&&(i(t)||o(t))}},78480:function(t){var e=RegExp("[\\u200d\ud800-\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");t.exports=function(t){return e.test(t)}},70068:function(t,e,r){var n=r(48260);t.exports=function(){this.__data__=n?n(null):{},this.size=0}},58464:function(t){t.exports=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e}},83122:function(t,e,r){var n=r(48260),o=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;if(n){var r=e[t];return"__lodash_hash_undefined__"===r?void 0:r}return o.call(e,t)?e[t]:void 0}},66078:function(t,e,r){var n=r(48260),o=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;return n?void 0!==e[t]:o.call(e,t)}},31789:function(t,e,r){var n=r(48260);t.exports=function(t,e){var r=this.__data__;return this.size+=this.has(t)?0:1,r[t]=n&&void 0===e?"__lodash_hash_undefined__":e,this}},81946:function(t,e,r){var Symbol=r(37515),n=r(84502),o=r(54703),i=Symbol?Symbol.isConcatSpreadable:void 0;t.exports=function(t){return o(t)||n(t)||!!(i&&t&&t[i])}},21497:function(t){var e=/^(?:0|[1-9]\d*)$/;t.exports=function(t,r){var n=typeof t;return!!(r=null==r?9007199254740991:r)&&("number"==n||"symbol"!=n&&e.test(t))&&t>-1&&t%1==0&&t<r}},12841:function(t,e,r){var n=r(41233),o=r(97703),i=r(21497),a=r(41548);t.exports=function(t,e,r){if(!a(r))return!1;var u=typeof e;return("number"==u?!!(o(r)&&i(e,r.length)):"string"==u&&e in r)&&n(r[e],t)}},39793:function(t,e,r){var n=r(54703),o=r(36883),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;t.exports=function(t,e){if(n(t))return!1;var r=typeof t;return!!("number"==r||"symbol"==r||"boolean"==r||null==t||o(t))||a.test(t)||!i.test(t)||null!=e&&t in Object(e)}},2998:function(t){t.exports=function(t){var e=typeof t;return"string"==e||"number"==e||"symbol"==e||"boolean"==e?"__proto__"!==t:null===t}},86840:function(t,e,r){var n,o=r(52582),i=(n=/[^.]+$/.exec(o&&o.keys&&o.keys.IE_PROTO||""))?"Symbol(src)_1."+n:"";t.exports=function(t){return!!i&&i in t}},3598:function(t){var e=Object.prototype;t.exports=function(t){var r=t&&t.constructor;return t===("function"==typeof r&&r.prototype||e)}},14550:function(t,e,r){var n=r(41548);t.exports=function(t){return t==t&&!n(t)}},30783:function(t){t.exports=function(){this.__data__=[],this.size=0}},21571:function(t,e,r){var n=r(22312),o=Array.prototype.splice;t.exports=function(t){var e=this.__data__,r=n(e,t);return!(r<0)&&(r==e.length-1?e.pop():o.call(e,r,1),--this.size,!0)}},21920:function(t,e,r){var n=r(22312);t.exports=function(t){var e=this.__data__,r=n(e,t);return r<0?void 0:e[r][1]}},98024:function(t,e,r){var n=r(22312);t.exports=function(t){return n(this.__data__,t)>-1}},8285:function(t,e,r){var n=r(22312);t.exports=function(t,e){var r=this.__data__,o=n(r,t);return o<0?(++this.size,r.push([t,e])):r[o][1]=e,this}},88109:function(t,e,r){var n=r(43135),o=r(28887),Map=r(43077);t.exports=function(){this.size=0,this.__data__={hash:new n,map:new(Map||o),string:new n}}},94709:function(t,e,r){var n=r(53587);t.exports=function(t){var e=n(this,t).delete(t);return this.size-=e?1:0,e}},99927:function(t,e,r){var n=r(53587);t.exports=function(t){return n(this,t).get(t)}},43571:function(t,e,r){var n=r(53587);t.exports=function(t){return n(this,t).has(t)}},94477:function(t,e,r){var n=r(53587);t.exports=function(t,e){var r=n(this,t),o=r.size;return r.set(t,e),this.size+=r.size==o?0:1,this}},66198:function(t){t.exports=function(t){var e=-1,r=Array(t.size);return t.forEach(function(t,n){r[++e]=[n,t]}),r}},31968:function(t){t.exports=function(t,e){return function(r){return null!=r&&r[t]===e&&(void 0!==e||t in Object(r))}}},52302:function(t,e,r){var n=r(23798);t.exports=function(t){var e=n(t,function(t){return 500===r.size&&r.clear(),t}),r=e.cache;return e}},48260:function(t,e,r){var n=r(54070)(Object,"create");t.exports=n},94808:function(t,e,r){var n=r(49468)(Object.keys,Object);t.exports=n},60789:function(t,e,r){t=r.nmd(t);var n=r(91155),o=e&&!e.nodeType&&e,i=o&&t&&!t.nodeType&&t,a=i&&i.exports===o&&n.process,u=function(){try{var t=i&&i.require&&i.require("util").types;if(t)return t;return a&&a.binding&&a.binding("util")}catch(t){}}();t.exports=u},87541:function(t){var e=Object.prototype.toString;t.exports=function(t){return e.call(t)}},49468:function(t){t.exports=function(t,e){return function(r){return t(e(r))}}},84776:function(t,e,r){var n=r(10572),o=Math.max;t.exports=function(t,e,r){return e=o(void 0===e?t.length-1:e,0),function(){for(var i=arguments,a=-1,u=o(i.length-e,0),c=Array(u);++a<u;)c[a]=i[e+a];a=-1;for(var l=Array(e+1);++a<e;)l[a]=i[a];return l[e]=r(c),n(t,this,l)}}},46838:function(t,e,r){var n=r(91155),o="object"==typeof self&&self&&self.Object===Object&&self,i=n||o||Function("return this")();t.exports=i},53454:function(t){t.exports=function(t){return this.__data__.set(t,"__lodash_hash_undefined__"),this}},62648:function(t){t.exports=function(t){return this.__data__.has(t)}},55012:function(t){t.exports=function(t){var e=-1,r=Array(t.size);return t.forEach(function(t){r[++e]=t}),r}},96684:function(t,e,r){var n=r(40438),o=r(79699)(n);t.exports=o},79699:function(t){var e=Date.now;t.exports=function(t){var r=0,n=0;return function(){var o=e(),i=16-(o-n);if(n=o,i>0){if(++r>=800)return arguments[0]}else r=0;return t.apply(void 0,arguments)}}},86060:function(t,e,r){var n=r(28887);t.exports=function(){this.__data__=new n,this.size=0}},34676:function(t){t.exports=function(t){var e=this.__data__,r=e.delete(t);return this.size=e.size,r}},22418:function(t){t.exports=function(t){return this.__data__.get(t)}},57581:function(t){t.exports=function(t){return this.__data__.has(t)}},83388:function(t,e,r){var n=r(28887),Map=r(43077),o=r(56147);t.exports=function(t,e){var r=this.__data__;if(r instanceof n){var i=r.__data__;if(!Map||i.length<199)return i.push([t,e]),this.size=++r.size,this;r=this.__data__=new o(i)}return r.set(t,e),this.size=r.size,this}},86976:function(t){t.exports=function(t,e,r){for(var n=r-1,o=t.length;++n<o;)if(t[n]===e)return n;return -1}},50866:function(t,e,r){var n=r(61085),o=r(78480),i=r(64305);t.exports=function(t){return o(t)?i(t):n(t)}},13968:function(t,e,r){var n=r(52302),o=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,i=/\\(\\)?/g,a=n(function(t){var e=[];return 46===t.charCodeAt(0)&&e.push(""),t.replace(o,function(t,r,n,o){e.push(n?o.replace(i,"$1"):r||t)}),e});t.exports=a},67156:function(t,e,r){var n=r(36883),o=1/0;t.exports=function(t){if("string"==typeof t||n(t))return t;var e=t+"";return"0"==e&&1/t==-o?"-0":e}},2022:function(t){var e=Function.prototype.toString;t.exports=function(t){if(null!=t){try{return e.call(t)}catch(t){}try{return t+""}catch(t){}}return""}},98239:function(t){var e=/\s/;t.exports=function(t){for(var r=t.length;r--&&e.test(t.charAt(r)););return r}},64305:function(t){var e="\ud800-\udfff",r="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",n="\ud83c[\udffb-\udfff]",o="[^"+e+"]",i="(?:\ud83c[\udde6-\uddff]){2}",a="[\ud800-\udbff][\udc00-\udfff]",u="(?:"+r+"|"+n+")?",c="[\\ufe0e\\ufe0f]?",l="(?:\\u200d(?:"+[o,i,a].join("|")+")"+c+u+")*",s=RegExp(n+"(?="+n+")|(?:"+[o+r+"?",r,i,a,"["+e+"]"].join("|")+")"+(c+u+l),"g");t.exports=function(t){return t.match(s)||[]}},82961:function(t){t.exports=function(t){return function(){return t}}},4913:function(t,e,r){var n=r(41548),o=r(67189),i=r(70531),a=Math.max,u=Math.min;t.exports=function(t,e,r){var c,l,s,f,p,h,d=0,y=!1,v=!1,m=!0;if("function"!=typeof t)throw TypeError("Expected a function");function b(e){var r=c,n=l;return c=l=void 0,d=e,f=t.apply(n,r)}function g(t){var r=t-h,n=t-d;return void 0===h||r>=e||r<0||v&&n>=s}function x(){var t,r,n,i=o();if(g(i))return O(i);p=setTimeout(x,(t=i-h,r=i-d,n=e-t,v?u(n,s-r):n))}function O(t){return(p=void 0,m&&c)?b(t):(c=l=void 0,f)}function w(){var t,r=o(),n=g(r);if(c=arguments,l=this,h=r,n){if(void 0===p)return d=t=h,p=setTimeout(x,e),y?b(t):f;if(v)return clearTimeout(p),p=setTimeout(x,e),b(h)}return void 0===p&&(p=setTimeout(x,e)),f}return e=i(e)||0,n(r)&&(y=!!r.leading,s=(v="maxWait"in r)?a(i(r.maxWait)||0,e):s,m="trailing"in r?!!r.trailing:m),w.cancel=function(){void 0!==p&&clearTimeout(p),d=0,c=h=l=p=void 0},w.flush=function(){return void 0===p?f:O(o())},w}},41233:function(t){t.exports=function(t,e){return t===e||t!=t&&e!=e}},61637:function(t,e,r){var n=r(82485),o=r(55323),i=r(89308),a=r(54703),u=r(12841);t.exports=function(t,e,r){var c=a(t)?n:o;return r&&u(t,e,r)&&(e=void 0),c(t,i(e,3))}},37941:function(t,e,r){var n=r(62319)(r(66296));t.exports=n},66296:function(t,e,r){var n=r(42159),o=r(89308),i=r(68969),a=Math.max;t.exports=function(t,e,r){var u=null==t?0:t.length;if(!u)return -1;var c=null==r?0:i(r);return c<0&&(c=a(u+c,0)),n(t,o(e,3),c)}},26286:function(t,e,r){var n=r(20231),o=r(71378);t.exports=function(t,e){return n(o(t,e),1)}},1995:function(t,e,r){var n=r(26402);t.exports=function(t,e,r){var o=null==t?void 0:n(t,e);return void 0===o?r:o}},9279:function(t,e,r){var n=r(96217),o=r(4669);t.exports=function(t,e){return null!=t&&o(t,e,n)}},39276:function(t){t.exports=function(t){return t}},84502:function(t,e,r){var n=r(38913),o=r(28010),i=Object.prototype,a=i.hasOwnProperty,u=i.propertyIsEnumerable,c=n(function(){return arguments}())?n:function(t){return o(t)&&a.call(t,"callee")&&!u.call(t,"callee")};t.exports=c},54703:function(t){var e=Array.isArray;t.exports=e},97703:function(t,e,r){var n=r(39390),o=r(33712);t.exports=function(t){return null!=t&&o(t.length)&&!n(t)}},95974:function(t,e,r){var n=r(76176),o=r(28010);t.exports=function(t){return!0===t||!1===t||o(t)&&"[object Boolean]"==n(t)}},58411:function(t,e,r){t=r.nmd(t);var n=r(46838),o=r(4125),i=e&&!e.nodeType&&e,a=i&&t&&!t.nodeType&&t,u=a&&a.exports===i?n.Buffer:void 0,c=u?u.isBuffer:void 0;t.exports=c||o},62927:function(t,e,r){var n=r(49215);t.exports=function(t,e){return n(t,e)}},39390:function(t,e,r){var n=r(76176),o=r(41548);t.exports=function(t){if(!o(t))return!1;var e=n(t);return"[object Function]"==e||"[object GeneratorFunction]"==e||"[object AsyncFunction]"==e||"[object Proxy]"==e}},33712:function(t){t.exports=function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=9007199254740991}},26398:function(t,e,r){var n=r(44968);t.exports=function(t){return n(t)&&t!=+t}},66952:function(t){t.exports=function(t){return null==t}},44968:function(t,e,r){var n=r(76176),o=r(28010);t.exports=function(t){return"number"==typeof t||o(t)&&"[object Number]"==n(t)}},41548:function(t){t.exports=function(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}},28010:function(t){t.exports=function(t){return null!=t&&"object"==typeof t}},56:function(t,e,r){var n=r(76176),o=r(74106),i=r(28010),a=Object.prototype,u=Function.prototype.toString,c=a.hasOwnProperty,l=u.call(Object);t.exports=function(t){if(!i(t)||"[object Object]"!=n(t))return!1;var e=o(t);if(null===e)return!0;var r=c.call(e,"constructor")&&e.constructor;return"function"==typeof r&&r instanceof r&&u.call(r)==l}},77605:function(t,e,r){var n=r(76176),o=r(54703),i=r(28010);t.exports=function(t){return"string"==typeof t||!o(t)&&i(t)&&"[object String]"==n(t)}},36883:function(t,e,r){var n=r(76176),o=r(28010);t.exports=function(t){return"symbol"==typeof t||o(t)&&"[object Symbol]"==n(t)}},96559:function(t,e,r){var n=r(77465),o=r(74238),i=r(60789),a=i&&i.isTypedArray,u=a?o(a):n;t.exports=u},22901:function(t,e,r){var n=r(45897),o=r(28090),i=r(97703);t.exports=function(t){return i(t)?n(t):o(t)}},87474:function(t){t.exports=function(t){var e=null==t?0:t.length;return e?t[e-1]:void 0}},71378:function(t,e,r){var n=r(45667),o=r(89308),i=r(97490),a=r(54703);t.exports=function(t,e){return(a(t)?n:i)(t,o(e,3))}},15277:function(t,e,r){var n=r(43185),o=r(11496),i=r(89308);t.exports=function(t,e){var r={};return e=i(e,3),o(t,function(t,o,i){n(r,o,e(t,o,i))}),r}},52136:function(t,e,r){var n=r(26917),o=r(32338),i=r(39276);t.exports=function(t){return t&&t.length?n(t,i,o):void 0}},23798:function(t,e,r){var n=r(56147);function o(t,e){if("function"!=typeof t||null!=e&&"function"!=typeof e)throw TypeError("Expected a function");var r=function(){var n=arguments,o=e?e.apply(this,n):n[0],i=r.cache;if(i.has(o))return i.get(o);var a=t.apply(this,n);return r.cache=i.set(o,a)||i,a};return r.cache=new(o.Cache||n),r}o.Cache=n,t.exports=o},83321:function(t,e,r){var n=r(26917),o=r(42148),i=r(39276);t.exports=function(t){return t&&t.length?n(t,i,o):void 0}},66239:function(t){t.exports=function(){}},67189:function(t,e,r){var n=r(46838);t.exports=function(){return n.Date.now()}},53785:function(t,e,r){var n=r(53730),o=r(82457),i=r(39793),a=r(67156);t.exports=function(t){return i(t)?n(a(t)):o(t)}},98561:function(t,e,r){var n=r(15967)();t.exports=n},79186:function(t,e,r){var n=r(1027),o=r(89308),i=r(19040),a=r(54703),u=r(12841);t.exports=function(t,e,r){var c=a(t)?n:i;return r&&u(t,e,r)&&(e=void 0),c(t,o(e,3))}},64677:function(t,e,r){var n=r(20231),o=r(60455),i=r(19952),a=r(12841),u=i(function(t,e){if(null==t)return[];var r=e.length;return r>1&&a(t,e[0],e[1])?e=[]:r>2&&a(e[0],e[1],e[2])&&(e=[e[0]]),o(t,n(e,1),[])});t.exports=u},43349:function(t){t.exports=function(){return[]}},4125:function(t){t.exports=function(){return!1}},91836:function(t,e,r){var n=r(4913),o=r(41548);t.exports=function(t,e,r){var i=!0,a=!0;if("function"!=typeof t)throw TypeError("Expected a function");return o(r)&&(i="leading"in r?!!r.leading:i,a="trailing"in r?!!r.trailing:a),n(t,e,{leading:i,maxWait:e,trailing:a})}},69569:function(t,e,r){var n=r(70531),o=1/0;t.exports=function(t){return t?(t=n(t))===o||t===-o?(t<0?-1:1)*17976931348623157e292:t==t?t:0:0===t?t:0}},68969:function(t,e,r){var n=r(69569);t.exports=function(t){var e=n(t),r=e%1;return e==e?r?e-r:e:0}},70531:function(t,e,r){var n=r(35669),o=r(41548),i=r(36883),a=0/0,u=/^[-+]0x[0-9a-f]+$/i,c=/^0b[01]+$/i,l=/^0o[0-7]+$/i,s=parseInt;t.exports=function(t){if("number"==typeof t)return t;if(i(t))return a;if(o(t)){var e="function"==typeof t.valueOf?t.valueOf():t;t=o(e)?e+"":e}if("string"!=typeof t)return 0===t?t:+t;t=n(t);var r=c.test(t);return r||l.test(t)?s(t.slice(2),r?2:8):u.test(t)?a:+t}},56798:function(t,e,r){var n=r(94325);t.exports=function(t){return null==t?"":n(t)}},36179:function(t,e,r){var n=r(89308),o=r(96222);t.exports=function(t,e){return t&&t.length?o(t,n(e,2)):[]}},21774:function(t,e,r){var n=r(68424)("toUpperCase");t.exports=n},35599:function(t,e,r){"use strict";var n=r(14823);function o(){}function i(){}i.resetWarningCache=o,t.exports=function(){function t(t,e,r,o,i,a){if(a!==n){var u=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw u.name="Invariant Violation",u}}function e(){return t}t.isRequired=t;var r={array:t,bigint:t,bool:t,func:t,number:t,object:t,string:t,symbol:t,any:t,arrayOf:e,element:t,elementType:t,instanceOf:e,node:t,objectOf:e,oneOf:e,oneOfType:e,shape:e,exact:e,checkPropTypes:i,resetWarningCache:o};return r.PropTypes=r,r}},3436:function(t,e,r){t.exports=r(35599)()},14823:function(t){"use strict";t.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},93339:function(t,e,r){"use strict";var n=r(2265);let o=n.forwardRef(function({title:t,titleId:e,...r},o){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":e},r),t?n.createElement("title",{id:e},t):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M20.25 14.15v4.25c0 1.094-.787 2.036-1.872 2.18-2.087.277-4.216.42-6.378.42s-4.291-.143-6.378-.42c-1.085-.144-1.872-1.086-1.872-2.18v-4.25m16.5 0a2.18 2.18 0 0 0 .75-1.661V8.706c0-1.081-.768-2.015-1.837-2.175a48.114 48.114 0 0 0-3.413-.387m4.5 8.006c-.194.165-.42.295-.673.38A23.978 23.978 0 0 1 12 15.75c-2.648 0-5.195-.429-7.577-1.22a2.016 2.016 0 0 1-.673-.38m0 0A2.18 2.18 0 0 1 3 12.489V8.706c0-1.081.768-2.015 1.837-2.175a48.111 48.111 0 0 1 3.413-.387m7.5 0V5.25A2.25 2.25 0 0 0 13.5 3h-3a2.25 2.25 0 0 0-2.25 2.25v.894m7.5 0a48.667 48.667 0 0 0-7.5 0M12 12.75h.008v.008H12v-.008Z"}))});e.Z=o},34697:function(t,e,r){"use strict";var n=r(2265);let o=n.forwardRef(function({title:t,titleId:e,...r},o){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":e},r),t?n.createElement("title",{id:e},t):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))});e.Z=o},48763:function(t,e,r){"use strict";var n=r(2265);let o=n.forwardRef(function({title:t,titleId:e,...r},o){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":e},r),t?n.createElement("title",{id:e},t):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z"}))});e.Z=o},90794:function(t,e,r){"use strict";function n(){for(var t,e,r=0,n="",o=arguments.length;r<o;r++)(t=arguments[r])&&(e=function t(e){var r,n,o="";if("string"==typeof e||"number"==typeof e)o+=e;else if("object"==typeof e){if(Array.isArray(e)){var i=e.length;for(r=0;r<i;r++)e[r]&&(n=t(e[r]))&&(o&&(o+=" "),o+=n)}else for(n in e)e[n]&&(o&&(o+=" "),o+=n)}return o}(t))&&(n&&(n+=" "),n+=e);return n}r.d(e,{W:function(){return n}}),e.Z=n},83896:function(t,e,r){"use strict";r.d(e,{Z:function(){return i},x:function(){return a}});var n=r(29468),o=r(13507);function i(){var t,e,r=(0,o.Z)().unknown(void 0),a=r.domain,u=r.range,c=0,l=1,s=!1,f=0,p=0,h=.5;function d(){var r=a().length,n=l<c,o=n?l:c,i=n?c:l;t=(i-o)/Math.max(1,r-f+2*p),s&&(t=Math.floor(t)),o+=(i-o-t*(r-f))*h,e=t*(1-f),s&&(o=Math.round(o),e=Math.round(e));var d=(function(t,e,r){t=+t,e=+e,r=(o=arguments.length)<2?(e=t,t=0,1):o<3?1:+r;for(var n=-1,o=0|Math.max(0,Math.ceil((e-t)/r)),i=Array(o);++n<o;)i[n]=t+n*r;return i})(r).map(function(e){return o+t*e});return u(n?d.reverse():d)}return delete r.unknown,r.domain=function(t){return arguments.length?(a(t),d()):a()},r.range=function(t){return arguments.length?([c,l]=t,c=+c,l=+l,d()):[c,l]},r.rangeRound=function(t){return[c,l]=t,c=+c,l=+l,s=!0,d()},r.bandwidth=function(){return e},r.step=function(){return t},r.round=function(t){return arguments.length?(s=!!t,d()):s},r.padding=function(t){return arguments.length?(f=Math.min(1,p=+t),d()):f},r.paddingInner=function(t){return arguments.length?(f=Math.min(1,t),d()):f},r.paddingOuter=function(t){return arguments.length?(p=+t,d()):p},r.align=function(t){return arguments.length?(h=Math.max(0,Math.min(1,t)),d()):h},r.copy=function(){return i(a(),[c,l]).round(s).paddingInner(f).paddingOuter(p).align(h)},n.o.apply(d(),arguments)}function a(){return function t(e){var r=e.copy;return e.padding=e.paddingOuter,delete e.paddingInner,delete e.paddingOuter,e.copy=function(){return t(r())},e}(i.apply(null,arguments).paddingInner(1))}},29468:function(t,e,r){"use strict";function n(t,e){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(e).domain(t)}return this}function o(t,e){switch(arguments.length){case 0:break;case 1:"function"==typeof t?this.interpolator(t):this.range(t);break;default:this.domain(t),"function"==typeof e?this.interpolator(e):this.range(e)}return this}r.d(e,{O:function(){return o},o:function(){return n}})},13507:function(t,e,r){"use strict";r.d(e,{Z:function(){return function t(){var e=new n,r=[],o=[],i=u;function c(t){let n=e.get(t);if(void 0===n){if(i!==u)return i;e.set(t,n=r.push(t)-1)}return o[n%o.length]}return c.domain=function(t){if(!arguments.length)return r.slice();for(let o of(r=[],e=new n,t))e.has(o)||e.set(o,r.push(o)-1);return c},c.range=function(t){return arguments.length?(o=Array.from(t),c):o.slice()},c.unknown=function(t){return arguments.length?(i=t,c):i},c.copy=function(){return t(r,o).unknown(i)},a.o.apply(c,arguments),c}},O:function(){return u}});class n extends Map{constructor(t,e=i){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:e}}),null!=t)for(let[e,r]of t)this.set(e,r)}get(t){return super.get(o(this,t))}has(t){return super.has(o(this,t))}set(t,e){return super.set(function({_intern:t,_key:e},r){let n=e(r);return t.has(n)?t.get(n):(t.set(n,r),r)}(this,t),e)}delete(t){return super.delete(function({_intern:t,_key:e},r){let n=e(r);return t.has(n)&&(r=t.get(n),t.delete(n)),r}(this,t))}}function o({_intern:t,_key:e},r){let n=e(r);return t.has(n)?t.get(n):r}function i(t){return null!==t&&"object"==typeof t?t.valueOf():t}var a=r(29468);let u=Symbol("implicit")},90418:function(t,e,r){"use strict";function n(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}r.d(e,{Z:function(){return n}}),Array.prototype.slice},47643:function(t,e,r){"use strict";function n(t){return function(){return t}}r.d(e,{Z:function(){return n}})},57863:function(t,e,r){"use strict";r.d(e,{d:function(){return c}});let n=Math.PI,o=2*n,i=o-1e-6;function a(t){this._+=t[0];for(let e=1,r=t.length;e<r;++e)this._+=arguments[e]+t[e]}class u{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==t?a:function(t){let e=Math.floor(t);if(!(e>=0))throw Error(`invalid digits: ${t}`);if(e>15)return a;let r=10**e;return function(t){this._+=t[0];for(let e=1,n=t.length;e<n;++e)this._+=Math.round(arguments[e]*r)/r+t[e]}}(t)}moveTo(t,e){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,e){this._append`L${this._x1=+t},${this._y1=+e}`}quadraticCurveTo(t,e,r,n){this._append`Q${+t},${+e},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(t,e,r,n,o,i){this._append`C${+t},${+e},${+r},${+n},${this._x1=+o},${this._y1=+i}`}arcTo(t,e,r,o,i){if(t=+t,e=+e,r=+r,o=+o,(i=+i)<0)throw Error(`negative radius: ${i}`);let a=this._x1,u=this._y1,c=r-t,l=o-e,s=a-t,f=u-e,p=s*s+f*f;if(null===this._x1)this._append`M${this._x1=t},${this._y1=e}`;else if(p>1e-6){if(Math.abs(f*c-l*s)>1e-6&&i){let h=r-a,d=o-u,y=c*c+l*l,v=Math.sqrt(y),m=Math.sqrt(p),b=i*Math.tan((n-Math.acos((y+p-(h*h+d*d))/(2*v*m)))/2),g=b/m,x=b/v;Math.abs(g-1)>1e-6&&this._append`L${t+g*s},${e+g*f}`,this._append`A${i},${i},0,0,${+(f*h>s*d)},${this._x1=t+x*c},${this._y1=e+x*l}`}else this._append`L${this._x1=t},${this._y1=e}`}}arc(t,e,r,a,u,c){if(t=+t,e=+e,c=!!c,(r=+r)<0)throw Error(`negative radius: ${r}`);let l=r*Math.cos(a),s=r*Math.sin(a),f=t+l,p=e+s,h=1^c,d=c?a-u:u-a;null===this._x1?this._append`M${f},${p}`:(Math.abs(this._x1-f)>1e-6||Math.abs(this._y1-p)>1e-6)&&this._append`L${f},${p}`,r&&(d<0&&(d=d%o+o),d>i?this._append`A${r},${r},0,1,${h},${t-l},${e-s}A${r},${r},0,1,${h},${this._x1=f},${this._y1=p}`:d>1e-6&&this._append`A${r},${r},0,${+(d>=n)},${h},${this._x1=t+r*Math.cos(u)},${this._y1=e+r*Math.sin(u)}`)}rect(t,e,r,n){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}h${r=+r}v${+n}h${-r}Z`}toString(){return this._}}function c(t){let e=3;return t.digits=function(r){if(!arguments.length)return e;if(null==r)e=null;else{let t=Math.floor(r);if(!(t>=0))throw RangeError(`invalid digits: ${r}`);e=t}return t},()=>new u(e)}u.prototype},16389:function(t,e,r){"use strict";function n(t,e){if(!t)throw Error("Invariant failed")}r.d(e,{Z:function(){return n}})}}]);