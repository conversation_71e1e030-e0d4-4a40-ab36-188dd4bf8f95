(()=>{var e={};e.id=91,e.ids=[91],e.modules={5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},1877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},5319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},7755:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>o});var s=t(7096),a=t(6132),n=t(7284),i=t.n(n),d=t(2564),l={};for(let e in d)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>d[e]);t.d(r,l);let o=["",{children:["dashboard",{children:["messages",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,5537)),"C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\app\\dashboard\\messages\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,8182)),"C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,2594)),"C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,6097)),"C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,5666)),"C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\app\\not-found.tsx"]}],c=["C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\app\\dashboard\\messages\\page.tsx"],m="/dashboard/messages/page",x={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/dashboard/messages/page",pathname:"/dashboard/messages",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},4637:(e,r,t)=>{Promise.resolve().then(t.bind(t,3891))},3891:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>c});var s=t(3854),a=t(4218);let n=a.forwardRef(function({title:e,titleId:r,...t},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":r},t),e?a.createElement("title",{id:r},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"}))});var i=t(8497);let d=a.forwardRef(function({title:e,titleId:r,...t},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":r},t),e?a.createElement("title",{id:r},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 12 3.269 3.125A59.769 59.769 0 0 1 21.485 12 59.768 59.768 0 0 1 3.27 20.875L5.999 12Zm0 0h7.5"}))});var l=t(817);let o=[{id:"1",clientId:"client1",clientName:"سارة أحمد",lastMessage:"شكراً لك على الاستشارة المفيدة",lastMessageTime:"2024-01-22 15:30",unreadCount:0,bookingId:"BK001",serviceName:"استشارة تطوير موقع إلكتروني",messages:[{id:"m1",senderId:"client1",senderName:"سارة أحمد",content:"مرحباً، أريد مناقشة متطلبات الموقع الإلكتروني",timestamp:"2024-01-22 14:00",isRead:!0},{id:"m2",senderId:"expert1",senderName:"أحمد محمد",content:"أهلاً وسهلاً، سأكون سعيداً لمساعدتك. ما هو نوع الموقع الذي تريدين إنشاءه؟",timestamp:"2024-01-22 14:05",isRead:!0},{id:"m3",senderId:"client1",senderName:"سارة أحمد",content:"شكراً لك على الاستشارة المفيدة",timestamp:"2024-01-22 15:30",isRead:!0}]},{id:"2",clientId:"client2",clientName:"محمد علي",lastMessage:"متى يمكننا بدء جلسة المراجعة؟",lastMessageTime:"2024-01-22 10:15",unreadCount:2,bookingId:"BK002",serviceName:"مراجعة كود البرمجة",messages:[{id:"m4",senderId:"client2",senderName:"محمد علي",content:"مرحباً، لدي كود React.js أريد مراجعته",timestamp:"2024-01-22 09:30",isRead:!0},{id:"m5",senderId:"client2",senderName:"محمد علي",content:"متى يمكننا بدء جلسة المراجعة؟",timestamp:"2024-01-22 10:15",isRead:!1}]},{id:"3",clientId:"client3",clientName:"نور حسن",lastMessage:"هل يمكن تأجيل الجلسة لغداً؟",lastMessageTime:"2024-01-21 16:45",unreadCount:1,bookingId:"BK003",serviceName:"تدريب على React Native",messages:[{id:"m6",senderId:"client3",senderName:"نور حسن",content:"هل يمكن تأجيل الجلسة لغداً؟",timestamp:"2024-01-21 16:45",isRead:!1}]}];function c(){let[e,r]=(0,a.useState)(o),[t,c]=(0,a.useState)(null),[m,x]=(0,a.useState)(""),[p,u]=(0,a.useState)(""),[g,h]=(0,a.useState)(!1),b=e.filter(e=>e.clientName.toLowerCase().includes(p.toLowerCase())||e.serviceName?.toLowerCase().includes(p.toLowerCase())),y=e.reduce((e,r)=>e+r.unreadCount,0),f=e=>{c(e),e.unreadCount>0&&r(r=>r.map(r=>r.id===e.id?{...r,unreadCount:0,messages:r.messages.map(e=>({...e,isRead:!0}))}:r))},v=async()=>{if(m.trim()&&t){h(!0);try{let e={id:`m${Date.now()}`,senderId:"expert1",senderName:"أحمد محمد",content:m.trim(),timestamp:new Date().toISOString(),isRead:!0};r(r=>r.map(r=>r.id===t.id?{...r,messages:[...r.messages,e],lastMessage:e.content,lastMessageTime:e.timestamp}:r)),c(r=>r?{...r,messages:[...r.messages,e],lastMessage:e.content,lastMessageTime:e.timestamp}:null),x("")}catch(e){console.error("Error sending message:",e)}finally{h(!1)}}},j=e=>{let r=new Date(e);return r.toLocaleTimeString("ar-SA",{hour:"2-digit",minute:"2-digit",hour12:!1})},N=e=>{let r=new Date(e),t=new Date,s=new Date(t);return(s.setDate(s.getDate()-1),r.toDateString()===t.toDateString())?"اليوم":r.toDateString()===s.toDateString()?"أمس":r.toLocaleDateString("ar-SA")};return(0,s.jsxs)("div",{className:"h-screen flex",children:[(0,s.jsxs)("div",{className:"w-1/3 bg-white dark:bg-gray-800 border-l rtl:border-l-0 rtl:border-r border-gray-200 dark:border-gray-700 flex flex-col",children:[(0,s.jsxs)("div",{className:"p-4 border-b border-gray-200 dark:border-gray-700",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[s.jsx("h1",{className:"text-xl font-bold text-gray-900 dark:text-white",children:"الرسائل"}),y>0&&(0,s.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",children:[y," غير مقروءة"]})]}),(0,s.jsxs)("div",{className:"relative",children:[s.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:s.jsx(n,{className:"h-5 w-5 text-gray-400"})}),s.jsx("input",{type:"text",placeholder:"البحث في المحادثات...",value:p,onChange:e=>u(e.target.value),className:"block w-full pr-10 border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"})]})]}),s.jsx("div",{className:"flex-1 overflow-y-auto",children:0===b.length?s.jsx(l.u,{icon:s.jsx(i.Z,{className:"h-12 w-12"}),title:"لا توجد محادثات",description:"لم يتم العثور على محادثات تطابق البحث"}):s.jsx("div",{className:"divide-y divide-gray-200 dark:divide-gray-700",children:b.map(e=>s.jsx("div",{onClick:()=>f(e),className:`p-4 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 ${t?.id===e.id?"bg-primary-50 dark:bg-primary-900 border-l-4 rtl:border-l-0 rtl:border-r-4 border-primary-500":""}`,children:(0,s.jsxs)("div",{className:"flex items-center space-x-3 rtl:space-x-reverse",children:[s.jsx("div",{className:"flex-shrink-0",children:s.jsx("div",{className:"h-10 w-10 rounded-full bg-primary-500 flex items-center justify-center",children:s.jsx("span",{className:"text-sm font-medium text-white",children:e.clientName.charAt(0)})})}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[s.jsx("p",{className:"text-sm font-medium text-gray-900 dark:text-white truncate",children:e.clientName}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 rtl:space-x-reverse",children:[s.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:N(e.lastMessageTime)}),e.unreadCount>0&&s.jsx("span",{className:"inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-red-600 rounded-full",children:e.unreadCount})]})]}),e.serviceName&&s.jsx("p",{className:"text-xs text-primary-600 dark:text-primary-400 truncate",children:e.serviceName}),s.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400 truncate",children:e.lastMessage})]})]})},e.id))})})]}),s.jsx("div",{className:"flex-1 flex flex-col",children:t?(0,s.jsxs)(s.Fragment,{children:[s.jsx("div",{className:"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-4",children:(0,s.jsxs)("div",{className:"flex items-center space-x-3 rtl:space-x-reverse",children:[s.jsx("div",{className:"h-10 w-10 rounded-full bg-primary-500 flex items-center justify-center",children:s.jsx("span",{className:"text-sm font-medium text-white",children:t.clientName.charAt(0)})}),(0,s.jsxs)("div",{children:[s.jsx("h2",{className:"text-lg font-medium text-gray-900 dark:text-white",children:t.clientName}),t.serviceName&&s.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:t.serviceName})]})]})}),s.jsx("div",{className:"flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50 dark:bg-gray-900",children:t.messages.map(e=>s.jsx("div",{className:`flex ${"expert1"===e.senderId?"justify-end":"justify-start"}`,children:(0,s.jsxs)("div",{className:`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${"expert1"===e.senderId?"bg-primary-600 text-white":"bg-white dark:bg-gray-800 text-gray-900 dark:text-white border border-gray-200 dark:border-gray-700"}`,children:[s.jsx("p",{className:"text-sm",children:e.content}),s.jsx("p",{className:`text-xs mt-1 ${"expert1"===e.senderId?"text-primary-100":"text-gray-500 dark:text-gray-400"}`,children:j(e.timestamp)})]})},e.id))}),s.jsx("div",{className:"bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-4",children:(0,s.jsxs)("div",{className:"flex space-x-3 rtl:space-x-reverse",children:[s.jsx("input",{type:"text",value:m,onChange:e=>x(e.target.value),onKeyPress:e=>"Enter"===e.key&&v(),placeholder:"اكتب رسالتك...",className:"flex-1 border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"}),s.jsx("button",{type:"button",onClick:v,disabled:!m.trim()||g,className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50",children:s.jsx(d,{className:"h-4 w-4"})})]})})]}):s.jsx("div",{className:"flex-1 flex items-center justify-center bg-gray-50 dark:bg-gray-900",children:s.jsx(l.u,{icon:s.jsx(i.Z,{className:"h-12 w-12"}),title:"اختر محادثة",description:"اختر محادثة من القائمة لبدء المراسلة"})})})]})}},817:(e,r,t)=>{"use strict";t.d(r,{u:()=>a});var s=t(3854);function a({icon:e,title:r,description:t,action:a,className:n=""}){return(0,s.jsxs)("div",{className:`text-center py-12 ${n}`,children:[e&&s.jsx("div",{className:"mx-auto h-12 w-12 text-gray-400 dark:text-gray-500 mb-4",children:e}),s.jsx("h3",{className:"mt-2 text-sm font-medium text-gray-900 dark:text-white",children:r}),t&&s.jsx("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:t}),a&&s.jsx("div",{className:"mt-6",children:(0,s.jsxs)("button",{type:"button",onClick:a.onClick,className:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500",children:[a.icon&&s.jsx("span",{className:"ml-2 h-4 w-4",children:a.icon}),a.label]})})]})}},5537:(e,r,t)=>{"use strict";t.r(r),t.d(r,{$$typeof:()=>i,__esModule:()=>n,default:()=>l});var s=t(5153);let a=(0,s.createProxy)(String.raw`C:\Users\<USER>\Documents\Freela\apps\expert-dashboard\src\app\dashboard\messages\page.tsx`),{__esModule:n,$$typeof:i}=a,d=a.default,l=d},8497:(e,r,t)=>{"use strict";t.d(r,{Z:()=>n});var s=t(4218);let a=s.forwardRef(function({title:e,titleId:r,...t},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},t),e?s.createElement("title",{id:r},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 12.76c0 1.6 1.123 2.994 2.707 3.227 1.087.16 2.185.283 3.293.369V21l4.076-4.076a1.526 1.526 0 0 1 1.037-.443 48.282 48.282 0 0 0 5.68-.494c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z"}))}),n=a}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[262,618,797],()=>t(7755));module.exports=s})();