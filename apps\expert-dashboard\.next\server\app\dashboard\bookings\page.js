(()=>{var e={};e.id=907,e.ids=[907],e.modules={5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},1877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},5319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},3057:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>o,routeModule:()=>h,tree:()=>c});var s=r(7096),a=r(6132),n=r(7284),i=r.n(n),l=r(2564),d={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);r.d(t,d);let c=["",{children:["dashboard",{children:["bookings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,7400)),"C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\app\\dashboard\\bookings\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,8182)),"C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,2594)),"C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,6097)),"C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,5666)),"C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\app\\not-found.tsx"]}],o=["C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\app\\dashboard\\bookings\\page.tsx"],m="/dashboard/bookings/page",x={require:r,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/dashboard/bookings/page",pathname:"/dashboard/bookings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},5597:(e,t,r)=>{Promise.resolve().then(r.bind(r,2147))},2147:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>g});var s=r(3854),a=r(4218),n=r(9174),i=r(3048);let l=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m4.5 12.75 6 6 9-13.5"}))});var d=r(4208),c=r(1350),o=r(8497),m=r(5675);function x({size:e="md",color:t="primary",className:r=""}){return s.jsx("div",{className:`animate-spin ${{sm:"h-4 w-4",md:"h-6 w-6",lg:"h-8 w-8"}[e]} ${{primary:"text-primary-600",white:"text-white",gray:"text-gray-400"}[t]} ${r}`,children:(0,s.jsxs)("svg",{className:"w-full h-full",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[s.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),s.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]})})}function h({message:e="جاري التحميل...",size:t="md"}){return(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center py-12",children:[s.jsx(x,{size:t}),s.jsx("p",{className:"mt-4 text-sm text-gray-500 dark:text-gray-400",children:e})]})}var u=r(817);let p=[{id:"1",clientName:"سارة أحمد",serviceName:"استشارة تطوير موقع إلكتروني",date:"2024-01-25",time:"14:00",duration:60,amount:75,status:"confirmed",paymentStatus:"paid",notes:"العميل يريد مناقشة متطلبات الموقع الإلكتروني للشركة",createdAt:"2024-01-20"},{id:"2",clientName:"محمد علي",serviceName:"مراجعة كود البرمجة",date:"2024-01-26",time:"10:00",duration:90,amount:120,status:"pending",paymentStatus:"pending",notes:"مراجعة كود React.js وتحسين الأداء",createdAt:"2024-01-22"},{id:"3",clientName:"نور حسن",serviceName:"تدريب على React Native",date:"2024-01-28",time:"16:00",duration:120,amount:150,status:"confirmed",paymentStatus:"paid",createdAt:"2024-01-18"}];function g(){let[e,t]=(0,a.useState)(p),[r,x]=(0,a.useState)("all"),[g,f]=(0,a.useState)(!1),j=e.filter(e=>"all"===r||e.status===r),y=e=>s.jsx("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${{pending:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",confirmed:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",completed:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",cancelled:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"}[e]}`,children:{pending:"في الانتظار",confirmed:"مؤكد",completed:"مكتمل",cancelled:"ملغي"}[e]}),v=async e=>{f(!0);try{await new Promise(e=>setTimeout(e,1e3)),t(t=>t.map(t=>t.id===e?{...t,status:"confirmed"}:t))}catch(e){console.error("Error accepting booking:",e)}finally{f(!1)}},b=async e=>{if(window.confirm("هل أنت متأكد من رفض هذا الحجز؟")){f(!0);try{await new Promise(e=>setTimeout(e,1e3)),t(t=>t.map(t=>t.id===e?{...t,status:"cancelled"}:t))}catch(e){console.error("Error rejecting booking:",e)}finally{f(!1)}}},w=async e=>{f(!0);try{await new Promise(e=>setTimeout(e,1e3)),t(t=>t.map(t=>t.id===e?{...t,status:"completed"}:t))}catch(e){console.error("Error completing booking:",e)}finally{f(!1)}},N=e.filter(e=>"confirmed"===e.status).length,k=e.filter(e=>"pending"===e.status).length,C=e.filter(e=>"completed"===e.status).length,E=e.filter(e=>"completed"===e.status).reduce((e,t)=>e+t.amount,0);return g?s.jsx(h,{message:"جاري تحديث الحجز..."}):(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[s.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"إدارة الحجوزات"}),s.jsx("p",{className:"mt-2 text-sm text-gray-700 dark:text-gray-300",children:"قم بإدارة حجوزاتك ومواعيدك مع العملاء"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4",children:[s.jsx("div",{className:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg",children:s.jsx("div",{className:"p-5",children:(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx("div",{className:"flex-shrink-0",children:s.jsx(n.Z,{className:"h-6 w-6 text-blue-400"})}),s.jsx("div",{className:"mr-5 w-0 flex-1",children:(0,s.jsxs)("dl",{children:[s.jsx("dt",{className:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate",children:"المواعيد القادمة"}),s.jsx("dd",{className:"text-lg font-medium text-gray-900 dark:text-white",children:N})]})})]})})}),s.jsx("div",{className:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg",children:s.jsx("div",{className:"p-5",children:(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx("div",{className:"flex-shrink-0",children:s.jsx(i.Z,{className:"h-6 w-6 text-yellow-400"})}),s.jsx("div",{className:"mr-5 w-0 flex-1",children:(0,s.jsxs)("dl",{children:[s.jsx("dt",{className:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate",children:"في الانتظار"}),s.jsx("dd",{className:"text-lg font-medium text-gray-900 dark:text-white",children:k})]})})]})})}),s.jsx("div",{className:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg",children:s.jsx("div",{className:"p-5",children:(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx("div",{className:"flex-shrink-0",children:s.jsx(l,{className:"h-6 w-6 text-green-400"})}),s.jsx("div",{className:"mr-5 w-0 flex-1",children:(0,s.jsxs)("dl",{children:[s.jsx("dt",{className:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate",children:"مكتملة"}),s.jsx("dd",{className:"text-lg font-medium text-gray-900 dark:text-white",children:C})]})})]})})}),s.jsx("div",{className:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg",children:s.jsx("div",{className:"p-5",children:(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx("div",{className:"flex-shrink-0",children:s.jsx(d.Z,{className:"h-6 w-6 text-primary-400"})}),s.jsx("div",{className:"mr-5 w-0 flex-1",children:(0,s.jsxs)("dl",{children:[s.jsx("dt",{className:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate",children:"إجمالي الأرباح"}),(0,s.jsxs)("dd",{className:"text-lg font-medium text-gray-900 dark:text-white",children:["$",E]})]})})]})})})]}),s.jsx("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg p-6",children:(0,s.jsxs)("div",{className:"flex items-center space-x-4 rtl:space-x-reverse",children:[s.jsx("label",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"تصفية حسب الحالة:"}),(0,s.jsxs)("select",{value:r,onChange:e=>x(e.target.value),className:"border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",children:[s.jsx("option",{value:"all",children:"جميع الحجوزات"}),s.jsx("option",{value:"pending",children:"في الانتظار"}),s.jsx("option",{value:"confirmed",children:"مؤكدة"}),s.jsx("option",{value:"completed",children:"مكتملة"}),s.jsx("option",{value:"cancelled",children:"ملغية"})]})]})}),0===j.length?s.jsx(u.u,{icon:s.jsx(n.Z,{className:"h-12 w-12"}),title:"لا توجد حجوزات",description:"لم يتم العثور على حجوزات تطابق المعايير المحددة"}):s.jsx("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden",children:s.jsx("ul",{className:"divide-y divide-gray-200 dark:divide-gray-700",children:j.map(e=>s.jsx("li",{className:"p-6 hover:bg-gray-50 dark:hover:bg-gray-700",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4 rtl:space-x-reverse",children:[s.jsx("div",{className:"flex-shrink-0",children:s.jsx("div",{className:"h-10 w-10 rounded-full bg-primary-500 flex items-center justify-center",children:s.jsx("span",{className:"text-sm font-medium text-white",children:e.clientName.charAt(0)})})}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 rtl:space-x-reverse",children:[s.jsx("p",{className:"text-sm font-medium text-gray-900 dark:text-white truncate",children:e.serviceName}),y(e.status)]}),(0,s.jsxs)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:["العميل: ",e.clientName]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4 rtl:space-x-reverse text-sm text-gray-500 dark:text-gray-400",children:[(0,s.jsxs)("span",{className:"flex items-center",children:[s.jsx(n.Z,{className:"h-4 w-4 ml-1"}),e.date]}),(0,s.jsxs)("span",{className:"flex items-center",children:[s.jsx(i.Z,{className:"h-4 w-4 ml-1"}),e.time," (",e.duration," دقيقة)"]}),(0,s.jsxs)("span",{className:"font-medium text-gray-900 dark:text-white",children:["$",e.amount]})]}),e.notes&&s.jsx("p",{className:"mt-2 text-sm text-gray-600 dark:text-gray-300",children:e.notes})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 rtl:space-x-reverse",children:[s.jsx("button",{type:"button",className:"text-primary-600 hover:text-primary-900 dark:text-primary-400",title:"عرض التفاصيل",children:s.jsx(c.Z,{className:"h-5 w-5"})}),s.jsx("button",{type:"button",className:"text-blue-600 hover:text-blue-900 dark:text-blue-400",title:"إرسال رسالة",children:s.jsx(o.Z,{className:"h-5 w-5"})}),"pending"===e.status&&(0,s.jsxs)(s.Fragment,{children:[s.jsx("button",{type:"button",onClick:()=>v(e.id),className:"text-green-600 hover:text-green-900 dark:text-green-400",title:"قبول الحجز",children:s.jsx(l,{className:"h-5 w-5"})}),s.jsx("button",{type:"button",onClick:()=>b(e.id),className:"text-red-600 hover:text-red-900 dark:text-red-400",title:"رفض الحجز",children:s.jsx(m.Z,{className:"h-5 w-5"})})]}),"confirmed"===e.status&&s.jsx("button",{type:"button",onClick:()=>w(e.id),className:"inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-full text-white bg-green-600 hover:bg-green-700",children:"إكمال الجلسة"})]})]})},e.id))})})]})}},817:(e,t,r)=>{"use strict";r.d(t,{u:()=>a});var s=r(3854);function a({icon:e,title:t,description:r,action:a,className:n=""}){return(0,s.jsxs)("div",{className:`text-center py-12 ${n}`,children:[e&&s.jsx("div",{className:"mx-auto h-12 w-12 text-gray-400 dark:text-gray-500 mb-4",children:e}),s.jsx("h3",{className:"mt-2 text-sm font-medium text-gray-900 dark:text-white",children:t}),r&&s.jsx("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:r}),a&&s.jsx("div",{className:"mt-6",children:(0,s.jsxs)("button",{type:"button",onClick:a.onClick,className:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500",children:[a.icon&&s.jsx("span",{className:"ml-2 h-4 w-4",children:a.icon}),a.label]})})]})}},7400:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>i,__esModule:()=>n,default:()=>d});var s=r(5153);let a=(0,s.createProxy)(String.raw`C:\Users\<USER>\Documents\Freela\apps\expert-dashboard\src\app\dashboard\bookings\page.tsx`),{__esModule:n,$$typeof:i}=a,l=a.default,d=l},8497:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});var s=r(4218);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 12.76c0 1.6 1.123 2.994 2.707 3.227 1.087.16 2.185.283 3.293.369V21l4.076-4.076a1.526 1.526 0 0 1 1.037-.443 48.282 48.282 0 0 0 5.68-.494c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z"}))}),n=a},3048:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});var s=r(4218);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}),n=a},1350:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});var s=r(4218);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))}),n=a}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[262,618,797],()=>r(3057));module.exports=s})();