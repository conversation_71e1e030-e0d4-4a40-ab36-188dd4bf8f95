(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[737],{622:function(e,t,n){"use strict";var r=n(2265),o=Symbol.for("react.element"),i=Symbol.for("react.fragment"),l=Object.prototype.hasOwnProperty,a=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,u={key:!0,ref:!0,__self:!0,__source:!0};function s(e,t,n){var r,i={},s=null,c=null;for(r in void 0!==n&&(s=""+n),void 0!==t.key&&(s=""+t.key),void 0!==t.ref&&(c=t.ref),t)l.call(t,r)&&!u.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===i[r]&&(i[r]=t[r]);return{$$typeof:o,type:e,key:s,ref:c,props:i,_owner:a.current}}t.Fragment=i,t.jsx=s,t.jsxs=s},7437:function(e,t,n){"use strict";e.exports=n(622)},1396:function(e,t,n){e.exports=n(8326)},4033:function(e,t,n){e.exports=n(94)},9332:function(e,t,n){"use strict";n.d(t,{F:function(){return s},f:function(){return c}});var r=n(2265);let o=["light","dark"],i="(prefers-color-scheme: dark)",l="undefined"==typeof window,a=(0,r.createContext)(void 0),u={setTheme:e=>{},themes:[]},s=()=>{var e;return null!==(e=(0,r.useContext)(a))&&void 0!==e?e:u},c=e=>(0,r.useContext)(a)?r.createElement(r.Fragment,null,e.children):r.createElement(f,e),d=["light","dark"],f=({forcedTheme:e,disableTransitionOnChange:t=!1,enableSystem:n=!0,enableColorScheme:l=!0,storageKey:u="theme",themes:s=d,defaultTheme:c=n?"system":"light",attribute:f="data-theme",value:g,children:E,nonce:b})=>{let[w,y]=(0,r.useState)(()=>p(u,c)),[T,S]=(0,r.useState)(()=>p(u)),M=g?Object.values(g):s,k=(0,r.useCallback)(e=>{let r=e;if(!r)return;"system"===e&&n&&(r=h());let i=g?g[r]:r,a=t?v():null,u=document.documentElement;if("class"===f?(u.classList.remove(...M),i&&u.classList.add(i)):i?u.setAttribute(f,i):u.removeAttribute(f),l){let e=o.includes(c)?c:null,t=o.includes(r)?r:e;u.style.colorScheme=t}null==a||a()},[]),R=(0,r.useCallback)(e=>{y(e);try{localStorage.setItem(u,e)}catch(e){}},[e]),C=(0,r.useCallback)(t=>{let r=h(t);S(r),"system"===w&&n&&!e&&k("system")},[w,e]);(0,r.useEffect)(()=>{let e=window.matchMedia(i);return e.addListener(C),C(e),()=>e.removeListener(C)},[C]),(0,r.useEffect)(()=>{let e=e=>{e.key===u&&R(e.newValue||c)};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[R]),(0,r.useEffect)(()=>{k(null!=e?e:w)},[e,w]);let L=(0,r.useMemo)(()=>({theme:w,setTheme:R,forcedTheme:e,resolvedTheme:"system"===w?T:w,themes:n?[...s,"system"]:s,systemTheme:n?T:void 0}),[w,R,e,T,n,s]);return r.createElement(a.Provider,{value:L},r.createElement(m,{forcedTheme:e,disableTransitionOnChange:t,enableSystem:n,enableColorScheme:l,storageKey:u,themes:s,defaultTheme:c,attribute:f,value:g,children:E,attrs:M,nonce:b}),E)},m=(0,r.memo)(({forcedTheme:e,storageKey:t,attribute:n,enableSystem:l,enableColorScheme:a,defaultTheme:u,value:s,attrs:c,nonce:d})=>{let f="system"===u,m="class"===n?`var d=document.documentElement,c=d.classList;c.remove(${c.map(e=>`'${e}'`).join(",")});`:`var d=document.documentElement,n='${n}',s='setAttribute';`,p=a?o.includes(u)&&u?`if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'${u}'`:"if(e==='light'||e==='dark')d.style.colorScheme=e":"",v=(e,t=!1,r=!0)=>{let i=s?s[e]:e,l=t?e+"|| ''":`'${i}'`,u="";return a&&r&&!t&&o.includes(e)&&(u+=`d.style.colorScheme = '${e}';`),"class"===n?u+=t||i?`c.add(${l})`:"null":i&&(u+=`d[s](n,${l})`),u},h=e?`!function(){${m}${v(e)}}()`:l?`!function(){try{${m}var e=localStorage.getItem('${t}');if('system'===e||(!e&&${f})){var t='${i}',m=window.matchMedia(t);if(m.media!==t||m.matches){${v("dark")}}else{${v("light")}}}else if(e){${s?`var x=${JSON.stringify(s)};`:""}${v(s?"x[e]":"e",!0)}}${f?"":"else{"+v(u,!1,!1)+"}"}${p}}catch(e){}}()`:`!function(){try{${m}var e=localStorage.getItem('${t}');if(e){${s?`var x=${JSON.stringify(s)};`:""}${v(s?"x[e]":"e",!0)}}else{${v(u,!1,!1)};}${p}}catch(t){}}();`;return r.createElement("script",{nonce:d,dangerouslySetInnerHTML:{__html:h}})},()=>!0),p=(e,t)=>{let n;if(!l){try{n=localStorage.getItem(e)||void 0}catch(e){}return n||t}},v=()=>{let e=document.createElement("style");return e.appendChild(document.createTextNode("*{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(e),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(e)},1)}},h=e=>(e||(e=window.matchMedia(i)),e.matches?"dark":"light")},4781:function(e,t,n){"use strict";let r,o;n.d(t,{V:function(){return ey}});var i,l,a,u,s,c,d,f=n(2265),m=n.t(f,2),p=n(2769),v=n(2950),h=n(1858);function g(e,t,n,r){let o=(0,h.E)(n);(0,f.useEffect)(()=>{function n(e){o.current(e)}return(e=null!=e?e:window).addEventListener(t,n,r),()=>e.removeEventListener(t,n,r)},[e,t,r])}var E=n(634),b=n(5195);function w(e){let t=(0,v.z)(e),n=(0,f.useRef)(!1);(0,f.useEffect)(()=>(n.current=!1,()=>{n.current=!0,(0,b.Y)(()=>{n.current&&t()})}),[t])}var y=n(9888),T=n(8957),S=n(6618),M=n(7976),k=((i=k||{})[i.Forwards=0]="Forwards",i[i.Backwards=1]="Backwards",i);function R(e,t){let n=(0,f.useRef)([]),r=(0,v.z)(e);(0,f.useEffect)(()=>{let e=[...n.current];for(let[o,i]of t.entries())if(n.current[o]!==i){let o=r(t,e);return n.current=t,o}},[r,...t])}var C=n(1931),L=((l=L||{})[l.None=1]="None",l[l.Focusable=2]="Focusable",l[l.Hidden=4]="Hidden",l);let x=(0,C.yV)(function(e,t){var n;let{features:r=1,...o}=e,i={ref:t,"aria-hidden":(2&r)==2||(null!=(n=o["aria-hidden"])?n:void 0),hidden:(4&r)==4||void 0,style:{position:"fixed",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0",...(4&r)==4&&(2&r)!=2&&{display:"none"}}};return(0,C.sY)({ourProps:i,theirProps:o,slot:{},defaultTag:"div",name:"Hidden"})}),O=[];!function(e){function t(){"loading"!==document.readyState&&(e(),document.removeEventListener("DOMContentLoaded",t))}"undefined"!=typeof window&&"undefined"!=typeof document&&(document.addEventListener("DOMContentLoaded",t),t())}(()=>{function e(e){e.target instanceof HTMLElement&&e.target!==document.body&&O[0]!==e.target&&(O.unshift(e.target),(O=O.filter(e=>null!=e&&e.isConnected)).splice(10))}window.addEventListener("click",e,{capture:!0}),window.addEventListener("mousedown",e,{capture:!0}),window.addEventListener("focus",e,{capture:!0}),document.body.addEventListener("click",e,{capture:!0}),document.body.addEventListener("mousedown",e,{capture:!0}),document.body.addEventListener("focus",e,{capture:!0})});var P=n(5410),F=n(597);function A(e){if(!e)return new Set;if("function"==typeof e)return new Set(e());let t=new Set;for(let n of e.current)n.current instanceof HTMLElement&&t.add(n.current);return t}var I=((a=I||{})[a.None=1]="None",a[a.InitialFocus=2]="InitialFocus",a[a.TabLock=4]="TabLock",a[a.FocusLock=8]="FocusLock",a[a.RestoreFocus=16]="RestoreFocus",a[a.All=30]="All",a);let D=Object.assign((0,C.yV)(function(e,t){let n,r=(0,f.useRef)(null),o=(0,S.T)(r,t),{initialFocus:i,containers:l,features:a=30,...u}=e;(0,T.H)()||(a=1);let s=(0,y.i)(r);!function({ownerDocument:e},t){let n=function(e=!0){let t=(0,f.useRef)(O.slice());return R(([e],[n])=>{!0===n&&!1===e&&(0,b.Y)(()=>{t.current.splice(0)}),!1===n&&!0===e&&(t.current=O.slice())},[e,O,t]),(0,v.z)(()=>{var e;return null!=(e=t.current.find(e=>null!=e&&e.isConnected))?e:null})}(t);R(()=>{t||(null==e?void 0:e.activeElement)===(null==e?void 0:e.body)&&(0,P.C5)(n())},[t]),w(()=>{t&&(0,P.C5)(n())})}({ownerDocument:s},!!(16&a));let c=function({ownerDocument:e,container:t,initialFocus:n},r){let o=(0,f.useRef)(null),i=(0,E.t)();return R(()=>{if(!r)return;let l=t.current;l&&(0,b.Y)(()=>{if(!i.current)return;let t=null==e?void 0:e.activeElement;if(null!=n&&n.current){if((null==n?void 0:n.current)===t){o.current=t;return}}else if(l.contains(t)){o.current=t;return}null!=n&&n.current?(0,P.C5)(n.current):(0,P.jA)(l,P.TO.First)===P.fE.Error&&console.warn("There are no focusable elements inside the <FocusTrap />"),o.current=null==e?void 0:e.activeElement})},[r]),o}({ownerDocument:s,container:r,initialFocus:i},!!(2&a));!function({ownerDocument:e,container:t,containers:n,previousActiveElement:r},o){let i=(0,E.t)();g(null==e?void 0:e.defaultView,"focus",e=>{if(!o||!i.current)return;let l=A(n);t.current instanceof HTMLElement&&l.add(t.current);let a=r.current;if(!a)return;let u=e.target;u&&u instanceof HTMLElement?N(l,u)?(r.current=u,(0,P.C5)(u)):(e.preventDefault(),e.stopPropagation(),(0,P.C5)(a)):(0,P.C5)(r.current)},!0)}({ownerDocument:s,container:r,containers:l,previousActiveElement:c},!!(8&a));let d=(n=(0,f.useRef)(0),(0,M.s)("keydown",e=>{"Tab"===e.key&&(n.current=e.shiftKey?1:0)},!0),n),m=(0,v.z)(e=>{let t=r.current;t&&(0,F.E)(d.current,{[k.Forwards]:()=>{(0,P.jA)(t,P.TO.First,{skipElements:[e.relatedTarget]})},[k.Backwards]:()=>{(0,P.jA)(t,P.TO.Last,{skipElements:[e.relatedTarget]})}})}),h=(0,p.G)(),I=(0,f.useRef)(!1);return f.createElement(f.Fragment,null,!!(4&a)&&f.createElement(x,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:m,features:L.Focusable}),(0,C.sY)({ourProps:{ref:o,onKeyDown(e){"Tab"==e.key&&(I.current=!0,h.requestAnimationFrame(()=>{I.current=!1}))},onBlur(e){let t=A(l);r.current instanceof HTMLElement&&t.add(r.current);let n=e.relatedTarget;n instanceof HTMLElement&&"true"!==n.dataset.headlessuiFocusGuard&&(N(t,n)||(I.current?(0,P.jA)(r.current,(0,F.E)(d.current,{[k.Forwards]:()=>P.TO.Next,[k.Backwards]:()=>P.TO.Previous})|P.TO.WrapAround,{relativeTo:e.target}):e.target instanceof HTMLElement&&(0,P.C5)(e.target)))}},theirProps:u,defaultTag:"div",name:"FocusTrap"}),!!(4&a)&&f.createElement(x,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:m,features:L.Focusable}))}),{features:I});function N(e,t){for(let n of e)if(n.contains(t))return!0;return!1}var j=n(4887),H=n(8610);let $=(0,f.createContext)(!1);function _(e){return f.createElement($.Provider,{value:e.force},e.children)}var V=n(2057);let z=f.Fragment,B=f.Fragment,Z=(0,f.createContext)(null),Y=(0,f.createContext)(null),W=Object.assign((0,C.yV)(function(e,t){let n=(0,f.useRef)(null),r=(0,S.T)((0,S.h)(e=>{n.current=e}),t),o=(0,y.i)(n),i=function(e){let t=(0,f.useContext)($),n=(0,f.useContext)(Z),r=(0,y.i)(e),[o,i]=(0,f.useState)(()=>{if(!t&&null!==n||V.O.isServer)return null;let e=null==r?void 0:r.getElementById("headlessui-portal-root");if(e)return e;if(null===r)return null;let o=r.createElement("div");return o.setAttribute("id","headlessui-portal-root"),r.body.appendChild(o)});return(0,f.useEffect)(()=>{null!==o&&(null!=r&&r.body.contains(o)||null==r||r.body.appendChild(o))},[o,r]),(0,f.useEffect)(()=>{t||null!==n&&i(n.current)},[n,i,t]),o}(n),[l]=(0,f.useState)(()=>{var e;return V.O.isServer?null:null!=(e=null==o?void 0:o.createElement("div"))?e:null}),a=(0,f.useContext)(Y),u=(0,T.H)();return(0,H.e)(()=>{!i||!l||i.contains(l)||(l.setAttribute("data-headlessui-portal",""),i.appendChild(l))},[i,l]),(0,H.e)(()=>{if(l&&a)return a.register(l)},[a,l]),w(()=>{var e;i&&l&&(l instanceof Node&&i.contains(l)&&i.removeChild(l),i.childNodes.length<=0&&(null==(e=i.parentElement)||e.removeChild(i)))}),u&&i&&l?(0,j.createPortal)((0,C.sY)({ourProps:{ref:r},theirProps:e,defaultTag:z,name:"Portal"}),l):null}),{Group:(0,C.yV)(function(e,t){let{target:n,...r}=e,o={ref:(0,S.T)(t)};return f.createElement(Z.Provider,{value:n},(0,C.sY)({ourProps:o,theirProps:r,defaultTag:B,name:"Popover.Group"}))})}),{useState:U,useEffect:q,useLayoutEffect:G,useDebugValue:K}=m;"undefined"!=typeof window&&void 0!==window.document&&window.document.createElement;let J=m.useSyncExternalStore;var Q=n(5390),X=n(4644);let ee=(u={PUSH(e,t){var n;let r=null!=(n=this.get(e))?n:{doc:e,count:0,d:(0,Q.k)(),meta:new Set};return r.count++,r.meta.add(t),this.set(e,r),this},POP(e,t){let n=this.get(e);return n&&(n.count--,n.meta.delete(t)),this},SCROLL_PREVENT({doc:e,d:t,meta:n}){let r;let o={doc:e,d:t,meta:function(e){let t={};for(let n of e)Object.assign(t,n(t));return t}(n)},i=[(0,X.gn)()?{before({doc:e,d:t,meta:n}){function r(e){return n.containers.flatMap(e=>e()).some(t=>t.contains(e))}t.microTask(()=>{var n;if("auto"!==window.getComputedStyle(e.documentElement).scrollBehavior){let n=(0,Q.k)();n.style(e.documentElement,"scrollBehavior","auto"),t.add(()=>t.microTask(()=>n.dispose()))}let o=null!=(n=window.scrollY)?n:window.pageYOffset,i=null;t.addEventListener(e,"click",t=>{if(t.target instanceof HTMLElement)try{let n=t.target.closest("a");if(!n)return;let{hash:o}=new URL(n.href),l=e.querySelector(o);l&&!r(l)&&(i=l)}catch{}},!0),t.addEventListener(e,"touchstart",e=>{if(e.target instanceof HTMLElement){if(r(e.target)){let n=e.target;for(;n.parentElement&&r(n.parentElement);)n=n.parentElement;t.style(n,"overscrollBehavior","contain")}else t.style(e.target,"touchAction","none")}}),t.addEventListener(e,"touchmove",e=>{if(e.target instanceof HTMLElement){if(r(e.target)){let t=e.target;for(;t.parentElement&&""!==t.dataset.headlessuiPortal&&!(t.scrollHeight>t.clientHeight||t.scrollWidth>t.clientWidth);)t=t.parentElement;""===t.dataset.headlessuiPortal&&e.preventDefault()}else e.preventDefault()}},{passive:!1}),t.add(()=>{var e;o!==(null!=(e=window.scrollY)?e:window.pageYOffset)&&window.scrollTo(0,o),i&&i.isConnected&&(i.scrollIntoView({block:"nearest"}),i=null)})})}}:{},{before({doc:e}){var t;let n=e.documentElement;r=(null!=(t=e.defaultView)?t:window).innerWidth-n.clientWidth},after({doc:e,d:t}){let n=e.documentElement,o=n.clientWidth-n.offsetWidth,i=r-o;t.style(n,"paddingRight",`${i}px`)}},{before({doc:e,d:t}){t.style(e.documentElement,"overflow","hidden")}}];i.forEach(({before:e})=>null==e?void 0:e(o)),i.forEach(({after:e})=>null==e?void 0:e(o))},SCROLL_ALLOW({d:e}){e.dispose()},TEARDOWN({doc:e}){this.delete(e)}},r=new Map,o=new Set,{getSnapshot:()=>r,subscribe:e=>(o.add(e),()=>o.delete(e)),dispatch(e,...t){let n=u[e].call(r,...t);n&&(r=n,o.forEach(e=>e()))}});ee.subscribe(()=>{let e=ee.getSnapshot(),t=new Map;for(let[n]of e)t.set(n,n.documentElement.style.overflow);for(let n of e.values()){let e="hidden"===t.get(n.doc),r=0!==n.count;(r&&!e||!r&&e)&&ee.dispatch(n.count>0?"SCROLL_PREVENT":"SCROLL_ALLOW",n),0===n.count&&ee.dispatch("TEARDOWN",n)}});var et=n(5606);let en=new Map,er=new Map;function eo(e,t=!0){(0,H.e)(()=>{var n;if(!t)return;let r="function"==typeof e?e():e.current;if(!r)return;let o=null!=(n=er.get(r))?n:0;return er.set(r,o+1),0!==o||(en.set(r,{"aria-hidden":r.getAttribute("aria-hidden"),inert:r.inert}),r.setAttribute("aria-hidden","true"),r.inert=!0),function(){var e;if(!r)return;let t=null!=(e=er.get(r))?e:1;if(1===t?er.delete(r):er.set(r,t-1),1!==t)return;let n=en.get(r);n&&(null===n["aria-hidden"]?r.removeAttribute("aria-hidden"):r.setAttribute("aria-hidden",n["aria-hidden"]),r.inert=n.inert,en.delete(r))}},[e,t])}var ei=n(583),el=n(5306);let ea=(0,f.createContext)(()=>{});ea.displayName="StackContext";var eu=((s=eu||{})[s.Add=0]="Add",s[s.Remove=1]="Remove",s);function es({children:e,onUpdate:t,type:n,element:r,enabled:o}){let i=(0,f.useContext)(ea),l=(0,v.z)((...e)=>{null==t||t(...e),i(...e)});return(0,H.e)(()=>{let e=void 0===o||!0===o;return e&&l(0,n,r),()=>{e&&l(1,n,r)}},[l,n,r,o]),f.createElement(ea.Provider,{value:l},e)}var ec=n(5863);let ed=(0,f.createContext)(null),ef=Object.assign((0,C.yV)(function(e,t){let n=(0,et.M)(),{id:r=`headlessui-description-${n}`,...o}=e,i=function e(){let t=(0,f.useContext)(ed);if(null===t){let t=Error("You used a <Description /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(t,e),t}return t}(),l=(0,S.T)(t);(0,H.e)(()=>i.register(r),[r,i.register]);let a={ref:l,...i.props,id:r};return(0,C.sY)({ourProps:a,theirProps:o,slot:i.slot||{},defaultTag:"p",name:i.name||"Description"})}),{});var em=n(3850),ep=((c=ep||{})[c.Open=0]="Open",c[c.Closed=1]="Closed",c),ev=((d=ev||{})[d.SetTitleId=0]="SetTitleId",d);let eh={0:(e,t)=>e.titleId===t.id?e:{...e,titleId:t.id}},eg=(0,f.createContext)(null);function eE(e){let t=(0,f.useContext)(eg);if(null===t){let t=Error(`<${e} /> is missing a parent <Dialog /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,eE),t}return t}function eb(e,t){return(0,F.E)(t.type,eh,e,t)}eg.displayName="DialogContext";let ew=C.AN.RenderStrategy|C.AN.Static,ey=Object.assign((0,C.yV)(function(e,t){let n,r,o,i,l,a=(0,et.M)(),{id:u=`headlessui-dialog-${a}`,open:s,onClose:c,initialFocus:d,role:m="dialog",__demoMode:p=!1,...h}=e,[E,b]=(0,f.useState)(0),w=(0,f.useRef)(!1);m="dialog"===m||"alertdialog"===m?m:(w.current||(w.current=!0,console.warn(`Invalid role [${m}] passed to <Dialog />. Only \`dialog\` and and \`alertdialog\` are supported. Using \`dialog\` instead.`)),"dialog");let M=(0,el.oJ)();void 0===s&&null!==M&&(s=(M&el.ZM.Open)===el.ZM.Open);let k=(0,f.useRef)(null),R=(0,S.T)(k,t),O=(0,y.i)(k),P=e.hasOwnProperty("open")||null!==M,A=e.hasOwnProperty("onClose");if(!P&&!A)throw Error("You have to provide an `open` and an `onClose` prop to the `Dialog` component.");if(!P)throw Error("You provided an `onClose` prop to the `Dialog`, but forgot an `open` prop.");if(!A)throw Error("You provided an `open` prop to the `Dialog`, but forgot an `onClose` prop.");if("boolean"!=typeof s)throw Error(`You provided an \`open\` prop to the \`Dialog\`, but the value is not a boolean. Received: ${s}`);if("function"!=typeof c)throw Error(`You provided an \`onClose\` prop to the \`Dialog\`, but the value is not a function. Received: ${c}`);let I=s?0:1,[N,j]=(0,f.useReducer)(eb,{titleId:null,descriptionId:null,panelRef:(0,f.createRef)()}),$=(0,v.z)(()=>c(!1)),V=(0,v.z)(e=>j({type:0,id:e})),z=!!(0,T.H)()&&!p&&0===I,B=E>1,Z=null!==(0,f.useContext)(eg),[U,q]=(n=(0,f.useContext)(Y),r=(0,f.useRef)([]),o=(0,v.z)(e=>(r.current.push(e),n&&n.register(e),()=>i(e))),i=(0,v.z)(e=>{let t=r.current.indexOf(e);-1!==t&&r.current.splice(t,1),n&&n.unregister(e)}),l=(0,f.useMemo)(()=>({register:o,unregister:i,portals:r}),[o,i,r]),[r,(0,f.useMemo)(()=>function({children:e}){return f.createElement(Y.Provider,{value:l},e)},[l])]),{resolveContainers:G,mainTreeNodeRef:K,MainTreeNode:Q}=function({defaultContainers:e=[],portals:t,mainTreeNodeRef:n}={}){var r;let o=(0,f.useRef)(null!=(r=null==n?void 0:n.current)?r:null),i=(0,y.i)(o),l=(0,v.z)(()=>{var n,r,l;let a=[];for(let t of e)null!==t&&(t instanceof HTMLElement?a.push(t):"current"in t&&t.current instanceof HTMLElement&&a.push(t.current));if(null!=t&&t.current)for(let e of t.current)a.push(e);for(let e of null!=(n=null==i?void 0:i.querySelectorAll("html > *, body > *"))?n:[])e!==document.body&&e!==document.head&&e instanceof HTMLElement&&"headlessui-portal-root"!==e.id&&(e.contains(o.current)||e.contains(null==(l=null==(r=o.current)?void 0:r.getRootNode())?void 0:l.host)||a.some(t=>e.contains(t))||a.push(e));return a});return{resolveContainers:l,contains:(0,v.z)(e=>l().some(t=>t.contains(e))),mainTreeNodeRef:o,MainTreeNode:(0,f.useMemo)(()=>function(){return null!=n?null:f.createElement(x,{features:L.Hidden,ref:o})},[o,n])}}({portals:U,defaultContainers:[{get current(){var X;return null!=(X=N.panelRef.current)?X:k.current}}]}),en=null!==M&&(M&el.ZM.Closing)===el.ZM.Closing,er=!Z&&!en&&z;eo((0,f.useCallback)(()=>{var e,t;return null!=(t=Array.from(null!=(e=null==O?void 0:O.querySelectorAll("body > *"))?e:[]).find(e=>"headlessui-portal-root"!==e.id&&e.contains(K.current)&&e instanceof HTMLElement))?t:null},[K]),er);let ea=!!B||z;eo((0,f.useCallback)(()=>{var e,t;return null!=(t=Array.from(null!=(e=null==O?void 0:O.querySelectorAll("[data-headlessui-portal]"))?e:[]).find(e=>e.contains(K.current)&&e instanceof HTMLElement))?t:null},[K]),ea);let ec=!(!z||B);(0,ei.O)(G,e=>{e.preventDefault(),$()},ec);let ef=!(B||0!==I);g(null==O?void 0:O.defaultView,"keydown",e=>{ef&&(e.defaultPrevented||e.key===em.R.Escape&&(e.preventDefault(),e.stopPropagation(),$()))}),function(e,t,n=()=>[document.body]){var r;let o,i;r=e=>{var t;return{containers:[...null!=(t=e.containers)?t:[],n]}},o=J(ee.subscribe,ee.getSnapshot,ee.getSnapshot),(i=e?o.get(e):void 0)&&i.count,(0,H.e)(()=>{if(!(!e||!t))return ee.dispatch("PUSH",e,r),()=>ee.dispatch("POP",e,r)},[t,e])}(O,!(en||0!==I||Z),G),(0,f.useEffect)(()=>{if(0!==I||!k.current)return;let e=new ResizeObserver(e=>{for(let t of e){let e=t.target.getBoundingClientRect();0===e.x&&0===e.y&&0===e.width&&0===e.height&&$()}});return e.observe(k.current),()=>e.disconnect()},[I,k,$]);let[ep,ev]=function(){let[e,t]=(0,f.useState)([]);return[e.length>0?e.join(" "):void 0,(0,f.useMemo)(()=>function(e){let n=(0,v.z)(e=>(t(t=>[...t,e]),()=>t(t=>{let n=t.slice(),r=n.indexOf(e);return -1!==r&&n.splice(r,1),n}))),r=(0,f.useMemo)(()=>({register:n,slot:e.slot,name:e.name,props:e.props}),[n,e.slot,e.name,e.props]);return f.createElement(ed.Provider,{value:r},e.children)},[t])]}(),eh=(0,f.useMemo)(()=>[{dialogState:I,close:$,setTitleId:V},N],[I,N,$,V]),eE=(0,f.useMemo)(()=>({open:0===I}),[I]),ey={ref:R,id:u,role:m,"aria-modal":0===I||void 0,"aria-labelledby":N.titleId,"aria-describedby":ep};return f.createElement(es,{type:"Dialog",enabled:0===I,element:k,onUpdate:(0,v.z)((e,t)=>{"Dialog"===t&&(0,F.E)(e,{[eu.Add]:()=>b(e=>e+1),[eu.Remove]:()=>b(e=>e-1)})})},f.createElement(_,{force:!0},f.createElement(W,null,f.createElement(eg.Provider,{value:eh},f.createElement(W.Group,{target:k},f.createElement(_,{force:!1},f.createElement(ev,{slot:eE,name:"Dialog.Description"},f.createElement(D,{initialFocus:d,containers:G,features:z?(0,F.E)(B?"parent":"leaf",{parent:D.features.RestoreFocus,leaf:D.features.All&~D.features.FocusLock}):D.features.None},f.createElement(q,null,(0,C.sY)({ourProps:ey,theirProps:h,slot:eE,defaultTag:"div",features:ew,visible:0===I,name:"Dialog"}))))))))),f.createElement(Q,null))}),{Backdrop:(0,C.yV)(function(e,t){let n=(0,et.M)(),{id:r=`headlessui-dialog-backdrop-${n}`,...o}=e,[{dialogState:i},l]=eE("Dialog.Backdrop"),a=(0,S.T)(t);(0,f.useEffect)(()=>{if(null===l.panelRef.current)throw Error("A <Dialog.Backdrop /> component is being used, but a <Dialog.Panel /> component is missing.")},[l.panelRef]);let u=(0,f.useMemo)(()=>({open:0===i}),[i]);return f.createElement(_,{force:!0},f.createElement(W,null,(0,C.sY)({ourProps:{ref:a,id:r,"aria-hidden":!0},theirProps:o,slot:u,defaultTag:"div",name:"Dialog.Backdrop"})))}),Panel:(0,C.yV)(function(e,t){let n=(0,et.M)(),{id:r=`headlessui-dialog-panel-${n}`,...o}=e,[{dialogState:i},l]=eE("Dialog.Panel"),a=(0,S.T)(t,l.panelRef),u=(0,f.useMemo)(()=>({open:0===i}),[i]),s=(0,v.z)(e=>{e.stopPropagation()});return(0,C.sY)({ourProps:{ref:a,id:r,onClick:s},theirProps:o,slot:u,defaultTag:"div",name:"Dialog.Panel"})}),Overlay:(0,C.yV)(function(e,t){let n=(0,et.M)(),{id:r=`headlessui-dialog-overlay-${n}`,...o}=e,[{dialogState:i,close:l}]=eE("Dialog.Overlay"),a=(0,S.T)(t),u=(0,v.z)(e=>{if(e.target===e.currentTarget){if((0,ec.P)(e.currentTarget))return e.preventDefault();e.preventDefault(),e.stopPropagation(),l()}}),s=(0,f.useMemo)(()=>({open:0===i}),[i]);return(0,C.sY)({ourProps:{ref:a,id:r,"aria-hidden":!0,onClick:u},theirProps:o,slot:s,defaultTag:"div",name:"Dialog.Overlay"})}),Title:(0,C.yV)(function(e,t){let n=(0,et.M)(),{id:r=`headlessui-dialog-title-${n}`,...o}=e,[{dialogState:i,setTitleId:l}]=eE("Dialog.Title"),a=(0,S.T)(t);(0,f.useEffect)(()=>(l(r),()=>l(null)),[r,l]);let u=(0,f.useMemo)(()=>({open:0===i}),[i]);return(0,C.sY)({ourProps:{ref:a,id:r},theirProps:o,slot:u,defaultTag:"h2",name:"Dialog.Title"})}),Description:ef})},3850:function(e,t,n){"use strict";n.d(t,{R:function(){return o}});var r,o=((r=o||{}).Space=" ",r.Enter="Enter",r.Escape="Escape",r.Backspace="Backspace",r.Delete="Delete",r.ArrowLeft="ArrowLeft",r.ArrowUp="ArrowUp",r.ArrowRight="ArrowRight",r.ArrowDown="ArrowDown",r.Home="Home",r.End="End",r.PageUp="PageUp",r.PageDown="PageDown",r.Tab="Tab",r)},2926:function(e,t,n){"use strict";n.d(t,{v:function(){return $}});var r,o,i,l,a=n(2265),u=n(2769),s=n(2950),c=n(5606),d=n(8610),f=n(583),m=n(9888);function p(e){var t;if(e.type)return e.type;let n=null!=(t=e.as)?t:"button";if("string"==typeof n&&"button"===n.toLowerCase())return"button"}var v=n(6618);let h=/([\u2700-\u27BF]|[\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g;function g(e){var t,n;let r=null!=(t=e.innerText)?t:"",o=e.cloneNode(!0);if(!(o instanceof HTMLElement))return r;let i=!1;for(let e of o.querySelectorAll('[hidden],[aria-hidden],[role="img"]'))e.remove(),i=!0;let l=i?null!=(n=o.innerText)?n:"":r;return h.test(l)&&(l=l.replace(h,"")),l}function E(e){return[e.screenX,e.screenY]}var b=n(4851),w=n(5306),y=n(5863),T=((r=T||{})[r.First=0]="First",r[r.Previous=1]="Previous",r[r.Next=2]="Next",r[r.Last=3]="Last",r[r.Specific=4]="Specific",r[r.Nothing=5]="Nothing",r),S=n(5390),M=n(5410),k=n(597),R=n(1931),C=n(3850),L=((o=L||{})[o.Open=0]="Open",o[o.Closed=1]="Closed",o),x=((i=x||{})[i.Pointer=0]="Pointer",i[i.Other=1]="Other",i),O=((l=O||{})[l.OpenMenu=0]="OpenMenu",l[l.CloseMenu=1]="CloseMenu",l[l.GoToItem=2]="GoToItem",l[l.Search=3]="Search",l[l.ClearSearch=4]="ClearSearch",l[l.RegisterItem=5]="RegisterItem",l[l.UnregisterItem=6]="UnregisterItem",l);function P(e,t=e=>e){let n=null!==e.activeItemIndex?e.items[e.activeItemIndex]:null,r=(0,M.z2)(t(e.items.slice()),e=>e.dataRef.current.domRef.current),o=n?r.indexOf(n):null;return -1===o&&(o=null),{items:r,activeItemIndex:o}}let F={1:e=>1===e.menuState?e:{...e,activeItemIndex:null,menuState:1},0:e=>0===e.menuState?e:{...e,__demoMode:!1,menuState:0},2:(e,t)=>{var n;let r=P(e),o=function(e,t){let n=t.resolveItems();if(n.length<=0)return null;let r=t.resolveActiveIndex(),o=null!=r?r:-1;switch(e.focus){case 0:for(let e=0;e<n.length;++e)if(!t.resolveDisabled(n[e],e,n))return e;return r;case 1:for(let e=o-1;e>=0;--e)if(!t.resolveDisabled(n[e],e,n))return e;return r;case 2:for(let e=o+1;e<n.length;++e)if(!t.resolveDisabled(n[e],e,n))return e;return r;case 3:for(let e=n.length-1;e>=0;--e)if(!t.resolveDisabled(n[e],e,n))return e;return r;case 4:for(let r=0;r<n.length;++r)if(t.resolveId(n[r],r,n)===e.id)return r;return r;case 5:return null;default:!function(e){throw Error("Unexpected object: "+e)}(e)}}(t,{resolveItems:()=>r.items,resolveActiveIndex:()=>r.activeItemIndex,resolveId:e=>e.id,resolveDisabled:e=>e.dataRef.current.disabled});return{...e,...r,searchQuery:"",activeItemIndex:o,activationTrigger:null!=(n=t.trigger)?n:1}},3:(e,t)=>{let n=""!==e.searchQuery?0:1,r=e.searchQuery+t.value.toLowerCase(),o=(null!==e.activeItemIndex?e.items.slice(e.activeItemIndex+n).concat(e.items.slice(0,e.activeItemIndex+n)):e.items).find(e=>{var t;return(null==(t=e.dataRef.current.textValue)?void 0:t.startsWith(r))&&!e.dataRef.current.disabled}),i=o?e.items.indexOf(o):-1;return -1===i||i===e.activeItemIndex?{...e,searchQuery:r}:{...e,searchQuery:r,activeItemIndex:i,activationTrigger:1}},4:e=>""===e.searchQuery?e:{...e,searchQuery:"",searchActiveItemIndex:null},5:(e,t)=>{let n=P(e,e=>[...e,{id:t.id,dataRef:t.dataRef}]);return{...e,...n}},6:(e,t)=>{let n=P(e,e=>{let n=e.findIndex(e=>e.id===t.id);return -1!==n&&e.splice(n,1),e});return{...e,...n,activationTrigger:1}}},A=(0,a.createContext)(null);function I(e){let t=(0,a.useContext)(A);if(null===t){let t=Error(`<${e} /> is missing a parent <Menu /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,I),t}return t}function D(e,t){return(0,k.E)(t.type,F,e,t)}A.displayName="MenuContext";let N=a.Fragment,j=R.AN.RenderStrategy|R.AN.Static,H=a.Fragment,$=Object.assign((0,R.yV)(function(e,t){let{__demoMode:n=!1,...r}=e,o=(0,a.useReducer)(D,{__demoMode:n,menuState:n?0:1,buttonRef:(0,a.createRef)(),itemsRef:(0,a.createRef)(),items:[],searchQuery:"",activeItemIndex:null,activationTrigger:1}),[{menuState:i,itemsRef:l,buttonRef:u},c]=o,d=(0,v.T)(t);(0,f.O)([u,l],(e,t)=>{var n;c({type:1}),(0,M.sP)(t,M.tJ.Loose)||(e.preventDefault(),null==(n=u.current)||n.focus())},0===i);let m=(0,s.z)(()=>{c({type:1})}),p=(0,a.useMemo)(()=>({open:0===i,close:m}),[i,m]);return a.createElement(A.Provider,{value:o},a.createElement(w.up,{value:(0,k.E)(i,{0:w.ZM.Open,1:w.ZM.Closed})},(0,R.sY)({ourProps:{ref:d},theirProps:r,slot:p,defaultTag:N,name:"Menu"})))}),{Button:(0,R.yV)(function(e,t){var n;let r=(0,c.M)(),{id:o=`headlessui-menu-button-${r}`,...i}=e,[l,f]=I("Menu.Button"),m=(0,v.T)(l.buttonRef,t),h=(0,u.G)(),g=(0,s.z)(e=>{switch(e.key){case C.R.Space:case C.R.Enter:case C.R.ArrowDown:e.preventDefault(),e.stopPropagation(),f({type:0}),h.nextFrame(()=>f({type:2,focus:T.First}));break;case C.R.ArrowUp:e.preventDefault(),e.stopPropagation(),f({type:0}),h.nextFrame(()=>f({type:2,focus:T.Last}))}}),E=(0,s.z)(e=>{e.key===C.R.Space&&e.preventDefault()}),b=(0,s.z)(t=>{if((0,y.P)(t.currentTarget))return t.preventDefault();e.disabled||(0===l.menuState?(f({type:1}),h.nextFrame(()=>{var e;return null==(e=l.buttonRef.current)?void 0:e.focus({preventScroll:!0})})):(t.preventDefault(),f({type:0})))}),w=(0,a.useMemo)(()=>({open:0===l.menuState}),[l]),S={ref:m,id:o,type:function(e,t){let[n,r]=(0,a.useState)(()=>p(e));return(0,d.e)(()=>{r(p(e))},[e.type,e.as]),(0,d.e)(()=>{n||t.current&&t.current instanceof HTMLButtonElement&&!t.current.hasAttribute("type")&&r("button")},[n,t]),n}(e,l.buttonRef),"aria-haspopup":"menu","aria-controls":null==(n=l.itemsRef.current)?void 0:n.id,"aria-expanded":0===l.menuState,onKeyDown:g,onKeyUp:E,onClick:b};return(0,R.sY)({ourProps:S,theirProps:i,slot:w,defaultTag:"button",name:"Menu.Button"})}),Items:(0,R.yV)(function(e,t){var n,r;let o=(0,c.M)(),{id:i=`headlessui-menu-items-${o}`,...l}=e,[f,p]=I("Menu.Items"),h=(0,v.T)(f.itemsRef,t),g=(0,m.i)(f.itemsRef),E=(0,u.G)(),y=(0,w.oJ)(),k=null!==y?(y&w.ZM.Open)===w.ZM.Open:0===f.menuState;(0,a.useEffect)(()=>{let e=f.itemsRef.current;e&&0===f.menuState&&e!==(null==g?void 0:g.activeElement)&&e.focus({preventScroll:!0})},[f.menuState,f.itemsRef,g]),function({container:e,accept:t,walk:n,enabled:r=!0}){let o=(0,a.useRef)(t),i=(0,a.useRef)(n);(0,a.useEffect)(()=>{o.current=t,i.current=n},[t,n]),(0,d.e)(()=>{if(!e||!r)return;let t=(0,b.r)(e);if(!t)return;let n=o.current,l=i.current,a=Object.assign(e=>n(e),{acceptNode:n}),u=t.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,a,!1);for(;u.nextNode();)l(u.currentNode)},[e,r,o,i])}({container:f.itemsRef.current,enabled:0===f.menuState,accept:e=>"menuitem"===e.getAttribute("role")?NodeFilter.FILTER_REJECT:e.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT,walk(e){e.setAttribute("role","none")}});let L=(0,s.z)(e=>{var t,n;switch(E.dispose(),e.key){case C.R.Space:if(""!==f.searchQuery)return e.preventDefault(),e.stopPropagation(),p({type:3,value:e.key});case C.R.Enter:if(e.preventDefault(),e.stopPropagation(),p({type:1}),null!==f.activeItemIndex){let{dataRef:e}=f.items[f.activeItemIndex];null==(n=null==(t=e.current)?void 0:t.domRef.current)||n.click()}(0,M.wI)(f.buttonRef.current);break;case C.R.ArrowDown:return e.preventDefault(),e.stopPropagation(),p({type:2,focus:T.Next});case C.R.ArrowUp:return e.preventDefault(),e.stopPropagation(),p({type:2,focus:T.Previous});case C.R.Home:case C.R.PageUp:return e.preventDefault(),e.stopPropagation(),p({type:2,focus:T.First});case C.R.End:case C.R.PageDown:return e.preventDefault(),e.stopPropagation(),p({type:2,focus:T.Last});case C.R.Escape:e.preventDefault(),e.stopPropagation(),p({type:1}),(0,S.k)().nextFrame(()=>{var e;return null==(e=f.buttonRef.current)?void 0:e.focus({preventScroll:!0})});break;case C.R.Tab:e.preventDefault(),e.stopPropagation(),p({type:1}),(0,S.k)().nextFrame(()=>{(0,M.EO)(f.buttonRef.current,e.shiftKey?M.TO.Previous:M.TO.Next)});break;default:1===e.key.length&&(p({type:3,value:e.key}),E.setTimeout(()=>p({type:4}),350))}}),x=(0,s.z)(e=>{e.key===C.R.Space&&e.preventDefault()}),O=(0,a.useMemo)(()=>({open:0===f.menuState}),[f]),P={"aria-activedescendant":null===f.activeItemIndex||null==(n=f.items[f.activeItemIndex])?void 0:n.id,"aria-labelledby":null==(r=f.buttonRef.current)?void 0:r.id,id:i,onKeyDown:L,onKeyUp:x,role:"menu",tabIndex:0,ref:h};return(0,R.sY)({ourProps:P,theirProps:l,slot:O,defaultTag:"div",features:j,visible:k,name:"Menu.Items"})}),Item:(0,R.yV)(function(e,t){let n,r,o,i=(0,c.M)(),{id:l=`headlessui-menu-item-${i}`,disabled:u=!1,...f}=e,[m,p]=I("Menu.Item"),h=null!==m.activeItemIndex&&m.items[m.activeItemIndex].id===l,b=(0,a.useRef)(null),w=(0,v.T)(t,b);(0,d.e)(()=>{if(m.__demoMode||0!==m.menuState||!h||0===m.activationTrigger)return;let e=(0,S.k)();return e.requestAnimationFrame(()=>{var e,t;null==(t=null==(e=b.current)?void 0:e.scrollIntoView)||t.call(e,{block:"nearest"})}),e.dispose},[m.__demoMode,b,h,m.menuState,m.activationTrigger,m.activeItemIndex]);let y=(n=(0,a.useRef)(""),r=(0,a.useRef)(""),(0,s.z)(()=>{let e=b.current;if(!e)return"";let t=e.innerText;if(n.current===t)return r.current;let o=(function(e){let t=e.getAttribute("aria-label");if("string"==typeof t)return t.trim();let n=e.getAttribute("aria-labelledby");if(n){let e=n.split(" ").map(e=>{let t=document.getElementById(e);if(t){let e=t.getAttribute("aria-label");return"string"==typeof e?e.trim():g(t).trim()}return null}).filter(Boolean);if(e.length>0)return e.join(", ")}return g(e).trim()})(e).trim().toLowerCase();return n.current=t,r.current=o,o})),k=(0,a.useRef)({disabled:u,domRef:b,get textValue(){return y()}});(0,d.e)(()=>{k.current.disabled=u},[k,u]),(0,d.e)(()=>(p({type:5,id:l,dataRef:k}),()=>p({type:6,id:l})),[k,l]);let C=(0,s.z)(()=>{p({type:1})}),L=(0,s.z)(e=>{if(u)return e.preventDefault();p({type:1}),(0,M.wI)(m.buttonRef.current)}),x=(0,s.z)(()=>{if(u)return p({type:2,focus:T.Nothing});p({type:2,focus:T.Specific,id:l})}),O=(o=(0,a.useRef)([-1,-1]),{wasMoved(e){let t=E(e);return(o.current[0]!==t[0]||o.current[1]!==t[1])&&(o.current=t,!0)},update(e){o.current=E(e)}}),P=(0,s.z)(e=>O.update(e)),F=(0,s.z)(e=>{O.wasMoved(e)&&(u||h||p({type:2,focus:T.Specific,id:l,trigger:0}))}),A=(0,s.z)(e=>{O.wasMoved(e)&&(u||h&&p({type:2,focus:T.Nothing}))}),D=(0,a.useMemo)(()=>({active:h,disabled:u,close:C}),[h,u,C]);return(0,R.sY)({ourProps:{id:l,ref:w,role:"menuitem",tabIndex:!0===u?void 0:-1,"aria-disabled":!0===u||void 0,disabled:void 0,onClick:L,onFocus:x,onPointerEnter:P,onMouseEnter:P,onPointerMove:F,onMouseMove:F,onPointerLeave:A,onMouseLeave:A},theirProps:f,slot:D,defaultTag:H,name:"Menu.Item"})})})},9805:function(e,t,n){"use strict";n.d(t,{u:function(){return F}});var r,o=n(2265),i=n(2769),l=n(2950),a=n(634),u=n(8610),s=n(1858),c=n(8957),d=n(6618),f=n(5390),m=n(597);function p(e,...t){e&&t.length>0&&e.classList.add(...t)}function v(e,...t){e&&t.length>0&&e.classList.remove(...t)}var h=n(5306),g=n(3960),E=n(1931);function b(e=""){return e.split(/\s+/).filter(e=>e.length>1)}let w=(0,o.createContext)(null);w.displayName="TransitionContext";var y=((r=y||{}).Visible="visible",r.Hidden="hidden",r);let T=(0,o.createContext)(null);function S(e){return"children"in e?S(e.children):e.current.filter(({el:e})=>null!==e.current).filter(({state:e})=>"visible"===e).length>0}function M(e,t){let n=(0,s.E)(e),r=(0,o.useRef)([]),u=(0,a.t)(),c=(0,i.G)(),d=(0,l.z)((e,t=E.l4.Hidden)=>{let o=r.current.findIndex(({el:t})=>t===e);-1!==o&&((0,m.E)(t,{[E.l4.Unmount](){r.current.splice(o,1)},[E.l4.Hidden](){r.current[o].state="hidden"}}),c.microTask(()=>{var e;!S(r)&&u.current&&(null==(e=n.current)||e.call(n))}))}),f=(0,l.z)(e=>{let t=r.current.find(({el:t})=>t===e);return t?"visible"!==t.state&&(t.state="visible"):r.current.push({el:e,state:"visible"}),()=>d(e,E.l4.Unmount)}),p=(0,o.useRef)([]),v=(0,o.useRef)(Promise.resolve()),h=(0,o.useRef)({enter:[],leave:[],idle:[]}),g=(0,l.z)((e,n,r)=>{p.current.splice(0),t&&(t.chains.current[n]=t.chains.current[n].filter(([t])=>t!==e)),null==t||t.chains.current[n].push([e,new Promise(e=>{p.current.push(e)})]),null==t||t.chains.current[n].push([e,new Promise(e=>{Promise.all(h.current[n].map(([e,t])=>t)).then(()=>e())})]),"enter"===n?v.current=v.current.then(()=>null==t?void 0:t.wait.current).then(()=>r(n)):r(n)}),b=(0,l.z)((e,t,n)=>{Promise.all(h.current[t].splice(0).map(([e,t])=>t)).then(()=>{var e;null==(e=p.current.shift())||e()}).then(()=>n(t))});return(0,o.useMemo)(()=>({children:r,register:f,unregister:d,onStart:g,onStop:b,wait:v,chains:h}),[f,d,r,g,b,h,v])}function k(){}T.displayName="NestingContext";let R=["beforeEnter","afterEnter","beforeLeave","afterLeave"];function C(e){var t;let n={};for(let r of R)n[r]=null!=(t=e[r])?t:k;return n}let L=E.AN.RenderStrategy,x=(0,E.yV)(function(e,t){let{show:n,appear:r=!1,unmount:i=!0,...a}=e,s=(0,o.useRef)(null),f=(0,d.T)(s,t);(0,c.H)();let m=(0,h.oJ)();if(void 0===n&&null!==m&&(n=(m&h.ZM.Open)===h.ZM.Open),![!0,!1].includes(n))throw Error("A <Transition /> is used but it is missing a `show={true | false}` prop.");let[p,v]=(0,o.useState)(n?"visible":"hidden"),g=M(()=>{v("hidden")}),[b,y]=(0,o.useState)(!0),k=(0,o.useRef)([n]);(0,u.e)(()=>{!1!==b&&k.current[k.current.length-1]!==n&&(k.current.push(n),y(!1))},[k,n]);let R=(0,o.useMemo)(()=>({show:n,appear:r,initial:b}),[n,r,b]);(0,o.useEffect)(()=>{if(n)v("visible");else if(S(g)){let e=s.current;if(!e)return;let t=e.getBoundingClientRect();0===t.x&&0===t.y&&0===t.width&&0===t.height&&v("hidden")}else v("hidden")},[n,g]);let C={unmount:i},x=(0,l.z)(()=>{var t;b&&y(!1),null==(t=e.beforeEnter)||t.call(e)}),P=(0,l.z)(()=>{var t;b&&y(!1),null==(t=e.beforeLeave)||t.call(e)});return o.createElement(T.Provider,{value:g},o.createElement(w.Provider,{value:R},(0,E.sY)({ourProps:{...C,as:o.Fragment,children:o.createElement(O,{ref:f,...C,...a,beforeEnter:x,beforeLeave:P})},theirProps:{},defaultTag:o.Fragment,features:L,visible:"visible"===p,name:"Transition"})))}),O=(0,E.yV)(function(e,t){var n,r,y;let k;let{beforeEnter:R,afterEnter:x,beforeLeave:O,afterLeave:P,enter:F,enterFrom:A,enterTo:I,entered:D,leave:N,leaveFrom:j,leaveTo:H,...$}=e,_=(0,o.useRef)(null),V=(0,d.T)(_,t),z=null==(n=$.unmount)||n?E.l4.Unmount:E.l4.Hidden,{show:B,appear:Z,initial:Y}=function(){let e=(0,o.useContext)(w);if(null===e)throw Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),[W,U]=(0,o.useState)(B?"visible":"hidden"),q=function(){let e=(0,o.useContext)(T);if(null===e)throw Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),{register:G,unregister:K}=q;(0,o.useEffect)(()=>G(_),[G,_]),(0,o.useEffect)(()=>{if(z===E.l4.Hidden&&_.current){if(B&&"visible"!==W){U("visible");return}return(0,m.E)(W,{hidden:()=>K(_),visible:()=>G(_)})}},[W,_,G,K,B,z]);let J=(0,s.E)({base:b($.className),enter:b(F),enterFrom:b(A),enterTo:b(I),entered:b(D),leave:b(N),leaveFrom:b(j),leaveTo:b(H)}),Q=(y={beforeEnter:R,afterEnter:x,beforeLeave:O,afterLeave:P},k=(0,o.useRef)(C(y)),(0,o.useEffect)(()=>{k.current=C(y)},[y]),k),X=(0,c.H)();(0,o.useEffect)(()=>{if(X&&"visible"===W&&null===_.current)throw Error("Did you forget to passthrough the `ref` to the actual DOM node?")},[_,W,X]);let ee=Z&&B&&Y,et=X&&(!Y||Z)?B?"enter":"leave":"idle",en=function(e=0){let[t,n]=(0,o.useState)(e),r=(0,a.t)(),i=(0,o.useCallback)(e=>{r.current&&n(t=>t|e)},[t,r]),l=(0,o.useCallback)(e=>!!(t&e),[t]);return{flags:t,addFlag:i,hasFlag:l,removeFlag:(0,o.useCallback)(e=>{r.current&&n(t=>t&~e)},[n,r]),toggleFlag:(0,o.useCallback)(e=>{r.current&&n(t=>t^e)},[n])}}(0),er=(0,l.z)(e=>(0,m.E)(e,{enter:()=>{en.addFlag(h.ZM.Opening),Q.current.beforeEnter()},leave:()=>{en.addFlag(h.ZM.Closing),Q.current.beforeLeave()},idle:()=>{}})),eo=(0,l.z)(e=>(0,m.E)(e,{enter:()=>{en.removeFlag(h.ZM.Opening),Q.current.afterEnter()},leave:()=>{en.removeFlag(h.ZM.Closing),Q.current.afterLeave()},idle:()=>{}})),ei=M(()=>{U("hidden"),K(_)},q),el=(0,o.useRef)(!1);!function({immediate:e,container:t,direction:n,classes:r,onStart:o,onStop:l}){let c=(0,a.t)(),d=(0,i.G)(),h=(0,s.E)(n);(0,u.e)(()=>{e&&(h.current="enter")},[e]),(0,u.e)(()=>{let e=(0,f.k)();d.add(e.dispose);let n=t.current;if(n&&"idle"!==h.current&&c.current){var i,a,u;let t,s,c,d,g,E,b;return e.dispose(),o.current(h.current),e.add((i=r.current,a="enter"===h.current,u=()=>{e.dispose(),l.current(h.current)},s=a?"enter":"leave",c=(0,f.k)(),d=void 0!==u?(t={called:!1},(...e)=>{if(!t.called)return t.called=!0,u(...e)}):()=>{},"enter"===s&&(n.removeAttribute("hidden"),n.style.display=""),g=(0,m.E)(s,{enter:()=>i.enter,leave:()=>i.leave}),E=(0,m.E)(s,{enter:()=>i.enterTo,leave:()=>i.leaveTo}),b=(0,m.E)(s,{enter:()=>i.enterFrom,leave:()=>i.leaveFrom}),v(n,...i.base,...i.enter,...i.enterTo,...i.enterFrom,...i.leave,...i.leaveFrom,...i.leaveTo,...i.entered),p(n,...i.base,...g,...b),c.nextFrame(()=>{v(n,...i.base,...g,...b),p(n,...i.base,...g,...E),function(e,t){let n=(0,f.k)();if(!e)return n.dispose;let{transitionDuration:r,transitionDelay:o}=getComputedStyle(e),[i,l]=[r,o].map(e=>{let[t=0]=e.split(",").filter(Boolean).map(e=>e.includes("ms")?parseFloat(e):1e3*parseFloat(e)).sort((e,t)=>t-e);return t}),a=i+l;if(0!==a){n.group(n=>{n.setTimeout(()=>{t(),n.dispose()},a),n.addEventListener(e,"transitionrun",e=>{e.target===e.currentTarget&&n.dispose()})});let r=n.addEventListener(e,"transitionend",e=>{e.target===e.currentTarget&&(t(),r())})}else t();n.add(()=>t()),n.dispose}(n,()=>(v(n,...i.base,...g),p(n,...i.base,...i.entered),d()))}),c.dispose)),e.dispose}},[n])}({immediate:ee,container:_,classes:J,direction:et,onStart:(0,s.E)(e=>{el.current=!0,ei.onStart(_,e,er)}),onStop:(0,s.E)(e=>{el.current=!1,ei.onStop(_,e,eo),"leave"!==e||S(ei)||(U("hidden"),K(_))})});let ea=$;return ee?ea={...ea,className:(0,g.A)($.className,...J.current.enter,...J.current.enterFrom)}:el.current&&(ea.className=(0,g.A)($.className,null==(r=_.current)?void 0:r.className),""===ea.className&&delete ea.className),o.createElement(T.Provider,{value:ei},o.createElement(h.up,{value:(0,m.E)(W,{visible:h.ZM.Open,hidden:h.ZM.Closed})|en.flags},(0,E.sY)({ourProps:{ref:V},theirProps:ea,defaultTag:"div",features:L,visible:"visible"===W,name:"Transition.Child"})))}),P=(0,E.yV)(function(e,t){let n=null!==(0,o.useContext)(w),r=null!==(0,h.oJ)();return o.createElement(o.Fragment,null,!n&&r?o.createElement(x,{ref:t,...e}):o.createElement(O,{ref:t,...e}))}),F=Object.assign(x,{Child:P,Root:x})},2769:function(e,t,n){"use strict";n.d(t,{G:function(){return i}});var r=n(2265),o=n(5390);function i(){let[e]=(0,r.useState)(o.k);return(0,r.useEffect)(()=>()=>e.dispose(),[e]),e}},2950:function(e,t,n){"use strict";n.d(t,{z:function(){return i}});var r=n(2265),o=n(1858);let i=function(e){let t=(0,o.E)(e);return r.useCallback((...e)=>t.current(...e),[t])}},5606:function(e,t,n){"use strict";n.d(t,{M:function(){return u}});var r,o=n(2265),i=n(2057),l=n(8610),a=n(8957);let u=null!=(r=o.useId)?r:function(){let e=(0,a.H)(),[t,n]=o.useState(e?()=>i.O.nextId():null);return(0,l.e)(()=>{null===t&&n(i.O.nextId())},[t]),null!=t?""+t:void 0}},634:function(e,t,n){"use strict";n.d(t,{t:function(){return i}});var r=n(2265),o=n(8610);function i(){let e=(0,r.useRef)(!1);return(0,o.e)(()=>(e.current=!0,()=>{e.current=!1}),[]),e}},8610:function(e,t,n){"use strict";n.d(t,{e:function(){return i}});var r=n(2265),o=n(2057);let i=(e,t)=>{o.O.isServer?(0,r.useEffect)(e,t):(0,r.useLayoutEffect)(e,t)}},1858:function(e,t,n){"use strict";n.d(t,{E:function(){return i}});var r=n(2265),o=n(8610);function i(e){let t=(0,r.useRef)(e);return(0,o.e)(()=>{t.current=e},[e]),t}},583:function(e,t,n){"use strict";n.d(t,{O:function(){return s}});var r=n(2265),o=n(5410),i=n(4644),l=n(1858);function a(e,t,n){let o=(0,l.E)(t);(0,r.useEffect)(()=>{function t(e){o.current(e)}return document.addEventListener(e,t,n),()=>document.removeEventListener(e,t,n)},[e,n])}var u=n(7976);function s(e,t,n=!0){let l=(0,r.useRef)(!1);function s(n,r){if(!l.current||n.defaultPrevented)return;let i=r(n);if(null!==i&&i.getRootNode().contains(i)&&i.isConnected){for(let t of function e(t){return"function"==typeof t?e(t()):Array.isArray(t)||t instanceof Set?t:[t]}(e)){if(null===t)continue;let e=t instanceof HTMLElement?t:t.current;if(null!=e&&e.contains(i)||n.composed&&n.composedPath().includes(e))return}return(0,o.sP)(i,o.tJ.Loose)||-1===i.tabIndex||n.preventDefault(),t(n,i)}}(0,r.useEffect)(()=>{requestAnimationFrame(()=>{l.current=n})},[n]);let c=(0,r.useRef)(null);a("pointerdown",e=>{var t,n;l.current&&(c.current=(null==(n=null==(t=e.composedPath)?void 0:t.call(e))?void 0:n[0])||e.target)},!0),a("mousedown",e=>{var t,n;l.current&&(c.current=(null==(n=null==(t=e.composedPath)?void 0:t.call(e))?void 0:n[0])||e.target)},!0),a("click",e=>{(0,i.tq)()||c.current&&(s(e,()=>c.current),c.current=null)},!0),a("touchend",e=>s(e,()=>e.target instanceof HTMLElement?e.target:null),!0),(0,u.s)("blur",e=>s(e,()=>window.document.activeElement instanceof HTMLIFrameElement?window.document.activeElement:null),!0)}},9888:function(e,t,n){"use strict";n.d(t,{i:function(){return i}});var r=n(2265),o=n(4851);function i(...e){return(0,r.useMemo)(()=>(0,o.r)(...e),[...e])}},8957:function(e,t,n){"use strict";n.d(t,{H:function(){return l}});var r,o=n(2265),i=n(2057);function l(){let e;let t=(e="undefined"==typeof document,(0,(r||(r=n.t(o,2))).useSyncExternalStore)(()=>()=>{},()=>!1,()=>!e)),[l,a]=o.useState(i.O.isHandoffComplete);return l&&!1===i.O.isHandoffComplete&&a(!1),o.useEffect(()=>{!0!==l&&a(!0)},[l]),o.useEffect(()=>i.O.handoff(),[]),!t&&l}},6618:function(e,t,n){"use strict";n.d(t,{T:function(){return a},h:function(){return l}});var r=n(2265),o=n(2950);let i=Symbol();function l(e,t=!0){return Object.assign(e,{[i]:t})}function a(...e){let t=(0,r.useRef)(e);(0,r.useEffect)(()=>{t.current=e},[e]);let n=(0,o.z)(e=>{for(let n of t.current)null!=n&&("function"==typeof n?n(e):n.current=e)});return e.every(e=>null==e||(null==e?void 0:e[i]))?void 0:n}},7976:function(e,t,n){"use strict";n.d(t,{s:function(){return i}});var r=n(2265),o=n(1858);function i(e,t,n){let i=(0,o.E)(t);(0,r.useEffect)(()=>{function t(e){i.current(e)}return window.addEventListener(e,t,n),()=>window.removeEventListener(e,t,n)},[e,n])}},5306:function(e,t,n){"use strict";n.d(t,{ZM:function(){return l},oJ:function(){return a},up:function(){return u}});var r,o=n(2265);let i=(0,o.createContext)(null);i.displayName="OpenClosedContext";var l=((r=l||{})[r.Open=1]="Open",r[r.Closed=2]="Closed",r[r.Closing=4]="Closing",r[r.Opening=8]="Opening",r);function a(){return(0,o.useContext)(i)}function u({value:e,children:t}){return o.createElement(i.Provider,{value:e},t)}},5863:function(e,t,n){"use strict";function r(e){let t=e.parentElement,n=null;for(;t&&!(t instanceof HTMLFieldSetElement);)t instanceof HTMLLegendElement&&(n=t),t=t.parentElement;let r=(null==t?void 0:t.getAttribute("disabled"))==="";return!(r&&function(e){if(!e)return!1;let t=e.previousElementSibling;for(;null!==t;){if(t instanceof HTMLLegendElement)return!1;t=t.previousElementSibling}return!0}(n))&&r}n.d(t,{P:function(){return r}})},3960:function(e,t,n){"use strict";function r(...e){return Array.from(new Set(e.flatMap(e=>"string"==typeof e?e.split(" "):[]))).filter(Boolean).join(" ")}n.d(t,{A:function(){return r}})},5390:function(e,t,n){"use strict";n.d(t,{k:function(){return function e(){let t=[],n={addEventListener:(e,t,r,o)=>(e.addEventListener(t,r,o),n.add(()=>e.removeEventListener(t,r,o))),requestAnimationFrame(...e){let t=requestAnimationFrame(...e);return n.add(()=>cancelAnimationFrame(t))},nextFrame:(...e)=>n.requestAnimationFrame(()=>n.requestAnimationFrame(...e)),setTimeout(...e){let t=setTimeout(...e);return n.add(()=>clearTimeout(t))},microTask(...e){let t={current:!0};return(0,r.Y)(()=>{t.current&&e[0]()}),n.add(()=>{t.current=!1})},style(e,t,n){let r=e.style.getPropertyValue(t);return Object.assign(e.style,{[t]:n}),this.add(()=>{Object.assign(e.style,{[t]:r})})},group(t){let n=e();return t(n),this.add(()=>n.dispose())},add:e=>(t.push(e),()=>{let n=t.indexOf(e);if(n>=0)for(let e of t.splice(n,1))e()}),dispose(){for(let e of t.splice(0))e()}};return n}}});var r=n(5195)},2057:function(e,t,n){"use strict";n.d(t,{O:function(){return a}});var r=Object.defineProperty,o=(e,t,n)=>t in e?r(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,i=(e,t,n)=>(o(e,"symbol"!=typeof t?t+"":t,n),n);class l{constructor(){i(this,"current",this.detect()),i(this,"handoffState","pending"),i(this,"currentId",0)}set(e){this.current!==e&&(this.handoffState="pending",this.currentId=0,this.current=e)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return"server"===this.current}get isClient(){return"client"===this.current}detect(){return"undefined"==typeof window||"undefined"==typeof document?"server":"client"}handoff(){"pending"===this.handoffState&&(this.handoffState="complete")}get isHandoffComplete(){return"complete"===this.handoffState}}let a=new l},5410:function(e,t,n){"use strict";n.d(t,{C5:function(){return w},EO:function(){return T},TO:function(){return f},fE:function(){return m},jA:function(){return S},sP:function(){return g},tJ:function(){return h},wI:function(){return E},z2:function(){return y}});var r,o,i,l,a,u=n(5390),s=n(597),c=n(4851);let d=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map(e=>`${e}:not([tabindex='-1'])`).join(",");var f=((r=f||{})[r.First=1]="First",r[r.Previous=2]="Previous",r[r.Next=4]="Next",r[r.Last=8]="Last",r[r.WrapAround=16]="WrapAround",r[r.NoScroll=32]="NoScroll",r),m=((o=m||{})[o.Error=0]="Error",o[o.Overflow=1]="Overflow",o[o.Success=2]="Success",o[o.Underflow=3]="Underflow",o),p=((i=p||{})[i.Previous=-1]="Previous",i[i.Next=1]="Next",i);function v(e=document.body){return null==e?[]:Array.from(e.querySelectorAll(d)).sort((e,t)=>Math.sign((e.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}var h=((l=h||{})[l.Strict=0]="Strict",l[l.Loose=1]="Loose",l);function g(e,t=0){var n;return e!==(null==(n=(0,c.r)(e))?void 0:n.body)&&(0,s.E)(t,{0:()=>e.matches(d),1(){let t=e;for(;null!==t;){if(t.matches(d))return!0;t=t.parentElement}return!1}})}function E(e){let t=(0,c.r)(e);(0,u.k)().nextFrame(()=>{t&&!g(t.activeElement,0)&&w(e)})}var b=((a=b||{})[a.Keyboard=0]="Keyboard",a[a.Mouse=1]="Mouse",a);function w(e){null==e||e.focus({preventScroll:!0})}function y(e,t=e=>e){return e.slice().sort((e,n)=>{let r=t(e),o=t(n);if(null===r||null===o)return 0;let i=r.compareDocumentPosition(o);return i&Node.DOCUMENT_POSITION_FOLLOWING?-1:i&Node.DOCUMENT_POSITION_PRECEDING?1:0})}function T(e,t){return S(v(),t,{relativeTo:e})}function S(e,t,{sorted:n=!0,relativeTo:r=null,skipElements:o=[]}={}){var i,l,a;let u=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e.ownerDocument,s=Array.isArray(e)?n?y(e):e:v(e);o.length>0&&s.length>1&&(s=s.filter(e=>!o.includes(e))),r=null!=r?r:u.activeElement;let c=(()=>{if(5&t)return 1;if(10&t)return -1;throw Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),d=(()=>{if(1&t)return 0;if(2&t)return Math.max(0,s.indexOf(r))-1;if(4&t)return Math.max(0,s.indexOf(r))+1;if(8&t)return s.length-1;throw Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),f=32&t?{preventScroll:!0}:{},m=0,p=s.length,h;do{if(m>=p||m+p<=0)return 0;let e=d+m;if(16&t)e=(e+p)%p;else{if(e<0)return 3;if(e>=p)return 1}null==(h=s[e])||h.focus(f),m+=c}while(h!==u.activeElement);return 6&t&&null!=(a=null==(l=null==(i=h)?void 0:i.matches)?void 0:l.call(i,"textarea,input"))&&a&&h.select(),2}"undefined"!=typeof window&&"undefined"!=typeof document&&(document.addEventListener("keydown",e=>{e.metaKey||e.altKey||e.ctrlKey||(document.documentElement.dataset.headlessuiFocusVisible="")},!0),document.addEventListener("click",e=>{1===e.detail?delete document.documentElement.dataset.headlessuiFocusVisible:0===e.detail&&(document.documentElement.dataset.headlessuiFocusVisible="")},!0))},597:function(e,t,n){"use strict";function r(e,t,...n){if(e in t){let r=t[e];return"function"==typeof r?r(...n):r}let o=Error(`Tried to handle "${e}" but there is no handler defined. Only defined handlers are: ${Object.keys(t).map(e=>`"${e}"`).join(", ")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(o,r),o}n.d(t,{E:function(){return r}})},5195:function(e,t,n){"use strict";function r(e){"function"==typeof queueMicrotask?queueMicrotask(e):Promise.resolve().then(e).catch(e=>setTimeout(()=>{throw e}))}n.d(t,{Y:function(){return r}})},4851:function(e,t,n){"use strict";n.d(t,{r:function(){return o}});var r=n(2057);function o(e){return r.O.isServer?null:e instanceof Node?e.ownerDocument:null!=e&&e.hasOwnProperty("current")&&e.current instanceof Node?e.current.ownerDocument:document}},4644:function(e,t,n){"use strict";function r(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function o(){return r()||/Android/gi.test(window.navigator.userAgent)}n.d(t,{gn:function(){return r},tq:function(){return o}})},1931:function(e,t,n){"use strict";n.d(t,{AN:function(){return u},l4:function(){return s},sY:function(){return c},yV:function(){return p}});var r,o,i=n(2265),l=n(3960),a=n(597),u=((r=u||{})[r.None=0]="None",r[r.RenderStrategy=1]="RenderStrategy",r[r.Static=2]="Static",r),s=((o=s||{})[o.Unmount=0]="Unmount",o[o.Hidden=1]="Hidden",o);function c({ourProps:e,theirProps:t,slot:n,defaultTag:r,features:o,visible:i=!0,name:l,mergeRefs:u}){u=null!=u?u:f;let s=m(t,e);if(i)return d(s,n,r,l,u);let c=null!=o?o:0;if(2&c){let{static:e=!1,...t}=s;if(e)return d(t,n,r,l,u)}if(1&c){let{unmount:e=!0,...t}=s;return(0,a.E)(e?0:1,{0:()=>null,1:()=>d({...t,hidden:!0,style:{display:"none"}},n,r,l,u)})}return d(s,n,r,l,u)}function d(e,t={},n,r,o){let{as:a=n,children:u,refName:s="ref",...c}=h(e,["unmount","static"]),d=void 0!==e.ref?{[s]:e.ref}:{},f="function"==typeof u?u(t):u;"className"in c&&c.className&&"function"==typeof c.className&&(c.className=c.className(t));let p={};if(t){let e=!1,n=[];for(let[r,o]of Object.entries(t))"boolean"==typeof o&&(e=!0),!0===o&&n.push(r);e&&(p["data-headlessui-state"]=n.join(" "))}if(a===i.Fragment&&Object.keys(v(c)).length>0){if(!(0,i.isValidElement)(f)||Array.isArray(f)&&f.length>1)throw Error(['Passing props on "Fragment"!',"",`The current component <${r} /> is rendering a "Fragment".`,"However we need to passthrough the following props:",Object.keys(c).map(e=>`  - ${e}`).join(`
`),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".',"Render a single element as the child so that we can forward the props onto that element."].map(e=>`  - ${e}`).join(`
`)].join(`
`));let e=f.props,t="function"==typeof(null==e?void 0:e.className)?(...t)=>(0,l.A)(null==e?void 0:e.className(...t),c.className):(0,l.A)(null==e?void 0:e.className,c.className);return(0,i.cloneElement)(f,Object.assign({},m(f.props,v(h(c,["ref"]))),p,d,{ref:o(f.ref,d.ref)},t?{className:t}:{}))}return(0,i.createElement)(a,Object.assign({},h(c,["ref"]),a!==i.Fragment&&d,a!==i.Fragment&&p),f)}function f(...e){return e.every(e=>null==e)?void 0:t=>{for(let n of e)null!=n&&("function"==typeof n?n(t):n.current=t)}}function m(...e){if(0===e.length)return{};if(1===e.length)return e[0];let t={},n={};for(let r of e)for(let e in r)e.startsWith("on")&&"function"==typeof r[e]?(null!=n[e]||(n[e]=[]),n[e].push(r[e])):t[e]=r[e];if(t.disabled||t["aria-disabled"])return Object.assign(t,Object.fromEntries(Object.keys(n).map(e=>[e,void 0])));for(let e in n)Object.assign(t,{[e](t,...r){for(let o of n[e]){if((t instanceof Event||(null==t?void 0:t.nativeEvent)instanceof Event)&&t.defaultPrevented)return;o(t,...r)}}});return t}function p(e){var t;return Object.assign((0,i.forwardRef)(e),{displayName:null!=(t=e.displayName)?t:e.name})}function v(e){let t=Object.assign({},e);for(let e in t)void 0===t[e]&&delete t[e];return t}function h(e,t=[]){let n=Object.assign({},e);for(let e of t)e in n&&delete n[e];return n}},7865:function(e,t,n){"use strict";var r=n(2265);let o=r.forwardRef(function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"}))});t.Z=o},1861:function(e,t,n){"use strict";var r=n(2265);let o=r.forwardRef(function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0"}))});t.Z=o},3339:function(e,t,n){"use strict";var r=n(2265);let o=r.forwardRef(function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M20.25 14.15v4.25c0 1.094-.787 2.036-1.872 2.18-2.087.277-4.216.42-6.378.42s-4.291-.143-6.378-.42c-1.085-.144-1.872-1.086-1.872-2.18v-4.25m16.5 0a2.18 2.18 0 0 0 .75-1.661V8.706c0-1.081-.768-2.015-1.837-2.175a48.114 48.114 0 0 0-3.413-.387m4.5 8.006c-.194.165-.42.295-.673.38A23.978 23.978 0 0 1 12 15.75c-2.648 0-5.195-.429-7.577-1.22a2.016 2.016 0 0 1-.673-.38m0 0A2.18 2.18 0 0 1 3 12.489V8.706c0-1.081.768-2.015 1.837-2.175a48.111 48.111 0 0 1 3.413-.387m7.5 0V5.25A2.25 2.25 0 0 0 13.5 3h-3a2.25 2.25 0 0 0-2.25 2.25v.894m7.5 0a48.667 48.667 0 0 0-7.5 0M12 12.75h.008v.008H12v-.008Z"}))});t.Z=o},6233:function(e,t,n){"use strict";var r=n(2265);let o=r.forwardRef(function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5"}))});t.Z=o},8447:function(e,t,n){"use strict";var r=n(2265);let o=r.forwardRef(function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"}))});t.Z=o},9776:function(e,t,n){"use strict";var r=n(2265);let o=r.forwardRef(function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3-3c-1.354 0-2.694-.055-4.02-.163a2.115 2.115 0 0 1-.825-.242m9.345-8.334a2.126 2.126 0 0 0-.476-.095 48.64 48.64 0 0 0-8.048 0c-1.131.094-1.976 1.057-1.976 2.192v4.286c0 .837.46 1.58 1.155 1.951m9.345-8.334V6.637c0-1.621-1.152-3.026-2.76-3.235A48.455 48.455 0 0 0 11.25 3c-2.115 0-4.198.137-6.24.402-1.608.209-2.76 1.614-2.76 3.235v6.226c0 1.621 1.152 3.026 2.76 3.235.577.075 1.157.14 1.74.194V21l4.155-4.155"}))});t.Z=o},7714:function(e,t,n){"use strict";var r=n(2265);let o=r.forwardRef(function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.5 12a7.5 7.5 0 0 0 15 0m-15 0a7.5 7.5 0 1 1 15 0m-15 0H3m16.5 0H21m-1.5 0H12m-8.457 3.077 1.41-.513m14.095-5.13 1.41-.513M5.106 17.785l1.15-.964m11.49-9.642 1.149-.964M7.501 19.795l.75-1.3m7.5-12.99.75-1.3m-6.063 16.658.26-1.477m2.605-14.772.26-1.477m0 17.726-.26-1.477M10.698 4.614l-.26-1.477M16.5 19.794l-.75-1.299M7.5 4.205 12 12m6.894 5.785-1.149-.964M6.256 7.178l-1.15-.964m15.352 8.864-1.41-.513M4.954 9.435l-1.41-.514M12.002 12l-3.75 6.495"}))});t.Z=o},4697:function(e,t,n){"use strict";var r=n(2265);let o=r.forwardRef(function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))});t.Z=o},5372:function(e,t,n){"use strict";var r=n(2265);let o=r.forwardRef(function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"}))});t.Z=o},8362:function(e,t,n){"use strict";var r=n(2265);let o=r.forwardRef(function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.752 15.002A9.72 9.72 0 0 1 18 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 0 0 3 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 0 0 9.002-5.998Z"}))});t.Z=o},8657:function(e,t,n){"use strict";var r=n(2265);let o=r.forwardRef(function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 3v2.25m6.364.386-1.591 1.591M21 12h-2.25m-.386 6.364-1.591-1.591M12 18.75V21m-4.773-4.227-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636M15.75 12a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0Z"}))});t.Z=o},7822:function(e,t,n){"use strict";var r=n(2265);let o=r.forwardRef(function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"}))});t.Z=o},3274:function(e,t,n){"use strict";var r=n(2265);let o=r.forwardRef(function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18 18 6M6 6l12 12"}))});t.Z=o},794:function(e,t,n){"use strict";function r(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=function e(t){var n,r,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t){if(Array.isArray(t)){var i=t.length;for(n=0;n<i;n++)t[n]&&(r=e(t[n]))&&(o&&(o+=" "),o+=r)}else for(r in t)t[r]&&(o&&(o+=" "),o+=r)}return o}(e))&&(r&&(r+=" "),r+=t);return r}n.d(t,{W:function(){return r}})}}]);