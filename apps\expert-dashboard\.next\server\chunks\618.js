"use strict";exports.id=618,exports.ids=[618],exports.modules={5548:(e,t,r)=>{e.exports=r(7490)},1018:(e,t,r)=>{e.exports=r(7804)},4919:(e,t,r)=>{r.d(t,{V:()=>eE});var n,o,l,a,i,u,s=r(4218),c=r.t(s,2),d=r(808),f=r(3266),m=r(5784);function p(e,t,r,n){let o=(0,m.E)(r);(0,s.useEffect)(()=>{function r(e){o.current(e)}return(e=null!=e?e:window).addEventListener(t,r,n),()=>e.removeEventListener(t,r,n)},[e,t,n])}var v=r(6812),h=r(7051);function g(e){let t=(0,f.z)(e),r=(0,s.useRef)(!1);(0,s.useEffect)(()=>(r.current=!1,()=>{r.current=!0,(0,h.Y)(()=>{r.current&&t()})}),[t])}var E=r(2885),b=r(4348),w=r(960),y=r(1530),T=((n=T||{})[n.Forwards=0]="Forwards",n[n.Backwards=1]="Backwards",n);function M(e,t){let r=(0,s.useRef)([]),n=(0,f.z)(e);(0,s.useEffect)(()=>{let e=[...r.current];for(let[o,l]of t.entries())if(r.current[o]!==l){let o=n(t,e);return r.current=t,o}},[n,...t])}var R=r(3226),x=((o=x||{})[o.None=1]="None",o[o.Focusable=2]="Focusable",o[o.Hidden=4]="Hidden",o);let S=(0,R.yV)(function(e,t){var r;let{features:n=1,...o}=e,l={ref:t,"aria-hidden":(2&n)==2||(null!=(r=o["aria-hidden"])?r:void 0),hidden:(4&n)==4||void 0,style:{position:"fixed",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0",...(4&n)==4&&(2&n)!=2&&{display:"none"}}};return(0,R.sY)({ourProps:l,theirProps:o,slot:{},defaultTag:"div",name:"Hidden"})}),k=[];var C=r(2763),P=r(9583);function O(e){if(!e)return new Set;if("function"==typeof e)return new Set(e());let t=new Set;for(let r of e.current)r.current instanceof HTMLElement&&t.add(r.current);return t}var L=((l=L||{})[l.None=1]="None",l[l.InitialFocus=2]="InitialFocus",l[l.TabLock=4]="TabLock",l[l.FocusLock=8]="FocusLock",l[l.RestoreFocus=16]="RestoreFocus",l[l.All=30]="All",l);let F=Object.assign((0,R.yV)(function(e,t){let r,n=(0,s.useRef)(null),o=(0,w.T)(n,t),{initialFocus:l,containers:a,features:i=30,...u}=e;(0,b.H)()||(i=1);let c=(0,E.i)(n);(function({ownerDocument:e},t){let r=function(e=!0){let t=(0,s.useRef)(k.slice());return M(([e],[r])=>{!0===r&&!1===e&&(0,h.Y)(()=>{t.current.splice(0)}),!1===r&&!0===e&&(t.current=k.slice())},[e,k,t]),(0,f.z)(()=>{var e;return null!=(e=t.current.find(e=>null!=e&&e.isConnected))?e:null})}(t);M(()=>{t||(null==e?void 0:e.activeElement)===(null==e?void 0:e.body)&&(0,C.C5)(r())},[t]),g(()=>{t&&(0,C.C5)(r())})})({ownerDocument:c},!!(16&i));let m=function({ownerDocument:e,container:t,initialFocus:r},n){let o=(0,s.useRef)(null),l=(0,v.t)();return M(()=>{if(!n)return;let a=t.current;a&&(0,h.Y)(()=>{if(!l.current)return;let t=null==e?void 0:e.activeElement;if(null!=r&&r.current){if((null==r?void 0:r.current)===t){o.current=t;return}}else if(a.contains(t)){o.current=t;return}null!=r&&r.current?(0,C.C5)(r.current):(0,C.jA)(a,C.TO.First)===C.fE.Error&&console.warn("There are no focusable elements inside the <FocusTrap />"),o.current=null==e?void 0:e.activeElement})},[n]),o}({ownerDocument:c,container:n,initialFocus:l},!!(2&i));(function({ownerDocument:e,container:t,containers:r,previousActiveElement:n},o){let l=(0,v.t)();p(null==e?void 0:e.defaultView,"focus",e=>{if(!o||!l.current)return;let a=O(r);t.current instanceof HTMLElement&&a.add(t.current);let i=n.current;if(!i)return;let u=e.target;u&&u instanceof HTMLElement?A(a,u)?(n.current=u,(0,C.C5)(u)):(e.preventDefault(),e.stopPropagation(),(0,C.C5)(i)):(0,C.C5)(n.current)},!0)})({ownerDocument:c,container:n,containers:a,previousActiveElement:m},!!(8&i));let L=(r=(0,s.useRef)(0),(0,y.s)("keydown",e=>{"Tab"===e.key&&(r.current=e.shiftKey?1:0)},!0),r),F=(0,f.z)(e=>{let t=n.current;t&&(0,P.E)(L.current,{[T.Forwards]:()=>{(0,C.jA)(t,C.TO.First,{skipElements:[e.relatedTarget]})},[T.Backwards]:()=>{(0,C.jA)(t,C.TO.Last,{skipElements:[e.relatedTarget]})}})}),I=(0,d.G)(),D=(0,s.useRef)(!1);return s.createElement(s.Fragment,null,!!(4&i)&&s.createElement(S,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:F,features:x.Focusable}),(0,R.sY)({ourProps:{ref:o,onKeyDown(e){"Tab"==e.key&&(D.current=!0,I.requestAnimationFrame(()=>{D.current=!1}))},onBlur(e){let t=O(a);n.current instanceof HTMLElement&&t.add(n.current);let r=e.relatedTarget;r instanceof HTMLElement&&"true"!==r.dataset.headlessuiFocusGuard&&(A(t,r)||(D.current?(0,C.jA)(n.current,(0,P.E)(L.current,{[T.Forwards]:()=>C.TO.Next,[T.Backwards]:()=>C.TO.Previous})|C.TO.WrapAround,{relativeTo:e.target}):e.target instanceof HTMLElement&&(0,C.C5)(e.target)))}},theirProps:u,defaultTag:"div",name:"FocusTrap"}),!!(4&i)&&s.createElement(S,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:F,features:x.Focusable}))}),{features:L});function A(e,t){for(let r of e)if(r.contains(t))return!0;return!1}var I=r(3638),D=r(1625);let N=(0,s.createContext)(!1);function j(e){return s.createElement(N.Provider,{value:e.force},e.children)}var H=r(4493);let V=s.Fragment,z=s.Fragment,B=(0,s.createContext)(null),Z=(0,s.createContext)(null),Y=Object.assign((0,R.yV)(function(e,t){let r=(0,s.useRef)(null),n=(0,w.T)((0,w.h)(e=>{r.current=e}),t),o=(0,E.i)(r),l=function(e){let t=(0,s.useContext)(N),r=(0,s.useContext)(B),n=(0,E.i)(e),[o,l]=(0,s.useState)(()=>{if(!t&&null!==r||H.O.isServer)return null;let e=null==n?void 0:n.getElementById("headlessui-portal-root");if(e)return e;if(null===n)return null;let o=n.createElement("div");return o.setAttribute("id","headlessui-portal-root"),n.body.appendChild(o)});return(0,s.useEffect)(()=>{null!==o&&(null!=n&&n.body.contains(o)||null==n||n.body.appendChild(o))},[o,n]),(0,s.useEffect)(()=>{t||null!==r&&l(r.current)},[r,l,t]),o}(r),[a]=(0,s.useState)(()=>{var e;return H.O.isServer?null:null!=(e=null==o?void 0:o.createElement("div"))?e:null}),i=(0,s.useContext)(Z),u=(0,b.H)();return(0,D.e)(()=>{!l||!a||l.contains(a)||(a.setAttribute("data-headlessui-portal",""),l.appendChild(a))},[l,a]),(0,D.e)(()=>{if(a&&i)return i.register(a)},[i,a]),g(()=>{var e;l&&a&&(a instanceof Node&&l.contains(a)&&l.removeChild(a),l.childNodes.length<=0&&(null==(e=l.parentElement)||e.removeChild(l)))}),u&&l&&a?(0,I.createPortal)((0,R.sY)({ourProps:{ref:n},theirProps:e,defaultTag:V,name:"Portal"}),a):null}),{Group:(0,R.yV)(function(e,t){let{target:r,...n}=e,o={ref:(0,w.T)(t)};return s.createElement(B.Provider,{value:r},(0,R.sY)({ourProps:o,theirProps:n,defaultTag:z,name:"Popover.Group"}))})});"function"==typeof Object.is&&Object.is;let{useState:W,useEffect:_,useLayoutEffect:U,useDebugValue:$}=c,q="useSyncExternalStore"in c?c.useSyncExternalStore:function(e,t,r){return t()};var G=r(881),K=r(2776);let J=function(e,t){let r=e(),n=new Set;return{getSnapshot:()=>r,subscribe:e=>(n.add(e),()=>n.delete(e)),dispatch(e,...o){let l=t[e].call(r,...o);l&&(r=l,n.forEach(e=>e()))}}}(()=>new Map,{PUSH(e,t){var r;let n=null!=(r=this.get(e))?r:{doc:e,count:0,d:(0,G.k)(),meta:new Set};return n.count++,n.meta.add(t),this.set(e,n),this},POP(e,t){let r=this.get(e);return r&&(r.count--,r.meta.delete(t)),this},SCROLL_PREVENT({doc:e,d:t,meta:r}){let n;let o={doc:e,d:t,meta:function(e){let t={};for(let r of e)Object.assign(t,r(t));return t}(r)},l=[(0,K.gn)()?{before({doc:e,d:t,meta:r}){function n(e){return r.containers.flatMap(e=>e()).some(t=>t.contains(e))}t.microTask(()=>{var r;if("auto"!==window.getComputedStyle(e.documentElement).scrollBehavior){let r=(0,G.k)();r.style(e.documentElement,"scrollBehavior","auto"),t.add(()=>t.microTask(()=>r.dispose()))}let o=null!=(r=window.scrollY)?r:window.pageYOffset,l=null;t.addEventListener(e,"click",t=>{if(t.target instanceof HTMLElement)try{let r=t.target.closest("a");if(!r)return;let{hash:o}=new URL(r.href),a=e.querySelector(o);a&&!n(a)&&(l=a)}catch{}},!0),t.addEventListener(e,"touchstart",e=>{if(e.target instanceof HTMLElement){if(n(e.target)){let r=e.target;for(;r.parentElement&&n(r.parentElement);)r=r.parentElement;t.style(r,"overscrollBehavior","contain")}else t.style(e.target,"touchAction","none")}}),t.addEventListener(e,"touchmove",e=>{if(e.target instanceof HTMLElement){if(n(e.target)){let t=e.target;for(;t.parentElement&&""!==t.dataset.headlessuiPortal&&!(t.scrollHeight>t.clientHeight||t.scrollWidth>t.clientWidth);)t=t.parentElement;""===t.dataset.headlessuiPortal&&e.preventDefault()}else e.preventDefault()}},{passive:!1}),t.add(()=>{var e;o!==(null!=(e=window.scrollY)?e:window.pageYOffset)&&window.scrollTo(0,o),l&&l.isConnected&&(l.scrollIntoView({block:"nearest"}),l=null)})})}}:{},{before({doc:e}){var t;let r=e.documentElement;n=(null!=(t=e.defaultView)?t:window).innerWidth-r.clientWidth},after({doc:e,d:t}){let r=e.documentElement,o=r.clientWidth-r.offsetWidth,l=n-o;t.style(r,"paddingRight",`${l}px`)}},{before({doc:e,d:t}){t.style(e.documentElement,"overflow","hidden")}}];l.forEach(({before:e})=>null==e?void 0:e(o)),l.forEach(({after:e})=>null==e?void 0:e(o))},SCROLL_ALLOW({d:e}){e.dispose()},TEARDOWN({doc:e}){this.delete(e)}});J.subscribe(()=>{let e=J.getSnapshot(),t=new Map;for(let[r]of e)t.set(r,r.documentElement.style.overflow);for(let r of e.values()){let e="hidden"===t.get(r.doc),n=0!==r.count;(n&&!e||!n&&e)&&J.dispatch(r.count>0?"SCROLL_PREVENT":"SCROLL_ALLOW",r),0===r.count&&J.dispatch("TEARDOWN",r)}});var Q=r(6506);let X=new Map,ee=new Map;function et(e,t=!0){(0,D.e)(()=>{var r;if(!t)return;let n="function"==typeof e?e():e.current;if(!n)return;let o=null!=(r=ee.get(n))?r:0;return ee.set(n,o+1),0!==o||(X.set(n,{"aria-hidden":n.getAttribute("aria-hidden"),inert:n.inert}),n.setAttribute("aria-hidden","true"),n.inert=!0),function(){var e;if(!n)return;let t=null!=(e=ee.get(n))?e:1;if(1===t?ee.delete(n):ee.set(n,t-1),1!==t)return;let r=X.get(n);r&&(null===r["aria-hidden"]?n.removeAttribute("aria-hidden"):n.setAttribute("aria-hidden",r["aria-hidden"]),n.inert=r.inert,X.delete(n))}},[e,t])}var er=r(9066),en=r(2698);let eo=(0,s.createContext)(()=>{});eo.displayName="StackContext";var el=((a=el||{})[a.Add=0]="Add",a[a.Remove=1]="Remove",a);function ea({children:e,onUpdate:t,type:r,element:n,enabled:o}){let l=(0,s.useContext)(eo),a=(0,f.z)((...e)=>{null==t||t(...e),l(...e)});return(0,D.e)(()=>{let e=void 0===o||!0===o;return e&&a(0,r,n),()=>{e&&a(1,r,n)}},[a,r,n,o]),s.createElement(eo.Provider,{value:a},e)}var ei=r(4642);let eu=(0,s.createContext)(null),es=Object.assign((0,R.yV)(function(e,t){let r=(0,Q.M)(),{id:n=`headlessui-description-${r}`,...o}=e,l=function e(){let t=(0,s.useContext)(eu);if(null===t){let t=Error("You used a <Description /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(t,e),t}return t}(),a=(0,w.T)(t);(0,D.e)(()=>l.register(n),[n,l.register]);let i={ref:a,...l.props,id:n};return(0,R.sY)({ourProps:i,theirProps:o,slot:l.slot||{},defaultTag:"p",name:l.name||"Description"})}),{});var ec=r(8334),ed=((i=ed||{})[i.Open=0]="Open",i[i.Closed=1]="Closed",i),ef=((u=ef||{})[u.SetTitleId=0]="SetTitleId",u);let em={0:(e,t)=>e.titleId===t.id?e:{...e,titleId:t.id}},ep=(0,s.createContext)(null);function ev(e){let t=(0,s.useContext)(ep);if(null===t){let t=Error(`<${e} /> is missing a parent <Dialog /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,ev),t}return t}function eh(e,t){return(0,P.E)(t.type,em,e,t)}ep.displayName="DialogContext";let eg=R.AN.RenderStrategy|R.AN.Static,eE=Object.assign((0,R.yV)(function(e,t){let r,n,o,l,a,i=(0,Q.M)(),{id:u=`headlessui-dialog-${i}`,open:c,onClose:d,initialFocus:m,role:v="dialog",__demoMode:h=!1,...g}=e,[y,T]=(0,s.useState)(0),M=(0,s.useRef)(!1);v="dialog"===v||"alertdialog"===v?v:(M.current||(M.current=!0,console.warn(`Invalid role [${v}] passed to <Dialog />. Only \`dialog\` and and \`alertdialog\` are supported. Using \`dialog\` instead.`)),"dialog");let k=(0,en.oJ)();void 0===c&&null!==k&&(c=(k&en.ZM.Open)===en.ZM.Open);let C=(0,s.useRef)(null),O=(0,w.T)(C,t),L=(0,E.i)(C),A=e.hasOwnProperty("open")||null!==k,I=e.hasOwnProperty("onClose");if(!A&&!I)throw Error("You have to provide an `open` and an `onClose` prop to the `Dialog` component.");if(!A)throw Error("You provided an `onClose` prop to the `Dialog`, but forgot an `open` prop.");if(!I)throw Error("You provided an `open` prop to the `Dialog`, but forgot an `onClose` prop.");if("boolean"!=typeof c)throw Error(`You provided an \`open\` prop to the \`Dialog\`, but the value is not a boolean. Received: ${c}`);if("function"!=typeof d)throw Error(`You provided an \`onClose\` prop to the \`Dialog\`, but the value is not a function. Received: ${d}`);let N=c?0:1,[H,V]=(0,s.useReducer)(eh,{titleId:null,descriptionId:null,panelRef:(0,s.createRef)()}),z=(0,f.z)(()=>d(!1)),B=(0,f.z)(e=>V({type:0,id:e})),W=!!(0,b.H)()&&!h&&0===N,_=y>1,U=null!==(0,s.useContext)(ep),[$,G]=(r=(0,s.useContext)(Z),n=(0,s.useRef)([]),o=(0,f.z)(e=>(n.current.push(e),r&&r.register(e),()=>l(e))),l=(0,f.z)(e=>{let t=n.current.indexOf(e);-1!==t&&n.current.splice(t,1),r&&r.unregister(e)}),a=(0,s.useMemo)(()=>({register:o,unregister:l,portals:n}),[o,l,n]),[n,(0,s.useMemo)(()=>function({children:e}){return s.createElement(Z.Provider,{value:a},e)},[a])]),{resolveContainers:K,mainTreeNodeRef:X,MainTreeNode:ee}=function({defaultContainers:e=[],portals:t,mainTreeNodeRef:r}={}){var n;let o=(0,s.useRef)(null!=(n=null==r?void 0:r.current)?n:null),l=(0,E.i)(o),a=(0,f.z)(()=>{var r,n,a;let i=[];for(let t of e)null!==t&&(t instanceof HTMLElement?i.push(t):"current"in t&&t.current instanceof HTMLElement&&i.push(t.current));if(null!=t&&t.current)for(let e of t.current)i.push(e);for(let e of null!=(r=null==l?void 0:l.querySelectorAll("html > *, body > *"))?r:[])e!==document.body&&e!==document.head&&e instanceof HTMLElement&&"headlessui-portal-root"!==e.id&&(e.contains(o.current)||e.contains(null==(a=null==(n=o.current)?void 0:n.getRootNode())?void 0:a.host)||i.some(t=>e.contains(t))||i.push(e));return i});return{resolveContainers:a,contains:(0,f.z)(e=>a().some(t=>t.contains(e))),mainTreeNodeRef:o,MainTreeNode:(0,s.useMemo)(()=>function(){return null!=r?null:s.createElement(S,{features:x.Hidden,ref:o})},[o,r])}}({portals:$,defaultContainers:[{get current(){var eo;return null!=(eo=H.panelRef.current)?eo:C.current}}]}),ei=null!==k&&(k&en.ZM.Closing)===en.ZM.Closing,es=!U&&!ei&&W;et((0,s.useCallback)(()=>{var e,t;return null!=(t=Array.from(null!=(e=null==L?void 0:L.querySelectorAll("body > *"))?e:[]).find(e=>"headlessui-portal-root"!==e.id&&e.contains(X.current)&&e instanceof HTMLElement))?t:null},[X]),es);let ed=!!_||W;et((0,s.useCallback)(()=>{var e,t;return null!=(t=Array.from(null!=(e=null==L?void 0:L.querySelectorAll("[data-headlessui-portal]"))?e:[]).find(e=>e.contains(X.current)&&e instanceof HTMLElement))?t:null},[X]),ed);let ef=!(!W||_);(0,er.O)(K,e=>{e.preventDefault(),z()},ef);let em=!(_||0!==N);p(null==L?void 0:L.defaultView,"keydown",e=>{em&&(e.defaultPrevented||e.key===ec.R.Escape&&(e.preventDefault(),e.stopPropagation(),z()))}),function(e,t,r=()=>[document.body]){var n;let o,l;n=e=>{var t;return{containers:[...null!=(t=e.containers)?t:[],r]}},o=q(J.subscribe,J.getSnapshot,J.getSnapshot),(l=e?o.get(e):void 0)&&l.count,(0,D.e)(()=>{if(!(!e||!t))return J.dispatch("PUSH",e,n),()=>J.dispatch("POP",e,n)},[t,e])}(L,!(ei||0!==N||U),K),(0,s.useEffect)(()=>{if(0!==N||!C.current)return;let e=new ResizeObserver(e=>{for(let t of e){let e=t.target.getBoundingClientRect();0===e.x&&0===e.y&&0===e.width&&0===e.height&&z()}});return e.observe(C.current),()=>e.disconnect()},[N,C,z]);let[ev,eE]=function(){let[e,t]=(0,s.useState)([]);return[e.length>0?e.join(" "):void 0,(0,s.useMemo)(()=>function(e){let r=(0,f.z)(e=>(t(t=>[...t,e]),()=>t(t=>{let r=t.slice(),n=r.indexOf(e);return -1!==n&&r.splice(n,1),r}))),n=(0,s.useMemo)(()=>({register:r,slot:e.slot,name:e.name,props:e.props}),[r,e.slot,e.name,e.props]);return s.createElement(eu.Provider,{value:n},e.children)},[t])]}(),eb=(0,s.useMemo)(()=>[{dialogState:N,close:z,setTitleId:B},H],[N,H,z,B]),ew=(0,s.useMemo)(()=>({open:0===N}),[N]),ey={ref:O,id:u,role:v,"aria-modal":0===N||void 0,"aria-labelledby":H.titleId,"aria-describedby":ev};return s.createElement(ea,{type:"Dialog",enabled:0===N,element:C,onUpdate:(0,f.z)((e,t)=>{"Dialog"===t&&(0,P.E)(e,{[el.Add]:()=>T(e=>e+1),[el.Remove]:()=>T(e=>e-1)})})},s.createElement(j,{force:!0},s.createElement(Y,null,s.createElement(ep.Provider,{value:eb},s.createElement(Y.Group,{target:C},s.createElement(j,{force:!1},s.createElement(eE,{slot:ew,name:"Dialog.Description"},s.createElement(F,{initialFocus:m,containers:K,features:W?(0,P.E)(_?"parent":"leaf",{parent:F.features.RestoreFocus,leaf:F.features.All&~F.features.FocusLock}):F.features.None},s.createElement(G,null,(0,R.sY)({ourProps:ey,theirProps:g,slot:ew,defaultTag:"div",features:eg,visible:0===N,name:"Dialog"}))))))))),s.createElement(ee,null))}),{Backdrop:(0,R.yV)(function(e,t){let r=(0,Q.M)(),{id:n=`headlessui-dialog-backdrop-${r}`,...o}=e,[{dialogState:l},a]=ev("Dialog.Backdrop"),i=(0,w.T)(t);(0,s.useEffect)(()=>{if(null===a.panelRef.current)throw Error("A <Dialog.Backdrop /> component is being used, but a <Dialog.Panel /> component is missing.")},[a.panelRef]);let u=(0,s.useMemo)(()=>({open:0===l}),[l]);return s.createElement(j,{force:!0},s.createElement(Y,null,(0,R.sY)({ourProps:{ref:i,id:n,"aria-hidden":!0},theirProps:o,slot:u,defaultTag:"div",name:"Dialog.Backdrop"})))}),Panel:(0,R.yV)(function(e,t){let r=(0,Q.M)(),{id:n=`headlessui-dialog-panel-${r}`,...o}=e,[{dialogState:l},a]=ev("Dialog.Panel"),i=(0,w.T)(t,a.panelRef),u=(0,s.useMemo)(()=>({open:0===l}),[l]),c=(0,f.z)(e=>{e.stopPropagation()});return(0,R.sY)({ourProps:{ref:i,id:n,onClick:c},theirProps:o,slot:u,defaultTag:"div",name:"Dialog.Panel"})}),Overlay:(0,R.yV)(function(e,t){let r=(0,Q.M)(),{id:n=`headlessui-dialog-overlay-${r}`,...o}=e,[{dialogState:l,close:a}]=ev("Dialog.Overlay"),i=(0,w.T)(t),u=(0,f.z)(e=>{if(e.target===e.currentTarget){if((0,ei.P)(e.currentTarget))return e.preventDefault();e.preventDefault(),e.stopPropagation(),a()}}),c=(0,s.useMemo)(()=>({open:0===l}),[l]);return(0,R.sY)({ourProps:{ref:i,id:n,"aria-hidden":!0,onClick:u},theirProps:o,slot:c,defaultTag:"div",name:"Dialog.Overlay"})}),Title:(0,R.yV)(function(e,t){let r=(0,Q.M)(),{id:n=`headlessui-dialog-title-${r}`,...o}=e,[{dialogState:l,setTitleId:a}]=ev("Dialog.Title"),i=(0,w.T)(t);(0,s.useEffect)(()=>(a(n),()=>a(null)),[n,a]);let u=(0,s.useMemo)(()=>({open:0===l}),[l]);return(0,R.sY)({ourProps:{ref:i,id:n},theirProps:o,slot:u,defaultTag:"h2",name:"Dialog.Title"})}),Description:es})},8334:(e,t,r)=>{r.d(t,{R:()=>o});var n,o=((n=o||{}).Space=" ",n.Enter="Enter",n.Escape="Escape",n.Backspace="Backspace",n.Delete="Delete",n.ArrowLeft="ArrowLeft",n.ArrowUp="ArrowUp",n.ArrowRight="ArrowRight",n.ArrowDown="ArrowDown",n.Home="Home",n.End="End",n.PageUp="PageUp",n.PageDown="PageDown",n.Tab="Tab",n)},1683:(e,t,r)=>{r.d(t,{v:()=>V});var n,o,l,a,i=r(4218),u=r(808),s=r(3266),c=r(6506),d=r(1625),f=r(9066),m=r(2885);function p(e){var t;if(e.type)return e.type;let r=null!=(t=e.as)?t:"button";if("string"==typeof r&&"button"===r.toLowerCase())return"button"}var v=r(960);let h=/([\u2700-\u27BF]|[\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g;function g(e){var t,r;let n=null!=(t=e.innerText)?t:"",o=e.cloneNode(!0);if(!(o instanceof HTMLElement))return n;let l=!1;for(let e of o.querySelectorAll('[hidden],[aria-hidden],[role="img"]'))e.remove(),l=!0;let a=l?null!=(r=o.innerText)?r:"":n;return h.test(a)&&(a=a.replace(h,"")),a}function E(e){return[e.screenX,e.screenY]}var b=r(5780),w=r(2698),y=r(4642),T=((n=T||{})[n.First=0]="First",n[n.Previous=1]="Previous",n[n.Next=2]="Next",n[n.Last=3]="Last",n[n.Specific=4]="Specific",n[n.Nothing=5]="Nothing",n),M=r(881),R=r(2763),x=r(9583),S=r(3226),k=r(8334),C=((o=C||{})[o.Open=0]="Open",o[o.Closed=1]="Closed",o),P=((l=P||{})[l.Pointer=0]="Pointer",l[l.Other=1]="Other",l),O=((a=O||{})[a.OpenMenu=0]="OpenMenu",a[a.CloseMenu=1]="CloseMenu",a[a.GoToItem=2]="GoToItem",a[a.Search=3]="Search",a[a.ClearSearch=4]="ClearSearch",a[a.RegisterItem=5]="RegisterItem",a[a.UnregisterItem=6]="UnregisterItem",a);function L(e,t=e=>e){let r=null!==e.activeItemIndex?e.items[e.activeItemIndex]:null,n=(0,R.z2)(t(e.items.slice()),e=>e.dataRef.current.domRef.current),o=r?n.indexOf(r):null;return -1===o&&(o=null),{items:n,activeItemIndex:o}}let F={1:e=>1===e.menuState?e:{...e,activeItemIndex:null,menuState:1},0:e=>0===e.menuState?e:{...e,__demoMode:!1,menuState:0},2:(e,t)=>{var r;let n=L(e),o=function(e,t){let r=t.resolveItems();if(r.length<=0)return null;let n=t.resolveActiveIndex(),o=null!=n?n:-1;switch(e.focus){case 0:for(let e=0;e<r.length;++e)if(!t.resolveDisabled(r[e],e,r))return e;return n;case 1:for(let e=o-1;e>=0;--e)if(!t.resolveDisabled(r[e],e,r))return e;return n;case 2:for(let e=o+1;e<r.length;++e)if(!t.resolveDisabled(r[e],e,r))return e;return n;case 3:for(let e=r.length-1;e>=0;--e)if(!t.resolveDisabled(r[e],e,r))return e;return n;case 4:for(let n=0;n<r.length;++n)if(t.resolveId(r[n],n,r)===e.id)return n;return n;case 5:return null;default:!function(e){throw Error("Unexpected object: "+e)}(e)}}(t,{resolveItems:()=>n.items,resolveActiveIndex:()=>n.activeItemIndex,resolveId:e=>e.id,resolveDisabled:e=>e.dataRef.current.disabled});return{...e,...n,searchQuery:"",activeItemIndex:o,activationTrigger:null!=(r=t.trigger)?r:1}},3:(e,t)=>{let r=""!==e.searchQuery?0:1,n=e.searchQuery+t.value.toLowerCase(),o=(null!==e.activeItemIndex?e.items.slice(e.activeItemIndex+r).concat(e.items.slice(0,e.activeItemIndex+r)):e.items).find(e=>{var t;return(null==(t=e.dataRef.current.textValue)?void 0:t.startsWith(n))&&!e.dataRef.current.disabled}),l=o?e.items.indexOf(o):-1;return -1===l||l===e.activeItemIndex?{...e,searchQuery:n}:{...e,searchQuery:n,activeItemIndex:l,activationTrigger:1}},4:e=>""===e.searchQuery?e:{...e,searchQuery:"",searchActiveItemIndex:null},5:(e,t)=>{let r=L(e,e=>[...e,{id:t.id,dataRef:t.dataRef}]);return{...e,...r}},6:(e,t)=>{let r=L(e,e=>{let r=e.findIndex(e=>e.id===t.id);return -1!==r&&e.splice(r,1),e});return{...e,...r,activationTrigger:1}}},A=(0,i.createContext)(null);function I(e){let t=(0,i.useContext)(A);if(null===t){let t=Error(`<${e} /> is missing a parent <Menu /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,I),t}return t}function D(e,t){return(0,x.E)(t.type,F,e,t)}A.displayName="MenuContext";let N=i.Fragment,j=S.AN.RenderStrategy|S.AN.Static,H=i.Fragment,V=Object.assign((0,S.yV)(function(e,t){let{__demoMode:r=!1,...n}=e,o=(0,i.useReducer)(D,{__demoMode:r,menuState:r?0:1,buttonRef:(0,i.createRef)(),itemsRef:(0,i.createRef)(),items:[],searchQuery:"",activeItemIndex:null,activationTrigger:1}),[{menuState:l,itemsRef:a,buttonRef:u},c]=o,d=(0,v.T)(t);(0,f.O)([u,a],(e,t)=>{var r;c({type:1}),(0,R.sP)(t,R.tJ.Loose)||(e.preventDefault(),null==(r=u.current)||r.focus())},0===l);let m=(0,s.z)(()=>{c({type:1})}),p=(0,i.useMemo)(()=>({open:0===l,close:m}),[l,m]);return i.createElement(A.Provider,{value:o},i.createElement(w.up,{value:(0,x.E)(l,{0:w.ZM.Open,1:w.ZM.Closed})},(0,S.sY)({ourProps:{ref:d},theirProps:n,slot:p,defaultTag:N,name:"Menu"})))}),{Button:(0,S.yV)(function(e,t){var r;let n=(0,c.M)(),{id:o=`headlessui-menu-button-${n}`,...l}=e,[a,f]=I("Menu.Button"),m=(0,v.T)(a.buttonRef,t),h=(0,u.G)(),g=(0,s.z)(e=>{switch(e.key){case k.R.Space:case k.R.Enter:case k.R.ArrowDown:e.preventDefault(),e.stopPropagation(),f({type:0}),h.nextFrame(()=>f({type:2,focus:T.First}));break;case k.R.ArrowUp:e.preventDefault(),e.stopPropagation(),f({type:0}),h.nextFrame(()=>f({type:2,focus:T.Last}))}}),E=(0,s.z)(e=>{e.key===k.R.Space&&e.preventDefault()}),b=(0,s.z)(t=>{if((0,y.P)(t.currentTarget))return t.preventDefault();e.disabled||(0===a.menuState?(f({type:1}),h.nextFrame(()=>{var e;return null==(e=a.buttonRef.current)?void 0:e.focus({preventScroll:!0})})):(t.preventDefault(),f({type:0})))}),w=(0,i.useMemo)(()=>({open:0===a.menuState}),[a]),M={ref:m,id:o,type:function(e,t){let[r,n]=(0,i.useState)(()=>p(e));return(0,d.e)(()=>{n(p(e))},[e.type,e.as]),(0,d.e)(()=>{r||t.current&&t.current instanceof HTMLButtonElement&&!t.current.hasAttribute("type")&&n("button")},[r,t]),r}(e,a.buttonRef),"aria-haspopup":"menu","aria-controls":null==(r=a.itemsRef.current)?void 0:r.id,"aria-expanded":0===a.menuState,onKeyDown:g,onKeyUp:E,onClick:b};return(0,S.sY)({ourProps:M,theirProps:l,slot:w,defaultTag:"button",name:"Menu.Button"})}),Items:(0,S.yV)(function(e,t){var r,n;let o=(0,c.M)(),{id:l=`headlessui-menu-items-${o}`,...a}=e,[f,p]=I("Menu.Items"),h=(0,v.T)(f.itemsRef,t),g=(0,m.i)(f.itemsRef),E=(0,u.G)(),y=(0,w.oJ)(),x=null!==y?(y&w.ZM.Open)===w.ZM.Open:0===f.menuState;(0,i.useEffect)(()=>{let e=f.itemsRef.current;e&&0===f.menuState&&e!==(null==g?void 0:g.activeElement)&&e.focus({preventScroll:!0})},[f.menuState,f.itemsRef,g]),function({container:e,accept:t,walk:r,enabled:n=!0}){let o=(0,i.useRef)(t),l=(0,i.useRef)(r);(0,i.useEffect)(()=>{o.current=t,l.current=r},[t,r]),(0,d.e)(()=>{if(!e||!n)return;let t=(0,b.r)(e);if(!t)return;let r=o.current,a=l.current,i=Object.assign(e=>r(e),{acceptNode:r}),u=t.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,i,!1);for(;u.nextNode();)a(u.currentNode)},[e,n,o,l])}({container:f.itemsRef.current,enabled:0===f.menuState,accept:e=>"menuitem"===e.getAttribute("role")?NodeFilter.FILTER_REJECT:e.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT,walk(e){e.setAttribute("role","none")}});let C=(0,s.z)(e=>{var t,r;switch(E.dispose(),e.key){case k.R.Space:if(""!==f.searchQuery)return e.preventDefault(),e.stopPropagation(),p({type:3,value:e.key});case k.R.Enter:if(e.preventDefault(),e.stopPropagation(),p({type:1}),null!==f.activeItemIndex){let{dataRef:e}=f.items[f.activeItemIndex];null==(r=null==(t=e.current)?void 0:t.domRef.current)||r.click()}(0,R.wI)(f.buttonRef.current);break;case k.R.ArrowDown:return e.preventDefault(),e.stopPropagation(),p({type:2,focus:T.Next});case k.R.ArrowUp:return e.preventDefault(),e.stopPropagation(),p({type:2,focus:T.Previous});case k.R.Home:case k.R.PageUp:return e.preventDefault(),e.stopPropagation(),p({type:2,focus:T.First});case k.R.End:case k.R.PageDown:return e.preventDefault(),e.stopPropagation(),p({type:2,focus:T.Last});case k.R.Escape:e.preventDefault(),e.stopPropagation(),p({type:1}),(0,M.k)().nextFrame(()=>{var e;return null==(e=f.buttonRef.current)?void 0:e.focus({preventScroll:!0})});break;case k.R.Tab:e.preventDefault(),e.stopPropagation(),p({type:1}),(0,M.k)().nextFrame(()=>{(0,R.EO)(f.buttonRef.current,e.shiftKey?R.TO.Previous:R.TO.Next)});break;default:1===e.key.length&&(p({type:3,value:e.key}),E.setTimeout(()=>p({type:4}),350))}}),P=(0,s.z)(e=>{e.key===k.R.Space&&e.preventDefault()}),O=(0,i.useMemo)(()=>({open:0===f.menuState}),[f]),L={"aria-activedescendant":null===f.activeItemIndex||null==(r=f.items[f.activeItemIndex])?void 0:r.id,"aria-labelledby":null==(n=f.buttonRef.current)?void 0:n.id,id:l,onKeyDown:C,onKeyUp:P,role:"menu",tabIndex:0,ref:h};return(0,S.sY)({ourProps:L,theirProps:a,slot:O,defaultTag:"div",features:j,visible:x,name:"Menu.Items"})}),Item:(0,S.yV)(function(e,t){let r,n,o,l=(0,c.M)(),{id:a=`headlessui-menu-item-${l}`,disabled:u=!1,...f}=e,[m,p]=I("Menu.Item"),h=null!==m.activeItemIndex&&m.items[m.activeItemIndex].id===a,b=(0,i.useRef)(null),w=(0,v.T)(t,b);(0,d.e)(()=>{if(m.__demoMode||0!==m.menuState||!h||0===m.activationTrigger)return;let e=(0,M.k)();return e.requestAnimationFrame(()=>{var e,t;null==(t=null==(e=b.current)?void 0:e.scrollIntoView)||t.call(e,{block:"nearest"})}),e.dispose},[m.__demoMode,b,h,m.menuState,m.activationTrigger,m.activeItemIndex]);let y=(r=(0,i.useRef)(""),n=(0,i.useRef)(""),(0,s.z)(()=>{let e=b.current;if(!e)return"";let t=e.innerText;if(r.current===t)return n.current;let o=(function(e){let t=e.getAttribute("aria-label");if("string"==typeof t)return t.trim();let r=e.getAttribute("aria-labelledby");if(r){let e=r.split(" ").map(e=>{let t=document.getElementById(e);if(t){let e=t.getAttribute("aria-label");return"string"==typeof e?e.trim():g(t).trim()}return null}).filter(Boolean);if(e.length>0)return e.join(", ")}return g(e).trim()})(e).trim().toLowerCase();return r.current=t,n.current=o,o})),x=(0,i.useRef)({disabled:u,domRef:b,get textValue(){return y()}});(0,d.e)(()=>{x.current.disabled=u},[x,u]),(0,d.e)(()=>(p({type:5,id:a,dataRef:x}),()=>p({type:6,id:a})),[x,a]);let k=(0,s.z)(()=>{p({type:1})}),C=(0,s.z)(e=>{if(u)return e.preventDefault();p({type:1}),(0,R.wI)(m.buttonRef.current)}),P=(0,s.z)(()=>{if(u)return p({type:2,focus:T.Nothing});p({type:2,focus:T.Specific,id:a})}),O=(o=(0,i.useRef)([-1,-1]),{wasMoved(e){let t=E(e);return(o.current[0]!==t[0]||o.current[1]!==t[1])&&(o.current=t,!0)},update(e){o.current=E(e)}}),L=(0,s.z)(e=>O.update(e)),F=(0,s.z)(e=>{O.wasMoved(e)&&(u||h||p({type:2,focus:T.Specific,id:a,trigger:0}))}),A=(0,s.z)(e=>{O.wasMoved(e)&&(u||h&&p({type:2,focus:T.Nothing}))}),D=(0,i.useMemo)(()=>({active:h,disabled:u,close:k}),[h,u,k]);return(0,S.sY)({ourProps:{id:a,ref:w,role:"menuitem",tabIndex:!0===u?void 0:-1,"aria-disabled":!0===u||void 0,disabled:void 0,onClick:C,onFocus:P,onPointerEnter:L,onMouseEnter:L,onPointerMove:F,onMouseMove:F,onPointerLeave:A,onMouseLeave:A},theirProps:f,slot:D,defaultTag:H,name:"Menu.Item"})})})},9013:(e,t,r)=>{r.d(t,{u:()=>F});var n,o=r(4218),l=r(808),a=r(3266),i=r(6812),u=r(1625),s=r(5784),c=r(4348),d=r(960),f=r(881),m=r(9583);function p(e,...t){e&&t.length>0&&e.classList.add(...t)}function v(e,...t){e&&t.length>0&&e.classList.remove(...t)}var h=r(2698),g=r(1883),E=r(3226);function b(e=""){return e.split(/\s+/).filter(e=>e.length>1)}let w=(0,o.createContext)(null);w.displayName="TransitionContext";var y=((n=y||{}).Visible="visible",n.Hidden="hidden",n);let T=(0,o.createContext)(null);function M(e){return"children"in e?M(e.children):e.current.filter(({el:e})=>null!==e.current).filter(({state:e})=>"visible"===e).length>0}function R(e,t){let r=(0,s.E)(e),n=(0,o.useRef)([]),u=(0,i.t)(),c=(0,l.G)(),d=(0,a.z)((e,t=E.l4.Hidden)=>{let o=n.current.findIndex(({el:t})=>t===e);-1!==o&&((0,m.E)(t,{[E.l4.Unmount](){n.current.splice(o,1)},[E.l4.Hidden](){n.current[o].state="hidden"}}),c.microTask(()=>{var e;!M(n)&&u.current&&(null==(e=r.current)||e.call(r))}))}),f=(0,a.z)(e=>{let t=n.current.find(({el:t})=>t===e);return t?"visible"!==t.state&&(t.state="visible"):n.current.push({el:e,state:"visible"}),()=>d(e,E.l4.Unmount)}),p=(0,o.useRef)([]),v=(0,o.useRef)(Promise.resolve()),h=(0,o.useRef)({enter:[],leave:[],idle:[]}),g=(0,a.z)((e,r,n)=>{p.current.splice(0),t&&(t.chains.current[r]=t.chains.current[r].filter(([t])=>t!==e)),null==t||t.chains.current[r].push([e,new Promise(e=>{p.current.push(e)})]),null==t||t.chains.current[r].push([e,new Promise(e=>{Promise.all(h.current[r].map(([e,t])=>t)).then(()=>e())})]),"enter"===r?v.current=v.current.then(()=>null==t?void 0:t.wait.current).then(()=>n(r)):n(r)}),b=(0,a.z)((e,t,r)=>{Promise.all(h.current[t].splice(0).map(([e,t])=>t)).then(()=>{var e;null==(e=p.current.shift())||e()}).then(()=>r(t))});return(0,o.useMemo)(()=>({children:n,register:f,unregister:d,onStart:g,onStop:b,wait:v,chains:h}),[f,d,n,g,b,h,v])}function x(){}T.displayName="NestingContext";let S=["beforeEnter","afterEnter","beforeLeave","afterLeave"];function k(e){var t;let r={};for(let n of S)r[n]=null!=(t=e[n])?t:x;return r}let C=E.AN.RenderStrategy,P=(0,E.yV)(function(e,t){let{show:r,appear:n=!1,unmount:l=!0,...i}=e,s=(0,o.useRef)(null),f=(0,d.T)(s,t);(0,c.H)();let m=(0,h.oJ)();if(void 0===r&&null!==m&&(r=(m&h.ZM.Open)===h.ZM.Open),![!0,!1].includes(r))throw Error("A <Transition /> is used but it is missing a `show={true | false}` prop.");let[p,v]=(0,o.useState)(r?"visible":"hidden"),g=R(()=>{v("hidden")}),[b,y]=(0,o.useState)(!0),x=(0,o.useRef)([r]);(0,u.e)(()=>{!1!==b&&x.current[x.current.length-1]!==r&&(x.current.push(r),y(!1))},[x,r]);let S=(0,o.useMemo)(()=>({show:r,appear:n,initial:b}),[r,n,b]);(0,o.useEffect)(()=>{if(r)v("visible");else if(M(g)){let e=s.current;if(!e)return;let t=e.getBoundingClientRect();0===t.x&&0===t.y&&0===t.width&&0===t.height&&v("hidden")}else v("hidden")},[r,g]);let k={unmount:l},P=(0,a.z)(()=>{var t;b&&y(!1),null==(t=e.beforeEnter)||t.call(e)}),L=(0,a.z)(()=>{var t;b&&y(!1),null==(t=e.beforeLeave)||t.call(e)});return o.createElement(T.Provider,{value:g},o.createElement(w.Provider,{value:S},(0,E.sY)({ourProps:{...k,as:o.Fragment,children:o.createElement(O,{ref:f,...k,...i,beforeEnter:P,beforeLeave:L})},theirProps:{},defaultTag:o.Fragment,features:C,visible:"visible"===p,name:"Transition"})))}),O=(0,E.yV)(function(e,t){var r,n,y;let x;let{beforeEnter:S,afterEnter:P,beforeLeave:O,afterLeave:L,enter:F,enterFrom:A,enterTo:I,entered:D,leave:N,leaveFrom:j,leaveTo:H,...V}=e,z=(0,o.useRef)(null),B=(0,d.T)(z,t),Z=null==(r=V.unmount)||r?E.l4.Unmount:E.l4.Hidden,{show:Y,appear:W,initial:_}=function(){let e=(0,o.useContext)(w);if(null===e)throw Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),[U,$]=(0,o.useState)(Y?"visible":"hidden"),q=function(){let e=(0,o.useContext)(T);if(null===e)throw Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),{register:G,unregister:K}=q;(0,o.useEffect)(()=>G(z),[G,z]),(0,o.useEffect)(()=>{if(Z===E.l4.Hidden&&z.current){if(Y&&"visible"!==U){$("visible");return}return(0,m.E)(U,{hidden:()=>K(z),visible:()=>G(z)})}},[U,z,G,K,Y,Z]);let J=(0,s.E)({base:b(V.className),enter:b(F),enterFrom:b(A),enterTo:b(I),entered:b(D),leave:b(N),leaveFrom:b(j),leaveTo:b(H)}),Q=(y={beforeEnter:S,afterEnter:P,beforeLeave:O,afterLeave:L},x=(0,o.useRef)(k(y)),(0,o.useEffect)(()=>{x.current=k(y)},[y]),x),X=(0,c.H)();(0,o.useEffect)(()=>{if(X&&"visible"===U&&null===z.current)throw Error("Did you forget to passthrough the `ref` to the actual DOM node?")},[z,U,X]);let ee=W&&Y&&_,et=X&&(!_||W)?Y?"enter":"leave":"idle",er=function(e=0){let[t,r]=(0,o.useState)(e),n=(0,i.t)(),l=(0,o.useCallback)(e=>{n.current&&r(t=>t|e)},[t,n]),a=(0,o.useCallback)(e=>!!(t&e),[t]);return{flags:t,addFlag:l,hasFlag:a,removeFlag:(0,o.useCallback)(e=>{n.current&&r(t=>t&~e)},[r,n]),toggleFlag:(0,o.useCallback)(e=>{n.current&&r(t=>t^e)},[r])}}(0),en=(0,a.z)(e=>(0,m.E)(e,{enter:()=>{er.addFlag(h.ZM.Opening),Q.current.beforeEnter()},leave:()=>{er.addFlag(h.ZM.Closing),Q.current.beforeLeave()},idle:()=>{}})),eo=(0,a.z)(e=>(0,m.E)(e,{enter:()=>{er.removeFlag(h.ZM.Opening),Q.current.afterEnter()},leave:()=>{er.removeFlag(h.ZM.Closing),Q.current.afterLeave()},idle:()=>{}})),el=R(()=>{$("hidden"),K(z)},q),ea=(0,o.useRef)(!1);!function({immediate:e,container:t,direction:r,classes:n,onStart:o,onStop:a}){let c=(0,i.t)(),d=(0,l.G)(),h=(0,s.E)(r);(0,u.e)(()=>{e&&(h.current="enter")},[e]),(0,u.e)(()=>{let e=(0,f.k)();d.add(e.dispose);let r=t.current;if(r&&"idle"!==h.current&&c.current){var l,i,u;let t,s,c,d,g,E,b;return e.dispose(),o.current(h.current),e.add((l=n.current,i="enter"===h.current,u=()=>{e.dispose(),a.current(h.current)},s=i?"enter":"leave",c=(0,f.k)(),d=void 0!==u?(t={called:!1},(...e)=>{if(!t.called)return t.called=!0,u(...e)}):()=>{},"enter"===s&&(r.removeAttribute("hidden"),r.style.display=""),g=(0,m.E)(s,{enter:()=>l.enter,leave:()=>l.leave}),E=(0,m.E)(s,{enter:()=>l.enterTo,leave:()=>l.leaveTo}),b=(0,m.E)(s,{enter:()=>l.enterFrom,leave:()=>l.leaveFrom}),v(r,...l.base,...l.enter,...l.enterTo,...l.enterFrom,...l.leave,...l.leaveFrom,...l.leaveTo,...l.entered),p(r,...l.base,...g,...b),c.nextFrame(()=>{v(r,...l.base,...g,...b),p(r,...l.base,...g,...E),function(e,t){let r=(0,f.k)();if(!e)return r.dispose;let{transitionDuration:n,transitionDelay:o}=getComputedStyle(e),[l,a]=[n,o].map(e=>{let[t=0]=e.split(",").filter(Boolean).map(e=>e.includes("ms")?parseFloat(e):1e3*parseFloat(e)).sort((e,t)=>t-e);return t}),i=l+a;if(0!==i){r.group(r=>{r.setTimeout(()=>{t(),r.dispose()},i),r.addEventListener(e,"transitionrun",e=>{e.target===e.currentTarget&&r.dispose()})});let n=r.addEventListener(e,"transitionend",e=>{e.target===e.currentTarget&&(t(),n())})}else t();r.add(()=>t()),r.dispose}(r,()=>(v(r,...l.base,...g),p(r,...l.base,...l.entered),d()))}),c.dispose)),e.dispose}},[r])}({immediate:ee,container:z,classes:J,direction:et,onStart:(0,s.E)(e=>{ea.current=!0,el.onStart(z,e,en)}),onStop:(0,s.E)(e=>{ea.current=!1,el.onStop(z,e,eo),"leave"!==e||M(el)||($("hidden"),K(z))})});let ei=V;return ee?ei={...ei,className:(0,g.A)(V.className,...J.current.enter,...J.current.enterFrom)}:ea.current&&(ei.className=(0,g.A)(V.className,null==(n=z.current)?void 0:n.className),""===ei.className&&delete ei.className),o.createElement(T.Provider,{value:el},o.createElement(h.up,{value:(0,m.E)(U,{visible:h.ZM.Open,hidden:h.ZM.Closed})|er.flags},(0,E.sY)({ourProps:{ref:B},theirProps:ei,defaultTag:"div",features:C,visible:"visible"===U,name:"Transition.Child"})))}),L=(0,E.yV)(function(e,t){let r=null!==(0,o.useContext)(w),n=null!==(0,h.oJ)();return o.createElement(o.Fragment,null,!r&&n?o.createElement(P,{ref:t,...e}):o.createElement(O,{ref:t,...e}))}),F=Object.assign(P,{Child:L,Root:P})},808:(e,t,r)=>{r.d(t,{G:()=>l});var n=r(4218),o=r(881);function l(){let[e]=(0,n.useState)(o.k);return(0,n.useEffect)(()=>()=>e.dispose(),[e]),e}},3266:(e,t,r)=>{r.d(t,{z:()=>l});var n=r(4218),o=r(5784);let l=function(e){let t=(0,o.E)(e);return n.useCallback((...e)=>t.current(...e),[t])}},6506:(e,t,r)=>{r.d(t,{M:()=>u});var n,o=r(4218),l=r(4493),a=r(1625),i=r(4348);let u=null!=(n=o.useId)?n:function(){let e=(0,i.H)(),[t,r]=o.useState(e?()=>l.O.nextId():null);return(0,a.e)(()=>{null===t&&r(l.O.nextId())},[t]),null!=t?""+t:void 0}},6812:(e,t,r)=>{r.d(t,{t:()=>l});var n=r(4218),o=r(1625);function l(){let e=(0,n.useRef)(!1);return(0,o.e)(()=>(e.current=!0,()=>{e.current=!1}),[]),e}},1625:(e,t,r)=>{r.d(t,{e:()=>l});var n=r(4218),o=r(4493);let l=(e,t)=>{o.O.isServer?(0,n.useEffect)(e,t):(0,n.useLayoutEffect)(e,t)}},5784:(e,t,r)=>{r.d(t,{E:()=>l});var n=r(4218),o=r(1625);function l(e){let t=(0,n.useRef)(e);return(0,o.e)(()=>{t.current=e},[e]),t}},9066:(e,t,r)=>{r.d(t,{O:()=>s});var n=r(4218),o=r(2763),l=r(2776),a=r(5784);function i(e,t,r){let o=(0,a.E)(t);(0,n.useEffect)(()=>{function t(e){o.current(e)}return document.addEventListener(e,t,r),()=>document.removeEventListener(e,t,r)},[e,r])}var u=r(1530);function s(e,t,r=!0){let a=(0,n.useRef)(!1);function s(r,n){if(!a.current||r.defaultPrevented)return;let l=n(r);if(null!==l&&l.getRootNode().contains(l)&&l.isConnected){for(let t of function e(t){return"function"==typeof t?e(t()):Array.isArray(t)||t instanceof Set?t:[t]}(e)){if(null===t)continue;let e=t instanceof HTMLElement?t:t.current;if(null!=e&&e.contains(l)||r.composed&&r.composedPath().includes(e))return}return(0,o.sP)(l,o.tJ.Loose)||-1===l.tabIndex||r.preventDefault(),t(r,l)}}(0,n.useEffect)(()=>{requestAnimationFrame(()=>{a.current=r})},[r]);let c=(0,n.useRef)(null);i("pointerdown",e=>{var t,r;a.current&&(c.current=(null==(r=null==(t=e.composedPath)?void 0:t.call(e))?void 0:r[0])||e.target)},!0),i("mousedown",e=>{var t,r;a.current&&(c.current=(null==(r=null==(t=e.composedPath)?void 0:t.call(e))?void 0:r[0])||e.target)},!0),i("click",e=>{(0,l.tq)()||c.current&&(s(e,()=>c.current),c.current=null)},!0),i("touchend",e=>s(e,()=>e.target instanceof HTMLElement?e.target:null),!0),(0,u.s)("blur",e=>s(e,()=>window.document.activeElement instanceof HTMLIFrameElement?window.document.activeElement:null),!0)}},2885:(e,t,r)=>{r.d(t,{i:()=>l});var n=r(4218),o=r(5780);function l(...e){return(0,n.useMemo)(()=>(0,o.r)(...e),[...e])}},4348:(e,t,r)=>{r.d(t,{H:()=>a});var n,o=r(4218),l=r(4493);function a(){let e;let t=(e="undefined"==typeof document,"useSyncExternalStore"in(n||(n=r.t(o,2)))&&(0,(n||(n=r.t(o,2))).useSyncExternalStore)(()=>()=>{},()=>!1,()=>!e)),[a,i]=o.useState(l.O.isHandoffComplete);return a&&!1===l.O.isHandoffComplete&&i(!1),o.useEffect(()=>{!0!==a&&i(!0)},[a]),o.useEffect(()=>l.O.handoff(),[]),!t&&a}},960:(e,t,r)=>{r.d(t,{T:()=>i,h:()=>a});var n=r(4218),o=r(3266);let l=Symbol();function a(e,t=!0){return Object.assign(e,{[l]:t})}function i(...e){let t=(0,n.useRef)(e);(0,n.useEffect)(()=>{t.current=e},[e]);let r=(0,o.z)(e=>{for(let r of t.current)null!=r&&("function"==typeof r?r(e):r.current=e)});return e.every(e=>null==e||(null==e?void 0:e[l]))?void 0:r}},1530:(e,t,r)=>{r.d(t,{s:()=>l});var n=r(4218),o=r(5784);function l(e,t,r){let l=(0,o.E)(t);(0,n.useEffect)(()=>{function t(e){l.current(e)}return window.addEventListener(e,t,r),()=>window.removeEventListener(e,t,r)},[e,r])}},2698:(e,t,r)=>{r.d(t,{ZM:()=>a,oJ:()=>i,up:()=>u});var n,o=r(4218);let l=(0,o.createContext)(null);l.displayName="OpenClosedContext";var a=((n=a||{})[n.Open=1]="Open",n[n.Closed=2]="Closed",n[n.Closing=4]="Closing",n[n.Opening=8]="Opening",n);function i(){return(0,o.useContext)(l)}function u({value:e,children:t}){return o.createElement(l.Provider,{value:e},t)}},4642:(e,t,r)=>{function n(e){let t=e.parentElement,r=null;for(;t&&!(t instanceof HTMLFieldSetElement);)t instanceof HTMLLegendElement&&(r=t),t=t.parentElement;let n=(null==t?void 0:t.getAttribute("disabled"))==="";return!(n&&function(e){if(!e)return!1;let t=e.previousElementSibling;for(;null!==t;){if(t instanceof HTMLLegendElement)return!1;t=t.previousElementSibling}return!0}(r))&&n}r.d(t,{P:()=>n})},1883:(e,t,r)=>{r.d(t,{A:()=>n});function n(...e){return Array.from(new Set(e.flatMap(e=>"string"==typeof e?e.split(" "):[]))).filter(Boolean).join(" ")}},881:(e,t,r)=>{r.d(t,{k:()=>function e(){let t=[],r={addEventListener:(e,t,n,o)=>(e.addEventListener(t,n,o),r.add(()=>e.removeEventListener(t,n,o))),requestAnimationFrame(...e){let t=requestAnimationFrame(...e);return r.add(()=>cancelAnimationFrame(t))},nextFrame:(...e)=>r.requestAnimationFrame(()=>r.requestAnimationFrame(...e)),setTimeout(...e){let t=setTimeout(...e);return r.add(()=>clearTimeout(t))},microTask(...e){let t={current:!0};return(0,n.Y)(()=>{t.current&&e[0]()}),r.add(()=>{t.current=!1})},style(e,t,r){let n=e.style.getPropertyValue(t);return Object.assign(e.style,{[t]:r}),this.add(()=>{Object.assign(e.style,{[t]:n})})},group(t){let r=e();return t(r),this.add(()=>r.dispose())},add:e=>(t.push(e),()=>{let r=t.indexOf(e);if(r>=0)for(let e of t.splice(r,1))e()}),dispose(){for(let e of t.splice(0))e()}};return r}});var n=r(7051)},4493:(e,t,r)=>{r.d(t,{O:()=>i});var n=Object.defineProperty,o=(e,t,r)=>t in e?n(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,l=(e,t,r)=>(o(e,"symbol"!=typeof t?t+"":t,r),r);class a{constructor(){l(this,"current",this.detect()),l(this,"handoffState","pending"),l(this,"currentId",0)}set(e){this.current!==e&&(this.handoffState="pending",this.currentId=0,this.current=e)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return"server"===this.current}get isClient(){return"client"===this.current}detect(){return"server"}handoff(){"pending"===this.handoffState&&(this.handoffState="complete")}get isHandoffComplete(){return"complete"===this.handoffState}}let i=new a},2763:(e,t,r)=>{r.d(t,{C5:()=>w,EO:()=>T,TO:()=>f,fE:()=>m,jA:()=>M,sP:()=>g,tJ:()=>h,wI:()=>E,z2:()=>y});var n,o,l,a,i,u=r(881),s=r(9583),c=r(5780);let d=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map(e=>`${e}:not([tabindex='-1'])`).join(",");var f=((n=f||{})[n.First=1]="First",n[n.Previous=2]="Previous",n[n.Next=4]="Next",n[n.Last=8]="Last",n[n.WrapAround=16]="WrapAround",n[n.NoScroll=32]="NoScroll",n),m=((o=m||{})[o.Error=0]="Error",o[o.Overflow=1]="Overflow",o[o.Success=2]="Success",o[o.Underflow=3]="Underflow",o),p=((l=p||{})[l.Previous=-1]="Previous",l[l.Next=1]="Next",l);function v(e=document.body){return null==e?[]:Array.from(e.querySelectorAll(d)).sort((e,t)=>Math.sign((e.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}var h=((a=h||{})[a.Strict=0]="Strict",a[a.Loose=1]="Loose",a);function g(e,t=0){var r;return e!==(null==(r=(0,c.r)(e))?void 0:r.body)&&(0,s.E)(t,{0:()=>e.matches(d),1(){let t=e;for(;null!==t;){if(t.matches(d))return!0;t=t.parentElement}return!1}})}function E(e){let t=(0,c.r)(e);(0,u.k)().nextFrame(()=>{t&&!g(t.activeElement,0)&&w(e)})}var b=((i=b||{})[i.Keyboard=0]="Keyboard",i[i.Mouse=1]="Mouse",i);function w(e){null==e||e.focus({preventScroll:!0})}function y(e,t=e=>e){return e.slice().sort((e,r)=>{let n=t(e),o=t(r);if(null===n||null===o)return 0;let l=n.compareDocumentPosition(o);return l&Node.DOCUMENT_POSITION_FOLLOWING?-1:l&Node.DOCUMENT_POSITION_PRECEDING?1:0})}function T(e,t){return M(v(),t,{relativeTo:e})}function M(e,t,{sorted:r=!0,relativeTo:n=null,skipElements:o=[]}={}){var l,a,i;let u=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e.ownerDocument,s=Array.isArray(e)?r?y(e):e:v(e);o.length>0&&s.length>1&&(s=s.filter(e=>!o.includes(e))),n=null!=n?n:u.activeElement;let c=(()=>{if(5&t)return 1;if(10&t)return -1;throw Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),d=(()=>{if(1&t)return 0;if(2&t)return Math.max(0,s.indexOf(n))-1;if(4&t)return Math.max(0,s.indexOf(n))+1;if(8&t)return s.length-1;throw Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),f=32&t?{preventScroll:!0}:{},m=0,p=s.length,h;do{if(m>=p||m+p<=0)return 0;let e=d+m;if(16&t)e=(e+p)%p;else{if(e<0)return 3;if(e>=p)return 1}null==(h=s[e])||h.focus(f),m+=c}while(h!==u.activeElement);return 6&t&&null!=(i=null==(a=null==(l=h)?void 0:l.matches)?void 0:a.call(l,"textarea,input"))&&i&&h.select(),2}},9583:(e,t,r)=>{r.d(t,{E:()=>n});function n(e,t,...r){if(e in t){let n=t[e];return"function"==typeof n?n(...r):n}let o=Error(`Tried to handle "${e}" but there is no handler defined. Only defined handlers are: ${Object.keys(t).map(e=>`"${e}"`).join(", ")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(o,n),o}},7051:(e,t,r)=>{r.d(t,{Y:()=>n});function n(e){"function"==typeof queueMicrotask?queueMicrotask(e):Promise.resolve().then(e).catch(e=>setTimeout(()=>{throw e}))}},5780:(e,t,r)=>{r.d(t,{r:()=>o});var n=r(4493);function o(e){return n.O.isServer?null:e instanceof Node?e.ownerDocument:null!=e&&e.hasOwnProperty("current")&&e.current instanceof Node?e.current.ownerDocument:document}},2776:(e,t,r)=>{function n(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function o(){return n()||/Android/gi.test(window.navigator.userAgent)}r.d(t,{gn:()=>n,tq:()=>o})},3226:(e,t,r)=>{r.d(t,{AN:()=>u,l4:()=>s,sY:()=>c,yV:()=>p});var n,o,l=r(4218),a=r(1883),i=r(9583),u=((n=u||{})[n.None=0]="None",n[n.RenderStrategy=1]="RenderStrategy",n[n.Static=2]="Static",n),s=((o=s||{})[o.Unmount=0]="Unmount",o[o.Hidden=1]="Hidden",o);function c({ourProps:e,theirProps:t,slot:r,defaultTag:n,features:o,visible:l=!0,name:a,mergeRefs:u}){u=null!=u?u:f;let s=m(t,e);if(l)return d(s,r,n,a,u);let c=null!=o?o:0;if(2&c){let{static:e=!1,...t}=s;if(e)return d(t,r,n,a,u)}if(1&c){let{unmount:e=!0,...t}=s;return(0,i.E)(e?0:1,{0:()=>null,1:()=>d({...t,hidden:!0,style:{display:"none"}},r,n,a,u)})}return d(s,r,n,a,u)}function d(e,t={},r,n,o){let{as:i=r,children:u,refName:s="ref",...c}=h(e,["unmount","static"]),d=void 0!==e.ref?{[s]:e.ref}:{},f="function"==typeof u?u(t):u;"className"in c&&c.className&&"function"==typeof c.className&&(c.className=c.className(t));let p={};if(t){let e=!1,r=[];for(let[n,o]of Object.entries(t))"boolean"==typeof o&&(e=!0),!0===o&&r.push(n);e&&(p["data-headlessui-state"]=r.join(" "))}if(i===l.Fragment&&Object.keys(v(c)).length>0){if(!(0,l.isValidElement)(f)||Array.isArray(f)&&f.length>1)throw Error(['Passing props on "Fragment"!',"",`The current component <${n} /> is rendering a "Fragment".`,"However we need to passthrough the following props:",Object.keys(c).map(e=>`  - ${e}`).join(`
`),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".',"Render a single element as the child so that we can forward the props onto that element."].map(e=>`  - ${e}`).join(`
`)].join(`
`));let e=f.props,t="function"==typeof(null==e?void 0:e.className)?(...t)=>(0,a.A)(null==e?void 0:e.className(...t),c.className):(0,a.A)(null==e?void 0:e.className,c.className);return(0,l.cloneElement)(f,Object.assign({},m(f.props,v(h(c,["ref"]))),p,d,{ref:o(f.ref,d.ref)},t?{className:t}:{}))}return(0,l.createElement)(i,Object.assign({},h(c,["ref"]),i!==l.Fragment&&d,i!==l.Fragment&&p),f)}function f(...e){return e.every(e=>null==e)?void 0:t=>{for(let r of e)null!=r&&("function"==typeof r?r(t):r.current=t)}}function m(...e){if(0===e.length)return{};if(1===e.length)return e[0];let t={},r={};for(let n of e)for(let e in n)e.startsWith("on")&&"function"==typeof n[e]?(null!=r[e]||(r[e]=[]),r[e].push(n[e])):t[e]=n[e];if(t.disabled||t["aria-disabled"])return Object.assign(t,Object.fromEntries(Object.keys(r).map(e=>[e,void 0])));for(let e in r)Object.assign(t,{[e](t,...n){for(let o of r[e]){if((t instanceof Event||(null==t?void 0:t.nativeEvent)instanceof Event)&&t.defaultPrevented)return;o(t,...n)}}});return t}function p(e){var t;return Object.assign((0,l.forwardRef)(e),{displayName:null!=(t=e.displayName)?t:e.name})}function v(e){let t=Object.assign({},e);for(let e in t)void 0===t[e]&&delete t[e];return t}function h(e,t=[]){let r=Object.assign({},e);for(let e of t)e in r&&delete r[e];return r}},6095:(e,t,r)=>{r.d(t,{Z:()=>l});var n=r(4218);let o=n.forwardRef(function({title:e,titleId:t,...r},o){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"}))}),l=o},1869:(e,t,r)=>{r.d(t,{Z:()=>l});var n=r(4218);let o=n.forwardRef(function({title:e,titleId:t,...r},o){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0"}))}),l=o},4700:(e,t,r)=>{r.d(t,{Z:()=>l});var n=r(4218);let o=n.forwardRef(function({title:e,titleId:t,...r},o){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M20.25 14.15v4.25c0 1.094-.787 2.036-1.872 2.18-2.087.277-4.216.42-6.378.42s-4.291-.143-6.378-.42c-1.085-.144-1.872-1.086-1.872-2.18v-4.25m16.5 0a2.18 2.18 0 0 0 .75-1.661V8.706c0-1.081-.768-2.015-1.837-2.175a48.114 48.114 0 0 0-3.413-.387m4.5 8.006c-.194.165-.42.295-.673.38A23.978 23.978 0 0 1 12 15.75c-2.648 0-5.195-.429-7.577-1.22a2.016 2.016 0 0 1-.673-.38m0 0A2.18 2.18 0 0 1 3 12.489V8.706c0-1.081.768-2.015 1.837-2.175a48.111 48.111 0 0 1 3.413-.387m7.5 0V5.25A2.25 2.25 0 0 0 13.5 3h-3a2.25 2.25 0 0 0-2.25 2.25v.894m7.5 0a48.667 48.667 0 0 0-7.5 0M12 12.75h.008v.008H12v-.008Z"}))}),l=o},9174:(e,t,r)=>{r.d(t,{Z:()=>l});var n=r(4218);let o=n.forwardRef(function({title:e,titleId:t,...r},o){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5"}))}),l=o},7476:(e,t,r)=>{r.d(t,{Z:()=>l});var n=r(4218);let o=n.forwardRef(function({title:e,titleId:t,...r},o){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"}))}),l=o},2480:(e,t,r)=>{r.d(t,{Z:()=>l});var n=r(4218);let o=n.forwardRef(function({title:e,titleId:t,...r},o){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3-3c-1.354 0-2.694-.055-4.02-.163a2.115 2.115 0 0 1-.825-.242m9.345-8.334a2.126 2.126 0 0 0-.476-.095 48.64 48.64 0 0 0-8.048 0c-1.131.094-1.976 1.057-1.976 2.192v4.286c0 .837.46 1.58 1.155 1.951m9.345-8.334V6.637c0-1.621-1.152-3.026-2.76-3.235A48.455 48.455 0 0 0 11.25 3c-2.115 0-4.198.137-6.24.402-1.608.209-2.76 1.614-2.76 3.235v6.226c0 1.621 1.152 3.026 2.76 3.235.577.075 1.157.14 1.74.194V21l4.155-4.155"}))}),l=o},2483:(e,t,r)=>{r.d(t,{Z:()=>l});var n=r(4218);let o=n.forwardRef(function({title:e,titleId:t,...r},o){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.5 12a7.5 7.5 0 0 0 15 0m-15 0a7.5 7.5 0 1 1 15 0m-15 0H3m16.5 0H21m-1.5 0H12m-8.457 3.077 1.41-.513m14.095-5.13 1.41-.513M5.106 17.785l1.15-.964m11.49-9.642 1.149-.964M7.501 19.795l.75-1.3m7.5-12.99.75-1.3m-6.063 16.658.26-1.477m2.605-14.772.26-1.477m0 17.726-.26-1.477M10.698 4.614l-.26-1.477M16.5 19.794l-.75-1.299M7.5 4.205 12 12m6.894 5.785-1.149-.964M6.256 7.178l-1.15-.964m15.352 8.864-1.41-.513M4.954 9.435l-1.41-.514M12.002 12l-3.75 6.495"}))}),l=o},7980:(e,t,r)=>{r.d(t,{Z:()=>l});var n=r(4218);let o=n.forwardRef(function({title:e,titleId:t,...r},o){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}),l=o},7330:(e,t,r)=>{r.d(t,{Z:()=>l});var n=r(4218);let o=n.forwardRef(function({title:e,titleId:t,...r},o){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"}))}),l=o},1660:(e,t,r)=>{r.d(t,{Z:()=>l});var n=r(4218);let o=n.forwardRef(function({title:e,titleId:t,...r},o){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.752 15.002A9.72 9.72 0 0 1 18 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 0 0 3 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 0 0 9.002-5.998Z"}))}),l=o},3249:(e,t,r)=>{r.d(t,{Z:()=>l});var n=r(4218);let o=n.forwardRef(function({title:e,titleId:t,...r},o){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 3v2.25m6.364.386-1.591 1.591M21 12h-2.25m-.386 6.364-1.591-1.591M12 18.75V21m-4.773-4.227-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636M15.75 12a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0Z"}))}),l=o},4208:(e,t,r)=>{r.d(t,{Z:()=>l});var n=r(4218);let o=n.forwardRef(function({title:e,titleId:t,...r},o){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"}))}),l=o},5675:(e,t,r)=>{r.d(t,{Z:()=>l});var n=r(4218);let o=n.forwardRef(function({title:e,titleId:t,...r},o){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18 18 6M6 6l12 12"}))}),l=o},2597:(e,t,r)=>{r.d(t,{W:()=>n});function n(){for(var e,t,r=0,n="",o=arguments.length;r<o;r++)(e=arguments[r])&&(t=function e(t){var r,n,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t){if(Array.isArray(t)){var l=t.length;for(r=0;r<l;r++)t[r]&&(n=e(t[r]))&&(o&&(o+=" "),o+=n)}else for(n in t)t[n]&&(o&&(o+=" "),o+=n)}return o}(e))&&(n&&(n+=" "),n+=t);return n}}};