(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[91],{2600:function(e,t,r){Promise.resolve().then(r.bind(r,2578))},2578:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return o}});var s=r(7437),a=r(2265);let n=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"}))});var i=r(7293);let d=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 12 3.269 3.125A59.769 59.769 0 0 1 21.485 12 59.768 59.768 0 0 1 3.27 20.875L5.999 12Zm0 0h7.5"}))});var l=r(5671);let c=[{id:"1",clientId:"client1",clientName:"سارة أحمد",lastMessage:"شكراً لك على الاستشارة المفيدة",lastMessageTime:"2024-01-22 15:30",unreadCount:0,bookingId:"BK001",serviceName:"استشارة تطوير موقع إلكتروني",messages:[{id:"m1",senderId:"client1",senderName:"سارة أحمد",content:"مرحباً، أريد مناقشة متطلبات الموقع الإلكتروني",timestamp:"2024-01-22 14:00",isRead:!0},{id:"m2",senderId:"expert1",senderName:"أحمد محمد",content:"أهلاً وسهلاً، سأكون سعيداً لمساعدتك. ما هو نوع الموقع الذي تريدين إنشاءه؟",timestamp:"2024-01-22 14:05",isRead:!0},{id:"m3",senderId:"client1",senderName:"سارة أحمد",content:"شكراً لك على الاستشارة المفيدة",timestamp:"2024-01-22 15:30",isRead:!0}]},{id:"2",clientId:"client2",clientName:"محمد علي",lastMessage:"متى يمكننا بدء جلسة المراجعة؟",lastMessageTime:"2024-01-22 10:15",unreadCount:2,bookingId:"BK002",serviceName:"مراجعة كود البرمجة",messages:[{id:"m4",senderId:"client2",senderName:"محمد علي",content:"مرحباً، لدي كود React.js أريد مراجعته",timestamp:"2024-01-22 09:30",isRead:!0},{id:"m5",senderId:"client2",senderName:"محمد علي",content:"متى يمكننا بدء جلسة المراجعة؟",timestamp:"2024-01-22 10:15",isRead:!1}]},{id:"3",clientId:"client3",clientName:"نور حسن",lastMessage:"هل يمكن تأجيل الجلسة لغداً؟",lastMessageTime:"2024-01-21 16:45",unreadCount:1,bookingId:"BK003",serviceName:"تدريب على React Native",messages:[{id:"m6",senderId:"client3",senderName:"نور حسن",content:"هل يمكن تأجيل الجلسة لغداً؟",timestamp:"2024-01-21 16:45",isRead:!1}]}];function o(){let[e,t]=(0,a.useState)(c),[r,o]=(0,a.useState)(null),[m,x]=(0,a.useState)(""),[u,g]=(0,a.useState)(""),[p,f]=(0,a.useState)(!1),h=e.filter(e=>{var t;return e.clientName.toLowerCase().includes(u.toLowerCase())||(null===(t=e.serviceName)||void 0===t?void 0:t.toLowerCase().includes(u.toLowerCase()))}),y=e.reduce((e,t)=>e+t.unreadCount,0),b=e=>{o(e),e.unreadCount>0&&t(t=>t.map(t=>t.id===e.id?{...t,unreadCount:0,messages:t.messages.map(e=>({...e,isRead:!0}))}:t))},v=async()=>{if(m.trim()&&r){f(!0);try{let e={id:"m".concat(Date.now()),senderId:"expert1",senderName:"أحمد محمد",content:m.trim(),timestamp:new Date().toISOString(),isRead:!0};t(t=>t.map(t=>t.id===r.id?{...t,messages:[...t.messages,e],lastMessage:e.content,lastMessageTime:e.timestamp}:t)),o(t=>t?{...t,messages:[...t.messages,e],lastMessage:e.content,lastMessageTime:e.timestamp}:null),x("")}catch(e){console.error("Error sending message:",e)}finally{f(!1)}}},N=e=>{let t=new Date(e);return t.toLocaleTimeString("ar-SA",{hour:"2-digit",minute:"2-digit",hour12:!1})},j=e=>{let t=new Date(e),r=new Date,s=new Date(r);return(s.setDate(s.getDate()-1),t.toDateString()===r.toDateString())?"اليوم":t.toDateString()===s.toDateString()?"أمس":t.toLocaleDateString("ar-SA")};return(0,s.jsxs)("div",{className:"h-screen flex",children:[(0,s.jsxs)("div",{className:"w-1/3 bg-white dark:bg-gray-800 border-l rtl:border-l-0 rtl:border-r border-gray-200 dark:border-gray-700 flex flex-col",children:[(0,s.jsxs)("div",{className:"p-4 border-b border-gray-200 dark:border-gray-700",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)("h1",{className:"text-xl font-bold text-gray-900 dark:text-white",children:"الرسائل"}),y>0&&(0,s.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",children:[y," غير مقروءة"]})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:(0,s.jsx)(n,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)("input",{type:"text",placeholder:"البحث في المحادثات...",value:u,onChange:e=>g(e.target.value),className:"block w-full pr-10 border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"})]})]}),(0,s.jsx)("div",{className:"flex-1 overflow-y-auto",children:0===h.length?(0,s.jsx)(l.u,{icon:(0,s.jsx)(i.Z,{className:"h-12 w-12"}),title:"لا توجد محادثات",description:"لم يتم العثور على محادثات تطابق البحث"}):(0,s.jsx)("div",{className:"divide-y divide-gray-200 dark:divide-gray-700",children:h.map(e=>(0,s.jsx)("div",{onClick:()=>b(e),className:"p-4 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 ".concat((null==r?void 0:r.id)===e.id?"bg-primary-50 dark:bg-primary-900 border-l-4 rtl:border-l-0 rtl:border-r-4 border-primary-500":""),children:(0,s.jsxs)("div",{className:"flex items-center space-x-3 rtl:space-x-reverse",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)("div",{className:"h-10 w-10 rounded-full bg-primary-500 flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-sm font-medium text-white",children:e.clientName.charAt(0)})})}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-900 dark:text-white truncate",children:e.clientName}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 rtl:space-x-reverse",children:[(0,s.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:j(e.lastMessageTime)}),e.unreadCount>0&&(0,s.jsx)("span",{className:"inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-red-600 rounded-full",children:e.unreadCount})]})]}),e.serviceName&&(0,s.jsx)("p",{className:"text-xs text-primary-600 dark:text-primary-400 truncate",children:e.serviceName}),(0,s.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400 truncate",children:e.lastMessage})]})]})},e.id))})})]}),(0,s.jsx)("div",{className:"flex-1 flex flex-col",children:r?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-4",children:(0,s.jsxs)("div",{className:"flex items-center space-x-3 rtl:space-x-reverse",children:[(0,s.jsx)("div",{className:"h-10 w-10 rounded-full bg-primary-500 flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-sm font-medium text-white",children:r.clientName.charAt(0)})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-lg font-medium text-gray-900 dark:text-white",children:r.clientName}),r.serviceName&&(0,s.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:r.serviceName})]})]})}),(0,s.jsx)("div",{className:"flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50 dark:bg-gray-900",children:r.messages.map(e=>(0,s.jsx)("div",{className:"flex ".concat("expert1"===e.senderId?"justify-end":"justify-start"),children:(0,s.jsxs)("div",{className:"max-w-xs lg:max-w-md px-4 py-2 rounded-lg ".concat("expert1"===e.senderId?"bg-primary-600 text-white":"bg-white dark:bg-gray-800 text-gray-900 dark:text-white border border-gray-200 dark:border-gray-700"),children:[(0,s.jsx)("p",{className:"text-sm",children:e.content}),(0,s.jsx)("p",{className:"text-xs mt-1 ".concat("expert1"===e.senderId?"text-primary-100":"text-gray-500 dark:text-gray-400"),children:N(e.timestamp)})]})},e.id))}),(0,s.jsx)("div",{className:"bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-4",children:(0,s.jsxs)("div",{className:"flex space-x-3 rtl:space-x-reverse",children:[(0,s.jsx)("input",{type:"text",value:m,onChange:e=>x(e.target.value),onKeyPress:e=>"Enter"===e.key&&v(),placeholder:"اكتب رسالتك...",className:"flex-1 border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"}),(0,s.jsx)("button",{type:"button",onClick:v,disabled:!m.trim()||p,className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50",children:(0,s.jsx)(d,{className:"h-4 w-4"})})]})})]}):(0,s.jsx)("div",{className:"flex-1 flex items-center justify-center bg-gray-50 dark:bg-gray-900",children:(0,s.jsx)(l.u,{icon:(0,s.jsx)(i.Z,{className:"h-12 w-12"}),title:"اختر محادثة",description:"اختر محادثة من القائمة لبدء المراسلة"})})})]})}},5671:function(e,t,r){"use strict";r.d(t,{u:function(){return a}});var s=r(7437);function a(e){let{icon:t,title:r,description:a,action:n,className:i=""}=e;return(0,s.jsxs)("div",{className:"text-center py-12 ".concat(i),children:[t&&(0,s.jsx)("div",{className:"mx-auto h-12 w-12 text-gray-400 dark:text-gray-500 mb-4",children:t}),(0,s.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900 dark:text-white",children:r}),a&&(0,s.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:a}),n&&(0,s.jsx)("div",{className:"mt-6",children:(0,s.jsxs)("button",{type:"button",onClick:n.onClick,className:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500",children:[n.icon&&(0,s.jsx)("span",{className:"ml-2 h-4 w-4",children:n.icon}),n.label]})})]})}},622:function(e,t,r){"use strict";var s=r(2265),a=Symbol.for("react.element"),n=Symbol.for("react.fragment"),i=Object.prototype.hasOwnProperty,d=s.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,l={key:!0,ref:!0,__self:!0,__source:!0};function c(e,t,r){var s,n={},c=null,o=null;for(s in void 0!==r&&(c=""+r),void 0!==t.key&&(c=""+t.key),void 0!==t.ref&&(o=t.ref),t)i.call(t,s)&&!l.hasOwnProperty(s)&&(n[s]=t[s]);if(e&&e.defaultProps)for(s in t=e.defaultProps)void 0===n[s]&&(n[s]=t[s]);return{$$typeof:a,type:e,key:c,ref:o,props:n,_owner:d.current}}t.Fragment=n,t.jsx=c,t.jsxs=c},7437:function(e,t,r){"use strict";e.exports=r(622)},7293:function(e,t,r){"use strict";var s=r(2265);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 12.76c0 1.6 1.123 2.994 2.707 3.227 1.087.16 2.185.283 3.293.369V21l4.076-4.076a1.526 1.526 0 0 1 1.037-.443 48.282 48.282 0 0 0 5.68-.494c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z"}))});t.Z=a}},function(e){e.O(0,[971,472,744],function(){return e(e.s=2600)}),_N_E=e.O()}]);