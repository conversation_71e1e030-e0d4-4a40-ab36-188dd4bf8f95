(()=>{var e={};e.id=165,e.ids=[165],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},37601:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>l});var s=t(67096),a=t(16132),n=t(37284),i=t.n(n),o=t(32564),d={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);t.d(r,d);let l=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,25666)),"C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\not-found.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,62594)),"C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,16097)),"C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,25666)),"C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\not-found.tsx"]}],c=[],m="/_not-found",u={require:t,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/_not-found",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},23526:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,53579,23)),Promise.resolve().then(t.t.bind(t,30619,23)),Promise.resolve().then(t.t.bind(t,81459,23)),Promise.resolve().then(t.t.bind(t,13456,23)),Promise.resolve().then(t.t.bind(t,50847,23)),Promise.resolve().then(t.t.bind(t,57303,23))},24720:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,67490,23))},42757:(e,r,t)=>{Promise.resolve().then(t.bind(t,85068))},79533:(e,r,t)=>{Promise.resolve().then(t.bind(t,79141))},79141:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>Error});var s=t(53854),a=t(34218);function Error({error:e,reset:r}){return(0,a.useEffect)(()=>{console.error(e)},[e]),s.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8 text-center",children:[(0,s.jsxs)("div",{children:[s.jsx("h1",{className:"text-9xl font-bold text-gray-300 dark:text-gray-700",children:"500"}),s.jsx("h2",{className:"mt-6 text-3xl font-bold text-gray-900 dark:text-white",children:"حدث خطأ ما"}),s.jsx("p",{className:"mt-2 text-sm text-gray-600 dark:text-gray-400",children:"عذراً، حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى."})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[s.jsx("button",{onClick:r,className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:"المحاولة مرة أخرى"}),s.jsx("div",{children:s.jsx("a",{href:"/dashboard",className:"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700",children:"العودة إلى لوحة التحكم"})})]})]})})}},85068:(e,r,t)=>{"use strict";t.r(r),t.d(r,{Providers:()=>l});var s=t(53854),a=t(75195),n=t(55045),i=t(96644),o=t(31352),d=t(34218);function l({children:e}){let[r]=(0,d.useState)(()=>new a.S({defaultOptions:{queries:{staleTime:6e4,retry:1}}}));return s.jsx(n.aH,{client:r,children:(0,s.jsxs)(i.f,{attribute:"class",defaultTheme:"dark",enableSystem:!1,disableTransitionOnChange:!0,children:[e,s.jsx(o.x7,{position:"top-center",toastOptions:{duration:4e3,style:{background:"#1f2937",color:"#f9fafb",border:"1px solid #374151"},success:{iconTheme:{primary:"#10b981",secondary:"#f9fafb"}},error:{iconTheme:{primary:"#ef4444",secondary:"#f9fafb"}}}})]})})}},16097:(e,r,t)=>{"use strict";t.r(r),t.d(r,{$$typeof:()=>i,__esModule:()=>n,default:()=>d});var s=t(95153);let a=(0,s.createProxy)(String.raw`C:\Users\<USER>\Documents\Freela\apps\admin-dashboard\src\app\error.tsx`),{__esModule:n,$$typeof:i}=a,o=a.default,d=o},62594:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>x,metadata:()=>m,viewport:()=>u});var s=t(4656),a=t(14302),n=t.n(a),i=t(95153);let o=(0,i.createProxy)(String.raw`C:\Users\<USER>\Documents\Freela\apps\admin-dashboard\src\app\providers.tsx`),{__esModule:d,$$typeof:l}=o;o.default;let c=(0,i.createProxy)(String.raw`C:\Users\<USER>\Documents\Freela\apps\admin-dashboard\src\app\providers.tsx#Providers`);t(5023);let m={title:"Freela Syria - Admin Dashboard",description:"Administrative dashboard for Freela Syria marketplace",keywords:["freelance","syria","admin","dashboard"],authors:[{name:"Freela Syria Team"}]},u={width:"device-width",initialScale:1};function x({children:e}){return(0,s.jsxs)("html",{lang:"ar",dir:"rtl",suppressHydrationWarning:!0,children:[s.jsx("head",{children:s.jsx("link",{href:"https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap",rel:"stylesheet"})}),s.jsx("body",{className:`${n().variable} font-arabic antialiased`,children:s.jsx(c,{children:e})})]})}},25666:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i});var s=t(4656),a=t(24353),n=t.n(a);function i(){return s.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8 text-center",children:[(0,s.jsxs)("div",{children:[s.jsx("h1",{className:"text-9xl font-bold text-gray-300 dark:text-gray-700",children:"404"}),s.jsx("h2",{className:"mt-6 text-3xl font-bold text-gray-900 dark:text-white",children:"الصفحة غير موجودة"}),s.jsx("p",{className:"mt-2 text-sm text-gray-600 dark:text-gray-400",children:"عذراً، لا يمكننا العثور على الصفحة التي تبحث عنها."})]}),s.jsx("div",{children:s.jsx(n(),{href:"/dashboard",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:"العودة إلى لوحة التحكم"})})]})})}},5023:()=>{}};var r=require("../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[917],()=>t(37601));module.exports=s})();