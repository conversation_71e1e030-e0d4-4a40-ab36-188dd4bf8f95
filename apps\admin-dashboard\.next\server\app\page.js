(()=>{var e={};e.id=931,e.ids=[931],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},96266:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>f,originalPathname:()=>c,pages:()=>d,routeModule:()=>p,tree:()=>l});var n=r(67096),o=r(16132),s=r(37284),a=r.n(s),i=r(32564),u={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(u[e]=()=>i[e]);r.d(t,u);let l=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,68203)),"C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,62594)),"C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,16097)),"C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,25666)),"C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\not-found.tsx"]}],d=["C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\page.tsx"],c="/page",f={require:r,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},23526:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,53579,23)),Promise.resolve().then(r.t.bind(r,30619,23)),Promise.resolve().then(r.t.bind(r,81459,23)),Promise.resolve().then(r.t.bind(r,13456,23)),Promise.resolve().then(r.t.bind(r,50847,23)),Promise.resolve().then(r.t.bind(r,57303,23))},24720:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,67490,23))},42757:(e,t,r)=>{Promise.resolve().then(r.bind(r,85068))},79533:(e,t,r)=>{Promise.resolve().then(r.bind(r,79141))},35303:()=>{},79141:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>Error});var n=r(53854),o=r(34218);function Error({error:e,reset:t}){return(0,o.useEffect)(()=>{console.error(e)},[e]),n.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900",children:(0,n.jsxs)("div",{className:"max-w-md w-full space-y-8 text-center",children:[(0,n.jsxs)("div",{children:[n.jsx("h1",{className:"text-9xl font-bold text-gray-300 dark:text-gray-700",children:"500"}),n.jsx("h2",{className:"mt-6 text-3xl font-bold text-gray-900 dark:text-white",children:"حدث خطأ ما"}),n.jsx("p",{className:"mt-2 text-sm text-gray-600 dark:text-gray-400",children:"عذراً، حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى."})]}),(0,n.jsxs)("div",{className:"space-y-4",children:[n.jsx("button",{onClick:t,className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:"المحاولة مرة أخرى"}),n.jsx("div",{children:n.jsx("a",{href:"/dashboard",className:"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700",children:"العودة إلى لوحة التحكم"})})]})]})})}},85068:(e,t,r)=>{"use strict";r.r(t),r.d(t,{Providers:()=>l});var n=r(53854),o=r(75195),s=r(55045),a=r(96644),i=r(31352),u=r(34218);function l({children:e}){let[t]=(0,u.useState)(()=>new o.S({defaultOptions:{queries:{staleTime:6e4,retry:1}}}));return n.jsx(s.aH,{client:t,children:(0,n.jsxs)(a.f,{attribute:"class",defaultTheme:"dark",enableSystem:!1,disableTransitionOnChange:!0,children:[e,n.jsx(i.x7,{position:"top-center",toastOptions:{duration:4e3,style:{background:"#1f2937",color:"#f9fafb",border:"1px solid #374151"},success:{iconTheme:{primary:"#10b981",secondary:"#f9fafb"}},error:{iconTheme:{primary:"#ef4444",secondary:"#f9fafb"}}}})]})})}},45183:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"bailoutToClientRendering",{enumerable:!0,get:function(){return s}});let n=r(21334),o=r(25319);function s(){let e=o.staticGenerationAsyncStorage.getStore();return null!=e&&!!e.forceStatic||((null==e?void 0:e.isStaticGeneration)&&(0,n.throwWithNoSSR)(),!1)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},62918:(e,t,r)=>{"use strict";function n(e){}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"clientHookInServerComponentError",{enumerable:!0,get:function(){return n}}),r(93279),r(3542),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},33859:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return p},useSearchParams:function(){return m},usePathname:function(){return h},ServerInsertedHTMLContext:function(){return u.ServerInsertedHTMLContext},useServerInsertedHTML:function(){return u.useServerInsertedHTML},useRouter:function(){return b},useParams:function(){return x},useSelectedLayoutSegments:function(){return g},useSelectedLayoutSegment:function(){return y},redirect:function(){return l.redirect},permanentRedirect:function(){return l.permanentRedirect},RedirectType:function(){return l.RedirectType},notFound:function(){return d.notFound}});let n=r(3542),o=r(25781),s=r(18170),a=r(62918),i=r(38204),u=r(41626),l=r(18461),d=r(57082),c=Symbol("internal for urlsearchparams readonly");function f(){return Error("ReadonlyURLSearchParams cannot be modified")}class p{[Symbol.iterator](){return this[c][Symbol.iterator]()}append(){throw f()}delete(){throw f()}set(){throw f()}sort(){throw f()}constructor(e){this[c]=e,this.entries=e.entries.bind(e),this.forEach=e.forEach.bind(e),this.get=e.get.bind(e),this.getAll=e.getAll.bind(e),this.has=e.has.bind(e),this.keys=e.keys.bind(e),this.values=e.values.bind(e),this.toString=e.toString.bind(e),this.size=e.size}}function m(){(0,a.clientHookInServerComponentError)("useSearchParams");let e=(0,n.useContext)(s.SearchParamsContext),t=(0,n.useMemo)(()=>e?new p(e):null,[e]);{let{bailoutToClientRendering:e}=r(45183);e()}return t}function h(){return(0,a.clientHookInServerComponentError)("usePathname"),(0,n.useContext)(s.PathnameContext)}function b(){(0,a.clientHookInServerComponentError)("useRouter");let e=(0,n.useContext)(o.AppRouterContext);if(null===e)throw Error("invariant expected app router to be mounted");return e}function x(){(0,a.clientHookInServerComponentError)("useParams");let e=(0,n.useContext)(o.GlobalLayoutRouterContext),t=(0,n.useContext)(s.PathParamsContext);return(0,n.useMemo)(()=>(null==e?void 0:e.tree)?function e(t,r){void 0===r&&(r={});let n=t[1];for(let t of Object.values(n)){let n=t[0],o=Array.isArray(n),s=o?n[1]:n;if(!s||s.startsWith("__PAGE__"))continue;let a=o&&("c"===n[2]||"oc"===n[2]);a?r[n[0]]=n[1].split("/"):o&&(r[n[0]]=n[1]),r=e(t,r)}return r}(e.tree):t,[null==e?void 0:e.tree,t])}function g(e){void 0===e&&(e="children"),(0,a.clientHookInServerComponentError)("useSelectedLayoutSegments");let{tree:t}=(0,n.useContext)(o.LayoutRouterContext);return function e(t,r,n,o){let s;if(void 0===n&&(n=!0),void 0===o&&(o=[]),n)s=t[1][r];else{var a;let e=t[1];s=null!=(a=e.children)?a:Object.values(e)[0]}if(!s)return o;let u=s[0],l=(0,i.getSegmentValue)(u);return!l||l.startsWith("__PAGE__")?o:(o.push(l),e(s,r,!1,o))}(t,e)}function y(e){void 0===e&&(e="children"),(0,a.clientHookInServerComponentError)("useSelectedLayoutSegment");let t=g(e);return 0===t.length?null:t[0]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},57082:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{notFound:function(){return n},isNotFoundError:function(){return o}});let r="NEXT_NOT_FOUND";function n(){let e=Error(r);throw e.digest=r,e}function o(e){return(null==e?void 0:e.digest)===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},18461:(e,t,r)=>{"use strict";var n;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RedirectType:function(){return n},getRedirectError:function(){return a},redirect:function(){return i},permanentRedirect:function(){return u},isRedirectError:function(){return l},getURLFromRedirectError:function(){return d},getRedirectTypeFromError:function(){return c}});let o=r(91877),s="NEXT_REDIRECT";function a(e,t,r){void 0===r&&(r=!1);let n=Error(s);n.digest=s+";"+t+";"+e+";"+r;let a=o.requestAsyncStorage.getStore();return a&&(n.mutableCookies=a.mutableCookies),n}function i(e,t){throw void 0===t&&(t="replace"),a(e,t,!1)}function u(e,t){throw void 0===t&&(t="replace"),a(e,t,!0)}function l(e){if("string"!=typeof(null==e?void 0:e.digest))return!1;let[t,r,n,o]=e.digest.split(";",4);return t===s&&("replace"===r||"push"===r)&&"string"==typeof n&&("true"===o||"false"===o)}function d(e){return l(e)?e.digest.split(";",3)[2]:null}function c(e){if(!l(e))throw Error("Not a redirect error");return e.digest.split(";",2)[1]}(function(e){e.push="push",e.replace="replace"})(n||(n={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},38204:(e,t)=>{"use strict";function r(e){return Array.isArray(e)?e[1]:e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getSegmentValue",{enumerable:!0,get:function(){return r}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},25781:(e,t,r)=>{"use strict";e.exports=r(67096).vendored.contexts.AppRouterContext},18170:(e,t,r)=>{"use strict";e.exports=r(67096).vendored.contexts.HooksClientContext},41626:(e,t,r)=>{"use strict";e.exports=r(67096).vendored.contexts.ServerInsertedHtml},21334:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{NEXT_DYNAMIC_NO_SSR_CODE:function(){return r},throwWithNoSSR:function(){return n}});let r="NEXT_DYNAMIC_NO_SSR_CODE";function n(){let e=Error(r);throw e.digest=r,e}},41412:(e,t,r)=>{"use strict";e.exports=r(33859)},16097:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>a,__esModule:()=>s,default:()=>u});var n=r(95153);let o=(0,n.createProxy)(String.raw`C:\Users\<USER>\Documents\Freela\apps\admin-dashboard\src\app\error.tsx`),{__esModule:s,$$typeof:a}=o,i=o.default,u=i},62594:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p,metadata:()=>c,viewport:()=>f});var n=r(4656),o=r(14302),s=r.n(o),a=r(95153);let i=(0,a.createProxy)(String.raw`C:\Users\<USER>\Documents\Freela\apps\admin-dashboard\src\app\providers.tsx`),{__esModule:u,$$typeof:l}=i;i.default;let d=(0,a.createProxy)(String.raw`C:\Users\<USER>\Documents\Freela\apps\admin-dashboard\src\app\providers.tsx#Providers`);r(5023);let c={title:"Freela Syria - Admin Dashboard",description:"Administrative dashboard for Freela Syria marketplace",keywords:["freelance","syria","admin","dashboard"],authors:[{name:"Freela Syria Team"}]},f={width:"device-width",initialScale:1};function p({children:e}){return(0,n.jsxs)("html",{lang:"ar",dir:"rtl",suppressHydrationWarning:!0,children:[n.jsx("head",{children:n.jsx("link",{href:"https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap",rel:"stylesheet"})}),n.jsx("body",{className:`${s().variable} font-arabic antialiased`,children:n.jsx(d,{children:e})})]})}},25666:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var n=r(4656),o=r(24353),s=r.n(o);function a(){return n.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900",children:(0,n.jsxs)("div",{className:"max-w-md w-full space-y-8 text-center",children:[(0,n.jsxs)("div",{children:[n.jsx("h1",{className:"text-9xl font-bold text-gray-300 dark:text-gray-700",children:"404"}),n.jsx("h2",{className:"mt-6 text-3xl font-bold text-gray-900 dark:text-white",children:"الصفحة غير موجودة"}),n.jsx("p",{className:"mt-2 text-sm text-gray-600 dark:text-gray-400",children:"عذراً، لا يمكننا العثور على الصفحة التي تبحث عنها."})]}),n.jsx("div",{children:n.jsx(s(),{href:"/dashboard",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:"العودة إلى لوحة التحكم"})})]})})}},68203:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var n=r(41412);function o(){(0,n.redirect)("/dashboard")}},5023:()=>{},93279:(e,t,r)=>{"use strict";function n(e){return e&&e.__esModule?e:{default:e}}r.r(t),r.d(t,{_:()=>n,_interop_require_default:()=>n})}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[917],()=>r(96266));module.exports=n})();