# 🗄️ @freela/database

> حزمة قاعدة البيانات المشتركة لمنصة فريلا سوريا

## 📋 نظرة عامة

حزمة `@freela/database` تحتوي على مخطط قاعدة البيانات، نماذج Prisma، والوظائف المساعدة لإدارة قاعدة البيانات في منصة فريلا سوريا. تستخدم PostgreSQL مع Prisma ORM لضمان الأداء والأمان.

## ✨ الميزات الرئيسية

### 📊 مخطط قاعدة البيانات
- **User Management**: إدارة المستخدمين والأدوار
- **Expert Profiles**: ملفات الخبراء المتخصصة
- **Service Catalog**: كتالوج الخدمات والفئات
- **Booking System**: نظام الحجوزات والجدولة
- **Payment Processing**: معالجة المدفوعات والمعاملات
- **Messaging System**: نظام المراسلة والإشعارات
- **Reviews & Ratings**: التقييمات والمراجعات
- **Analytics**: بيانات التحليلات والإحصائيات

### 🔧 أدوات Prisma
- **Schema Definition**: تعريف مخطط قاعدة البيانات
- **Migration Management**: إدارة ترحيلات قاعدة البيانات
- **Type Generation**: توليد أنواع TypeScript
- **Database Seeding**: ملء قاعدة البيانات بالبيانات التجريبية

### 🛡️ الأمان والتحقق
- **Data Validation**: التحقق من صحة البيانات
- **Constraints**: قيود قاعدة البيانات
- **Indexes**: فهارس لتحسين الأداء
- **Relations**: علاقات آمنة بين الجداول

## 🛠️ المكدس التقني

### Database
- **PostgreSQL 13+**: قاعدة البيانات الرئيسية
- **Prisma ORM**: أداة إدارة قاعدة البيانات
- **TypeScript**: لغة البرمجة المكتوبة

### Tools
- **Prisma Client**: عميل قاعدة البيانات
- **Prisma Migrate**: إدارة الترحيلات
- **Prisma Studio**: واجهة إدارة قاعدة البيانات

## 🚀 التثبيت والاستخدام

### التثبيت
```bash
# في مجلد المشروع الرئيسي
npm install

# أو تثبيت الحزمة مباشرة
npm install @freela/database
```

### الاستخدام
```typescript
import { prisma } from '@freela/database';

// مثال على استخدام قاعدة البيانات
const users = await prisma.user.findMany({
  include: {
    expertProfile: true,
    clientProfile: true,
  },
});
```

## 📁 هيكل الحزمة

```
packages/database/
├── prisma/                 # ملفات Prisma
│   ├── schema.prisma      # مخطط قاعدة البيانات
│   ├── migrations/        # ترحيلات قاعدة البيانات
│   └── seed.ts           # بيانات تجريبية
├── src/                   # الكود المصدري
│   ├── index.ts          # نقطة الدخول الرئيسية
│   ├── client.ts         # عميل Prisma
│   ├── types.ts          # أنواع TypeScript
│   └── utils/            # وظائف مساعدة
│       ├── seed.ts       # وظائف البذر
│       └── helpers.ts    # وظائف مساعدة
├── dist/                 # الملفات المبنية
├── package.json          # إعدادات الحزمة
└── tsconfig.json         # إعدادات TypeScript
```

## 📊 مخطط قاعدة البيانات

### الجداول الرئيسية

#### 👤 User (المستخدمون)
```prisma
model User {
  id              String    @id @default(cuid())
  email           String    @unique
  password        String
  firstName       String
  lastName        String
  phone           String?
  avatar          String?
  role            UserRole  @default(CLIENT)
  isEmailVerified Boolean   @default(false)
  isActive        Boolean   @default(true)
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  
  // Relations
  expertProfile   ExpertProfile?
  clientProfile   ClientProfile?
  bookings        Booking[]
  reviews         Review[]
  messages        Message[]
  notifications   Notification[]
}
```

#### 👨‍💻 ExpertProfile (ملفات الخبراء)
```prisma
model ExpertProfile {
  id              String    @id @default(cuid())
  userId          String    @unique
  title           String
  bio             String?
  skills          String[]
  experience      Int       @default(0)
  hourlyRate      Decimal?
  availability    String?
  languages       String[]
  portfolio       Json?
  certifications  Json?
  rating          Decimal?  @default(0)
  totalReviews    Int       @default(0)
  totalEarnings   Decimal   @default(0)
  isVerified      Boolean   @default(false)
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  
  // Relations
  user            User      @relation(fields: [userId], references: [id])
  services        Service[]
  bookings        Booking[]
}
```

#### 🛠️ Service (الخدمات)
```prisma
model Service {
  id              String         @id @default(cuid())
  expertId        String
  categoryId      String
  title           String
  description     String
  price           Decimal
  duration        Int            // in hours
  deliveryTime    Int            // in days
  requirements    String?
  features        String[]
  images          String[]
  status          ServiceStatus  @default(PENDING)
  isActive        Boolean        @default(true)
  createdAt       DateTime       @default(now())
  updatedAt       DateTime       @updatedAt
  
  // Relations
  expert          ExpertProfile  @relation(fields: [expertId], references: [id])
  category        ServiceCategory @relation(fields: [categoryId], references: [id])
  bookings        Booking[]
  reviews         Review[]
}
```

#### 📅 Booking (الحجوزات)
```prisma
model Booking {
  id              String        @id @default(cuid())
  clientId        String
  expertId        String
  serviceId       String
  title           String
  description     String?
  budget          Decimal
  deadline        DateTime?
  status          BookingStatus @default(PENDING)
  requirements    Json?
  deliverables    Json?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  
  // Relations
  client          User          @relation(fields: [clientId], references: [id])
  expert          ExpertProfile @relation(fields: [expertId], references: [id])
  service         Service       @relation(fields: [serviceId], references: [id])
  payments        Payment[]
  messages        Message[]
  reviews         Review[]
}
```

#### 💰 Payment (المدفوعات)
```prisma
model Payment {
  id              String        @id @default(cuid())
  bookingId       String
  amount          Decimal
  currency        String        @default("USD")
  status          PaymentStatus @default(PENDING)
  method          String?
  transactionId   String?
  processedAt     DateTime?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  
  // Relations
  booking         Booking       @relation(fields: [bookingId], references: [id])
}
```

### Enums (التعدادات)

```prisma
enum UserRole {
  CLIENT
  EXPERT
  ADMIN
}

enum ServiceStatus {
  PENDING
  APPROVED
  REJECTED
  INACTIVE
}

enum BookingStatus {
  PENDING
  ACCEPTED
  IN_PROGRESS
  COMPLETED
  CANCELLED
  DISPUTED
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  REFUNDED
}
```

## 🔧 الأوامر المتاحة

### إدارة قاعدة البيانات
```bash
# توليد عميل Prisma
npm run db:generate

# تشغيل الترحيلات
npm run db:migrate

# إعادة تعيين قاعدة البيانات
npm run db:reset

# ملء قاعدة البيانات بالبيانات التجريبية
npm run db:seed

# فتح Prisma Studio
npm run db:studio
```

### التطوير
```bash
# بناء الحزمة
npm run build

# مراقبة التغييرات
npm run dev

# فحص الأنواع
npm run type-check
```

## 🌱 البيانات التجريبية (Seeding)

### تشغيل البذر
```bash
npm run db:seed
```

### البيانات المتضمنة
- **10 مستخدمين**: 5 خبراء و 5 عملاء
- **20 خدمة**: خدمات متنوعة عبر فئات مختلفة
- **15 حجز**: حجوزات بحالات مختلفة
- **8 فئات خدمات**: فئات رئيسية للخدمات
- **25 تقييم**: تقييمات ومراجعات
- **50 رسالة**: رسائل تجريبية

## 🔍 الاستعلامات الشائعة

### البحث عن الخدمات
```typescript
const services = await prisma.service.findMany({
  where: {
    status: 'APPROVED',
    isActive: true,
    title: {
      contains: searchTerm,
      mode: 'insensitive',
    },
  },
  include: {
    expert: {
      include: {
        user: true,
      },
    },
    category: true,
    reviews: true,
  },
  orderBy: {
    createdAt: 'desc',
  },
});
```

### إحصائيات الخبير
```typescript
const expertStats = await prisma.expertProfile.findUnique({
  where: { id: expertId },
  include: {
    services: {
      where: { status: 'APPROVED' },
    },
    bookings: {
      where: { status: 'COMPLETED' },
    },
    _count: {
      select: {
        services: true,
        bookings: true,
      },
    },
  },
});
```

### تحليلات المنصة
```typescript
const analytics = await prisma.$transaction([
  prisma.user.count(),
  prisma.service.count({ where: { status: 'APPROVED' } }),
  prisma.booking.count({ where: { status: 'COMPLETED' } }),
  prisma.payment.aggregate({
    _sum: { amount: true },
    where: { status: 'COMPLETED' },
  }),
]);
```

## 🧪 الاختبار

### تشغيل الاختبارات
```bash
npm test
```

### اختبارات قاعدة البيانات
- **Schema Validation**: التحقق من صحة المخطط
- **Migration Tests**: اختبار الترحيلات
- **Seed Tests**: اختبار البيانات التجريبية
- **Query Tests**: اختبار الاستعلامات

## 📞 الدعم والمساعدة

### الوثائق
- [Prisma Documentation](https://www.prisma.io/docs)
- [PostgreSQL Documentation](https://www.postgresql.org/docs)

### المساهمة
1. Fork المشروع
2. إنشاء فرع للميزة الجديدة
3. Commit التغييرات
4. Push إلى الفرع
5. فتح Pull Request

---

**🎯 حزمة قاعدة بيانات شاملة وآمنة لمنصة فريلا سوريا**

*آخر تحديث: ديسمبر 2024*
