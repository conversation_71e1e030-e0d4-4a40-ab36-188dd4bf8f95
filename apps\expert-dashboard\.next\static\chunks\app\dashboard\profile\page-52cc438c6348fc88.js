(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[544],{7465:function(e,t,r){Promise.resolve().then(r.bind(r,1441))},1441:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return h}});var a=r(7437),s=r(2265);let l=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6.827 6.175A2.31 2.31 0 0 1 5.186 7.23c-.38.054-.757.112-1.134.175C2.999 7.58 2.25 8.507 2.25 9.574V18a2.25 2.25 0 0 0 2.25 2.25h15A2.25 2.25 0 0 0 21.75 18V9.574c0-1.067-.75-1.994-1.802-2.169a47.865 47.865 0 0 0-1.134-.175 2.31 2.31 0 0 1-1.64-1.055l-.822-1.316a2.192 2.192 0 0 0-1.736-1.039 48.774 48.774 0 0 0-5.232 0 2.192 2.192 0 0 0-1.736 1.039l-.821 1.316Z"}),s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M16.5 12.75a4.5 4.5 0 1 1-9 0 4.5 4.5 0 0 1 9 0ZM18.75 10.5h.008v.008h-.008V10.5Z"}))}),i=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z"}))});var n=r(5907);let d=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}),s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z"}))});var c=r(6233),o=r(3339),m=r(7822);let x=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.26 10.147a60.438 60.438 0 0 0-.491 6.347A48.62 48.62 0 0 1 12 20.904a48.62 48.62 0 0 1 8.232-4.41 60.46 60.46 0 0 0-.491-6.347m-15.482 0a50.636 50.636 0 0 0-2.658-.813A59.906 59.906 0 0 1 12 3.493a59.903 59.903 0 0 1 10.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0A50.717 50.717 0 0 1 12 13.489a50.702 50.702 0 0 1 7.74-3.342M6.75 15a.75.75 0 1 0 0-1.5.75.75 0 0 0 0 1.5Zm0 0v-3.675A55.378 55.378 0 0 1 12 8.443m-7.007 11.55A5.981 5.981 0 0 0 6.75 15.75v-1.5"}))}),g={id:"1",name:"أحمد محمد",title:"مطور ويب متخصص في React و Node.js",bio:"مطور ويب محترف مع أكثر من 5 سنوات من الخبرة في تطوير تطبيقات الويب الحديثة باستخدام React، Node.js، وقواعد البيانات المختلفة. أتخصص في بناء حلول تقنية مبتكرة وسهلة الاستخدام.",email:"<EMAIL>",phone:"+963 123 456 789",location:"دمشق، سوريا",languages:["العربية","الإنجليزية"],skills:["React","Node.js","TypeScript","PostgreSQL","MongoDB","AWS"],experience:5,education:"بكالوريوس هندسة معلوماتية - جامعة دمشق",certifications:["AWS Certified Developer","React Professional Certificate"],hourlyRate:25,availability:"متاح للعمل",verified:!0,rating:4.8,completedProjects:47,joinDate:"2023-01-15"};function h(){let[e,t]=(0,s.useState)(g),[r,h]=(0,s.useState)(!1),[u,y]=(0,s.useState)(g);return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"sm:flex sm:items-center sm:justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"الملف الشخصي"}),(0,a.jsx)("p",{className:"mt-2 text-sm text-gray-700 dark:text-gray-300",children:"إدارة معلوماتك الشخصية ومهاراتك وخبراتك"})]}),(0,a.jsx)("div",{className:"mt-4 sm:mt-0",children:r?(0,a.jsxs)("div",{className:"flex space-x-3 rtl:space-x-reverse",children:[(0,a.jsx)("button",{onClick:()=>{y(e),h(!1)},className:"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600",children:"إلغاء"}),(0,a.jsx)("button",{onClick:()=>{t(u),h(!1)},className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700",children:"حفظ التغييرات"})]}):(0,a.jsx)("button",{onClick:()=>h(!0),className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700",children:"تعديل الملف الشخصي"})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsx)("div",{className:"lg:col-span-1",children:(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg p-6",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"relative inline-block",children:[(0,a.jsx)("div",{className:"h-32 w-32 rounded-full bg-primary-500 flex items-center justify-center mx-auto",children:(0,a.jsx)("span",{className:"text-4xl font-medium text-white",children:e.name.charAt(0)})}),r&&(0,a.jsx)("button",{className:"absolute bottom-0 right-0 bg-white dark:bg-gray-700 rounded-full p-2 shadow-lg border border-gray-200 dark:border-gray-600",children:(0,a.jsx)(l,{className:"h-4 w-4 text-gray-600 dark:text-gray-400"})})]}),(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white flex items-center justify-center",children:[e.name,e.verified&&(0,a.jsx)(i,{className:"h-5 w-5 text-blue-500 mr-2"})]}),(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400 mt-1",children:e.title})]}),(0,a.jsxs)("div",{className:"mt-6 grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center",children:[(0,a.jsx)(n.Z,{className:"h-4 w-4 text-yellow-400"}),(0,a.jsx)("span",{className:"mr-1 text-lg font-semibold text-gray-900 dark:text-white",children:e.rating})]}),(0,a.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"التقييم"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:e.completedProjects}),(0,a.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"مشروع مكتمل"})]})]}),(0,a.jsxs)("div",{className:"mt-6 space-y-3 text-sm",children:[(0,a.jsxs)("div",{className:"flex items-center text-gray-600 dark:text-gray-400",children:[(0,a.jsx)(d,{className:"h-4 w-4 ml-2"}),e.location]}),(0,a.jsxs)("div",{className:"flex items-center text-gray-600 dark:text-gray-400",children:[(0,a.jsx)(c.Z,{className:"h-4 w-4 ml-2"}),"انضم في ",e.joinDate]}),(0,a.jsxs)("div",{className:"flex items-center text-gray-600 dark:text-gray-400",children:[(0,a.jsx)(o.Z,{className:"h-4 w-4 ml-2"}),e.experience," سنوات خبرة"]})]})]})})}),(0,a.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg p-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center",children:[(0,a.jsx)(m.Z,{className:"h-5 w-5 ml-2"}),"المعلومات الأساسية"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"الاسم الكامل"}),r?(0,a.jsx)("input",{type:"text",value:u.name,onChange:e=>y({...u,name:e.target.value}),className:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"}):(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-900 dark:text-white",children:e.name})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"البريد الإلكتروني"}),r?(0,a.jsx)("input",{type:"email",value:u.email,onChange:e=>y({...u,email:e.target.value}),className:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"}):(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-900 dark:text-white",children:e.email})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"رقم الهاتف"}),r?(0,a.jsx)("input",{type:"tel",value:u.phone,onChange:e=>y({...u,phone:e.target.value}),className:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"}):(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-900 dark:text-white",children:e.phone})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"الموقع"}),r?(0,a.jsx)("input",{type:"text",value:u.location,onChange:e=>y({...u,location:e.target.value}),className:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"}):(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-900 dark:text-white",children:e.location})]}),(0,a.jsxs)("div",{className:"md:col-span-2",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"العنوان المهني"}),r?(0,a.jsx)("input",{type:"text",value:u.title,onChange:e=>y({...u,title:e.target.value}),className:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"}):(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-900 dark:text-white",children:e.title})]}),(0,a.jsxs)("div",{className:"md:col-span-2",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"نبذة عني"}),r?(0,a.jsx)("textarea",{rows:4,value:u.bio,onChange:e=>y({...u,bio:e.target.value}),className:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"}):(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-900 dark:text-white",children:e.bio})]})]})]}),(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg p-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center",children:[(0,a.jsx)(x,{className:"h-5 w-5 ml-2"}),"المعلومات المهنية"]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"المهارات"}),(0,a.jsx)("div",{className:"mt-2 flex flex-wrap gap-2",children:e.skills.map((e,t)=>(0,a.jsx)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-300",children:e},t))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"اللغات"}),(0,a.jsx)("div",{className:"mt-2 flex flex-wrap gap-2",children:e.languages.map((e,t)=>(0,a.jsx)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",children:e},t))})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"سنوات الخبرة"}),(0,a.jsxs)("p",{className:"mt-1 text-sm text-gray-900 dark:text-white",children:[e.experience," سنوات"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"السعر بالساعة"}),(0,a.jsxs)("p",{className:"mt-1 text-sm text-gray-900 dark:text-white",children:["$",e.hourlyRate,"/ساعة"]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"التعليم"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-900 dark:text-white",children:e.education})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"الشهادات"}),(0,a.jsx)("ul",{className:"mt-2 space-y-1",children:e.certifications.map((e,t)=>(0,a.jsxs)("li",{className:"text-sm text-gray-900 dark:text-white",children:["• ",e]},t))})]})]})]})]})]})]})}},622:function(e,t,r){"use strict";var a=r(2265),s=Symbol.for("react.element"),l=Symbol.for("react.fragment"),i=Object.prototype.hasOwnProperty,n=a.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,d={key:!0,ref:!0,__self:!0,__source:!0};function c(e,t,r){var a,l={},c=null,o=null;for(a in void 0!==r&&(c=""+r),void 0!==t.key&&(c=""+t.key),void 0!==t.ref&&(o=t.ref),t)i.call(t,a)&&!d.hasOwnProperty(a)&&(l[a]=t[a]);if(e&&e.defaultProps)for(a in t=e.defaultProps)void 0===l[a]&&(l[a]=t[a]);return{$$typeof:s,type:e,key:c,ref:o,props:l,_owner:n.current}}t.Fragment=l,t.jsx=c,t.jsxs=c},7437:function(e,t,r){"use strict";e.exports=r(622)},3339:function(e,t,r){"use strict";var a=r(2265);let s=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M20.25 14.15v4.25c0 1.094-.787 2.036-1.872 2.18-2.087.277-4.216.42-6.378.42s-4.291-.143-6.378-.42c-1.085-.144-1.872-1.086-1.872-2.18v-4.25m16.5 0a2.18 2.18 0 0 0 .75-1.661V8.706c0-1.081-.768-2.015-1.837-2.175a48.114 48.114 0 0 0-3.413-.387m4.5 8.006c-.194.165-.42.295-.673.38A23.978 23.978 0 0 1 12 15.75c-2.648 0-5.195-.429-7.577-1.22a2.016 2.016 0 0 1-.673-.38m0 0A2.18 2.18 0 0 1 3 12.489V8.706c0-1.081.768-2.015 1.837-2.175a48.111 48.111 0 0 1 3.413-.387m7.5 0V5.25A2.25 2.25 0 0 0 13.5 3h-3a2.25 2.25 0 0 0-2.25 2.25v.894m7.5 0a48.667 48.667 0 0 0-7.5 0M12 12.75h.008v.008H12v-.008Z"}))});t.Z=s},6233:function(e,t,r){"use strict";var a=r(2265);let s=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5"}))});t.Z=s},5907:function(e,t,r){"use strict";var a=r(2265);let s=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M11.48 3.499a.562.562 0 0 1 1.04 0l2.125 5.111a.563.563 0 0 0 .475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 0 0-.182.557l1.285 5.385a.562.562 0 0 1-.84.61l-4.725-2.885a.562.562 0 0 0-.586 0L6.982 20.54a.562.562 0 0 1-.84-.61l1.285-5.386a.562.562 0 0 0-.182-.557l-4.204-3.602a.562.562 0 0 1 .321-.988l5.518-.442a.563.563 0 0 0 .475-.345L11.48 3.5Z"}))});t.Z=s},7822:function(e,t,r){"use strict";var a=r(2265);let s=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"}))});t.Z=s}},function(e){e.O(0,[971,472,744],function(){return e(e.s=7465)}),_N_E=e.O()}]);